{"name": "skote-angular-vertical", "version": "1.0.0", "scripts": {"ng": "ng", "start": "ng serve", "start:local": "ng serve --configuration=local", "build-lise": "ng build --configuration=ec", "build-inter": "ng build --configuration=production", "build:dev": "ng build --configuration=dev", "install-build": "npm install --legacy-peer-deps && ng build --configuration=production --outputHashing=all", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@agm/core": "^1.1.0", "@angular/animations": "^10.2.5", "@angular/common": "~10.0.2", "@angular/compiler": "~10.0.2", "@angular/core": "~10.0.2", "@angular/fire": "^5.4.2", "@angular/forms": "~10.0.2", "@angular/localize": "^10.0.2", "@angular/platform-browser": "~10.0.2", "@angular/platform-browser-dynamic": "~10.0.2", "@angular/router": "~10.0.2", "@ckeditor/ckeditor5-angular": "^1.1.2", "@ckeditor/ckeditor5-build-classic": "^16.0.0", "@fullcalendar/angular": "^4.4.5-beta", "@fullcalendar/bootstrap": "^4.4.0", "@fullcalendar/core": "^4.4.0", "@fullcalendar/daygrid": "^4.4.0", "@fullcalendar/interaction": "^4.4.0", "@fullcalendar/list": "^4.4.0", "@fullcalendar/timegrid": "^4.4.0", "@ng-bootstrap/ng-bootstrap": "^8.0.0", "@ng-select/ng-select": "^3.7.2", "@ngx-translate/core": "^12.1.2", "@ngx-translate/http-loader": "^5.0.0", "@nicky-lenaers/ngx-scroll-to": "^3.0.1", "@swimlane/ngx-datatable": "^17.1.0", "@types/chartist": "^0.9.47", "angular-archwizard": "^5.0.0", "apexcharts": "^3.15.3", "bootstrap": "^4.6.1", "bs-custom-file-input": "^1.3.4", "chart.js": "^2.9.3", "chartist": "^0.11.4", "echarts": "^4.6.0", "firebase": "^7.14.0", "metismenujs": "^1.1.0", "ng-apexcharts": "^1.2.0", "ng-chartist": "^4.1.0", "ng-click-outside": "^5.1.1", "ng-multiselect-dropdown": "^0.2.10", "ng2-charts": "^2.4.3", "ng2-search-filter": "^0.5.1", "ng5-slider": "^1.2.4", "ngx-chartist": "^1.0.3", "ngx-color-picker": "^8.2.0", "ngx-cookie-service": "^3.0.4", "ngx-drag-drop": "^2.0.0", "ngx-dropzone": "^2.2.2", "ngx-dropzone-wrapper": "^8.0.0", "ngx-echarts": "^4.2.2", "ngx-image-cropper": "^3.0.3", "ngx-mask": "^8.1.7", "ngx-owl-carousel-o": "^3.0.0", "ngx-perfect-scrollbar": "^9.0.0", "ngx-scrollbar": "^7.2.0", "ngx-spinner": "^10.0.1", "ngx-ui-switch": "^8.3.0", "primeicons": "^5.0.0", "rxjs": "~6.5.4", "sass-loader": "^8.0.2", "sweetalert2": "^9.7.1", "tslib": "^2.0.0", "xlsx": "^0.18.5", "zone.js": "~0.10.2"}, "devDependencies": {"@angular-devkit/build-angular": "^0.1000.1", "@angular/cdk": "^11.2.13", "@angular/cli": "^10.0.1", "@angular/compiler-cli": "~10.0.2", "@angular/language-service": "~10.0.2", "@types/echarts": "^4.4.3", "@types/jasmine": "~3.3.8", "@types/jasminewd2": "~2.0.3", "@types/node": "^12.11.1", "codelyzer": "^5.1.2", "jasmine-core": "~3.5.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~5.0.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~3.3.0", "karma-jasmine-html-reporter": "^1.5.0", "primeng": "^11.4.2", "protractor": "~7.0.0", "ts-node": "~7.0.0", "tslint": "~6.1.0", "typescript": "~3.9.5"}}