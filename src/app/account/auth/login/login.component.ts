import { Component, OnInit, AfterViewInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';

import { AuthenticationService } from '../../../core/services/auth.service';
import { LoginService } from '../../../core/services/login.service';

import { AuthfakeauthenticationService } from '../../../core/services/authfake.service';

import { ActivatedRoute, Router } from '@angular/router';
import { first } from 'rxjs/operators';

import { environment } from '../../../../environments/environment';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss']
})

/**
 * Login component
 */
export class LoginComponent implements OnInit, AfterViewInit {

  appUrl = environment.appUrl;
  loginForm: FormGroup;
  submitted = false;
  error :any;
  returnUrl: string;

  // set the currenr year
  year: number = new Date().getFullYear();

  // tslint:disable-next-line: max-line-length
  constructor(private formBuilder: FormBuilder, private route: ActivatedRoute, private router: Router, private authenticationService: AuthenticationService,
    private authFackservice: AuthfakeauthenticationService, private loginService: LoginService) { }

  ngOnInit() {
    this.loginForm = this.formBuilder.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required]],
    });

    // reset login status
    // this.authenticationService.logout();
    // get return url from route parameters or default to '/'
    // tslint:disable-next-line: no-string-literal
    this.returnUrl = '/dashboard';
  }

  ngAfterViewInit() {
  }

  // convenience getter for easy access to form fields
  get f() { return this.loginForm.controls; }

  /**
   * Form submit
   */
  onSubmit() {
    this.submitted = true;

    // stop here if form is invalid
    if (this.loginForm.invalid) {

      return;
    } else {

      this.authFackservice.login(this.f.email.value, this.f.password.value).subscribe(
        data => {
          if (data["user"] && data["user"]["team"] && data["user"]["team"]["id"]) {


          } else {
            this.router.navigate(['dashboard']);
            this.router.navigateByUrl('dashboard');
          }

        },
        error => {
          this.error = error ? error : '';
        });


      /*
  if (environment.defaultauth === 'firebase') {
    this.authenticationService.login(this.f.email.value, this.f.password.value).then((res: any) => {
      this.router.navigate(['/dashboard']);
    })
      .catch(error => {
        this.error = error ? error : '';
      });
  } else {
    this.authFackservice.login(this.f.email.value, this.f.password.value)
      .pipe(first())
      .subscribe(
        data => {
          this.router.navigate(['/dashboard']);
        },
        error => {
          this.error = error ? error : '';
        });
  }
  */


    }
  }
}
