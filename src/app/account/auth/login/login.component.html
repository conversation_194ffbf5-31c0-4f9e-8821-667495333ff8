<div class="home-btn d-none d-sm-block">
  <a href="/" class="text-dark"><i class="fas fa-home h2"></i></a>
</div>
<div class="account-pages mt-5 mb-5">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-md-8 col-lg-6 col-xl-5">
        <div class="card overflow-hidden">
          <div class="bg-soft-primary">
            <div class="row">
              <div class="col-9">
                <div class="text-primary p-4">
                  <h5 class="text-primary">Te<PERSON>r hoşgeldiniz</h5>
                  <p>EC Panel kullanıcı bilgilerinizi giriniz.</p>
                </div>
              </div>
              <div class="col-3 align-self-end my-2">
                <img style="height: 100px" src="assets/images/EC_LOGO.png" alt="" class="img-fluid">
              </div>
            </div>
          </div>
          <div class="card-body pt-0">
            <div>
              <a href="/">
                <div class="avatar-md profile-user-wid mb-4">
                  <span class="avatar-title rounded-circle bg-light">
                    <img src="assets/images/logo2.png" alt="" class="rounded-circle" height="90">
                  </span>
                </div>
              </a>
            </div>

            <div class="p-2">
              <form class="form-horizontal" [formGroup]="loginForm" (ngSubmit)="onSubmit()">

                <ngb-alert type="danger" *ngIf="error" [dismissible]="false">{{ error?.error?.message|| error.statusText  }}</ngb-alert>

                <div class="form-group mb-3">
                  <label for="email">Email</label>

                  <input type="email" formControlName="email" class="form-control" id="email" placeholder="Email"
                    [ngClass]="{ 'is-invalid': submitted && f.email.errors }" />
                  <div *ngIf="submitted && f.email.errors" class="invalid-feedback">
                    <div *ngIf="f.email.errors.required">Email is required</div>
                    <div *ngIf="f.email.errors.email">Email must be a valid email address</div>
                  </div>
                </div>

                <div class="form-group mb-3">
                  <label for="password">Password</label>

                  <input type="password" formControlName="password" class="form-control" id="password"
                    [ngClass]="{ 'is-invalid': submitted && f.password.errors }" placeholder="Password" />
                  <div *ngIf="submitted && f.password.errors" class="invalid-feedback">
                    <div *ngIf="f.password.errors.required">Password is required</div>
                  </div>
                </div>

                <div class="custom-control custom-checkbox">
                  <input type="checkbox" class="custom-control-input" id="customControlInline">
                  <label class="custom-control-label" for="customControlInline">Remember me</label>
                </div>

                <div class="mt-3">
                  <button class="btn btn-primary btn-block" type="submit">Log In</button>
                </div>
              </form>
            </div>

          </div>
        </div>
        <div class="mt-5 text-center">

<!--          <p>© {{year}} Rastmobile.com Crafted with <i class="mdi mdi-heart text-danger"></i> by <a href="https://rastmobile.com" target="_blank">RastMobile.com</a> </p>-->
        </div>

      </div>
    </div>
  </div>

  <!-- end container -->
</div>
<!-- end page -->
