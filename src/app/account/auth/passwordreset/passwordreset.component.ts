import { Component, OnInit, AfterViewInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';

import { AuthenticationService } from '../../../core/services/auth.service';
import { environment } from '../../../../environments/environment';
import { LoginService } from 'src/app/core/services/login.service';
import { NgxSpinnerService } from 'ngx-spinner';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-passwordreset',
  templateUrl: './passwordreset.component.html',
  styleUrls: ['./passwordreset.component.scss']
})

/**
 * Reset-password component
 */
export class PasswordresetComponent implements OnInit, AfterViewInit {

  resetForm: FormGroup;
  submitted = false;
  error = '';
  success = '';
  loading = false;
  passwordForm: FormGroup;
  breadCrumbItems = Array({});
  // set the currenr year
  year: number = new Date().getFullYear();

  // tslint:disable-next-line: max-line-length
  constructor(
    private formBuilder: FormBuilder,
    private route: ActivatedRoute, private router: Router,
    private authenticationService: AuthenticationService,
    private loginService: LoginService,
    private spinner: NgxSpinnerService
    ) { }

  ngOnInit() {

    this.breadCrumbItems = [{ label: 'User' }, { label: 'Edit Password', active: true }];
    this.resetForm = this.formBuilder.group({
      email: ['', [Validators.required, Validators.email]],
    });

    this.passwordForm = this.formBuilder.group({
      password: ['', Validators.required],
      password_confirmation: ['', Validators.required]
    })
  }

  ngAfterViewInit() {
  }

  // convenience getter for easy access to form fields
  get f() { return this.resetForm.controls; }

  /**
   * On submit form
   */
  onSubmit() {
    this.success = '';
    this.submitted = true;

    // stop here if form is invalid
    if (this.resetForm.invalid) {
      return;
    }
    if (environment.defaultauth === 'firebase') {
      this.authenticationService.resetPassword(this.f.email.value)
        .catch(error => {
          this.error = error ? error : '';
        });
    }
  }

  updatePassword(){
    if(this.passwordForm.valid){
      const newPassword = this.passwordForm.value;
      this.spinner.show();
      this.loginService.updatePassword(newPassword).subscribe((response: any) => {
        this.spinner.hide();
        this.position('Password Reset Successfuly', 'Reset', 'Ok', 'success');
      }, error => {
        this.error = error.error['errors']['password'];
        this.spinner.hide();
      })
    }else{
      this.error = "Invalid Form"
    }
  }

  position(title, text, confirmText, icon, dismissAll = true) {
    return Swal.fire({
      title: title,
      html: text,
      icon: icon,
      confirmButtonColor: '#34c38f',
      cancelButtonColor: '#f46a6a',
      confirmButtonText: confirmText,
    }).then(result => {
      if (result.value && dismissAll) {
        this.router.navigate(['dashboard']);
      }
    });
  }
}
