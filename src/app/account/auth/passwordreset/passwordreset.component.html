<div class="container-fluid">
  <app-page-title [title]="'reset_password._update_password' |translate" [breadCrumbItems]="breadCrumbItems"></app-page-title>

  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body" *ngIf="passwordForm">
          <form [formGroup]="passwordForm">
            <ngb-alert type="danger" *ngIf="error" [dismissible]="false">{{ error }}</ngb-alert>
            <div class="form-group row">
              <label for="password" class="col-md-2 col-form-label">{{ 'reset_password._password' |translate}}</label>
              <div class="col-md-10">
                <input class="form-control" [placeholder]="'reset_password._type_new_password' |translate" type="password" value="" id="password"
                       formControlName="password">
              </div>
            </div>

            <div class="form-group row">
              <label for="password_confirmation" class="col-md-2 col-form-label">{{ 'reset_password._password_confirmation' |translate}}</label>
              <div class="col-md-10">
                <input class="form-control" type="password" value="" id="password_confirmation" [placeholder]="'reset_password._type_new_password_confirmation' |translate"
                       formControlName="password_confirmation">
              </div>
            </div>
            <div class="row">
              <div class="col-12">
                <div class="text-center my-3">

                  <a (click)="updatePassword()" class="btn btn-success inner">
                    {{'reset_password._update_password' |translate}} </a>
                </div>
              </div> <!-- end col-->
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
<ngx-spinner type="ball-clip-rotate-multiple" size="medium"> </ngx-spinner>

<!-- <div class="home-btn d-none d-sm-block">
  <a href="/" class="text-dark"><i class="fas fa-home h2"></i></a>
</div>
<div class="account-pages my-5 pt-sm-5">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-md-8 col-lg-6 col-xl-5">
        <div class="card overflow-hidden">
          <div class="bg-soft-primary">
            <div class="row">
              <div class="col-7">
                <div class="text-primary p-4">
                  <h5 class="text-primary"> Reset Password</h5>
                  <p>Re-Password with Tubitak</p>
                </div>
              </div>
              <div class="col-5 align-self-end">
                <img src="assets/images/profile-img.png" alt="" class="img-fluid">
              </div>
            </div>
          </div>
          <div class="card-body pt-0">
            <div>
              <a href="/">
                <div class="avatar-md profile-user-wid mb-4">
                  <span class="avatar-title rounded-circle bg-light">
                    <img src="assets/images/logo.png" alt="" class="rounded-circle" height="90">
                  </span>
                </div>
              </a>
            </div>

            <form class="needs-validation" name="resetForm" [formGroup]="resetForm" (ngSubmit)="onSubmit()" novalidate>

              <ngb-alert type="danger" *ngIf="error" [dismissible]="false">{{ error }}</ngb-alert>

              <div class="form-group mb-3">
                <label for="email">Email</label>

                <input type="email" formControlName="email" class="form-control"
                  [ngClass]="{ 'is-invalid': submitted && f.email.errors }" id="email" placeholder="Email" />

                <div *ngIf="submitted && f.email.errors" class="invalid-feedback">
                  <div *ngIf="f.email.errors.required">Email is required</div>
                  <div *ngIf="f.email.errors.email">Email must be a valid email address</div>
                </div>
              </div>
              <div class="form-group mb-0 row">
                <div class="col-12 text-right">
                  <button class="btn btn-primary w-md" type="submit">Reset</button>
                </div>
              </div>
            </form>

          </div>
        </div>
        <div class="mt-5 text-center">
          <p>Remember It ? <a routerLink="/account/login" class="font-weight-medium text-primary"> Sign In here</a> </p>
          <p>© {{year}} Rastmobile.com Crafted with <i class="mdi mdi-heart text-danger"></i> by <a href="https://rastmobile.com" target="_blank">RastMobile.com</a> </p>
        </div>

      </div>
    </div>
  </div>
</div> -->
