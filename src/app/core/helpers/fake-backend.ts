import { Injectable } from '@angular/core';
import { HttpRequest, HttpResponse, HttpHandler, HttpEvent, HttpInterceptor, HTTP_INTERCEPTORS } from '@angular/common/http';
import { Observable, of, throwError } from 'rxjs';
import { delay, mergeMap, materialize, dematerialize } from 'rxjs/operators';

@Injectable()
export class FakeBackendInterceptor implements HttpInterceptor {

    constructor() { }

    intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {

        const headers = {} as any;
        const token = localStorage.getItem("token");
        if (!request.headers.has('Authorization') && token) {
            headers['Authorization'] = `Bearer ${token}`;
        }

        if (!request.headers.has('Content-Type') && request.headers.get('accept') !== 'application/json') {
            headers['Content-Type'] = 'application/json';
        }

        return next.handle(
            request.clone({
                setHeaders: headers,
            }),
        );
    }
}
