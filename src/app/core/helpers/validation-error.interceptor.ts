import { Injectable } from '@angular/core';
import { <PERSON>ttp<PERSON>vent, HttpHandler, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import Swal from 'sweetalert2';
import { NgxSpinnerService } from 'ngx-spinner';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
@Injectable()
export class ValidationErrorInterceptor implements HttpInterceptor {
    constructor(
      private spinner: NgxSpinnerService,
      private modalService: NgbModal
      ) { }
  
    intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
        return next.handle(request).pipe(catchError(err => {
            if(err.status === 422){
              this.showValidationError(err);
            }
            const error = err || err.statusText;
            return throwError(error);
        }));
    }

    showValidationError(error) {
        let message = error.error.message;
        if (message === 'validation.error') {
          message = Object.values(error.error.errors).join('\n<br>');
        }
        this.spinner.hide();
        this.errorModal('Validation Error', message);
    }
    
    errorModal(title , errorText){
        Swal.fire({
          title: title,
          html: errorText,
          icon: 'error',
          confirmButtonColor: '#34c38f',
          cancelButtonColor: '#f46a6a',
          confirmButtonText: 'Ok',
        }).then(result => {
          //window.history.back();
          if (result.value) {
            //this.modalService.dismissAll();
          }
        });
    }
}
