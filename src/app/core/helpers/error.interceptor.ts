import { Injectable } from '@angular/core';
import { <PERSON>ttp<PERSON><PERSON>, <PERSON>ttpH<PERSON><PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { AuthfakeauthenticationService } from '../services/authfake.service';
import Swal from 'sweetalert2';
import { NgxSpinnerService } from 'ngx-spinner';

@Injectable()
export class ErrorInterceptor implements HttpInterceptor {
  constructor(private authenticationService: AuthfakeauthenticationService, private spinner: NgxSpinnerService) { }

  intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    return next.handle(request).pipe(catchError(err => {
      this.spinner.hide();
      if (err.status === 401) {
        // auto logout if 401 response returned from api
        this.authenticationService?.logout();
        if (request.headers.get('Authorization')) {
          location.reload();
        }
      }
      if(err.status === 500){
        if(err['error']['message'] === 'This action is unauthorized.')
          this.errorModal('Unauthorized' ,err['error']['message']);
        else
          this.errorModal('Server Error' ,err['error']['message']);
      }

      // if(err.status === 422){
      //   if(err['error']['message'] === 'validation.error'){
      //     this.errorModal('Validation Error' ,Object.values(err['error']['errors']).join('\n<br>'));
      //   }
      // }

      const error = err || err.statusText;
      return throwError(error);
    }));
  }

  errorModal(title , errorText){
    Swal.fire({
      title: title,
      html: errorText,
      icon: 'error',
      confirmButtonColor: '#34c38f',
      cancelButtonColor: '#f46a6a',
      confirmButtonText: 'Ok',
    }).then(result => {
      //window.history.back();
    });
  }
}
