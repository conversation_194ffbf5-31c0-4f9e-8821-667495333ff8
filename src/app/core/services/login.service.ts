import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { User } from '../models/auth.models';
import { BehaviorSubject, Observable } from 'rxjs';
import { map } from 'rxjs/internal/operators/map';
import { Router } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class LoginService {

  apiUrl = environment.apiUrl;
  appUrl = environment.appUrl;

  private currentUserSubject: BehaviorSubject<User>;
  public currentUser: Observable<User>;

  constructor(private http: HttpClient, private router: Router) {
    this.currentUserSubject = new BehaviorSubject<User>(JSON.parse(localStorage.getItem('currentUser')));
    this.currentUser = this.currentUserSubject.asObservable();
  }

  public get currentUserValue(): User {
    return this.currentUserSubject.value;
  }


  login(email: string, password: string) {
    return this.http.post<any>(`${this.apiUrl}login`, { email, password })
      .pipe(map(user => {
        // login successful if there's a jwt token in the response

        if (user && user.token) {
          let userData = JSON.stringify(user);
          localStorage.setItem('currentUser', userData);
          this.currentUserSubject.next(user);
          // window.location.href = `${this.appUrl}`;
          window.location.href = `/`;

        }
        return user;
      }));
  }

  updatePassword(body){
    return this.http.patch(`${this.apiUrl}user`, body);
  }
}
