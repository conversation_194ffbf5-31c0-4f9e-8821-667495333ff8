import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class StatisticsService {

  apiUrl = environment.apiUrl;

  constructor(private httpClient: HttpClient) { }

  getStatistics() {
    return this.httpClient.get(`${this.apiUrl}evaluation-stats`);
  }

  exportStatistics() {
    return this.httpClient.get(`${this.apiUrl}evaluation-stats-export`, {
      responseType: 'arraybuffer',
    });
  }

}
