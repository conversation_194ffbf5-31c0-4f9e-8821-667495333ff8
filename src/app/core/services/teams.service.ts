import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class TeamsService {

  apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) { }

  getTeams(obj) {
    let params = new URLSearchParams();
    for (let key in obj) {
      if (obj[key]) {
        params.append(key, obj[key]);
      }
    }
    return this.http.get(`${this.apiUrl}team?${params}`);
  }

  getTeam(id: number) {
    return this.http.get(`${this.apiUrl}team/${id}`);
  }

  getMyTeam() {
    return this.http.get(`${this.apiUrl}my-team`);
  }

  createTeam(obj) {
    return this.http.post(`${this.apiUrl}team`, obj);
  }


  updateTeam(obj, teamId) {
    return this.http.patch(`${this.apiUrl}team/${teamId}`, obj);
  }

  myTeamAutoVehicleNumber(obj ={}) {
    return this.http.put(`${this.apiUrl}my-team/vehicle-number`, obj);
  }

  autoVehicleNumberForAdmin(teamId){
    return this.http.put(`${this.apiUrl}team/${teamId}/vehicle-number`, {});
  }

  updateMyTeam(obj) {
    return this.http.patch(`${this.apiUrl}my-team`, obj);
  }

  importTeam(obj) {
    const headers = new HttpHeaders({
      Accept: 'application/json',
    });
    return this.http.post(`${this.apiUrl}team/import`, obj, { headers });
  }

  attachFormsAdd(obj) {
    return this.http.post(`${this.apiUrl}form`, obj);
  }

  attachFormsUpdate(obj) {
    return this.http.patch(`${this.apiUrl}form/${obj.id}`, obj);
  }

  attachFile(obj, teamId) {
    return this.http.post(`${this.apiUrl}team/${teamId}/attach`, obj);
  }

  addPromotion(obj) {
    return this.http.put(`${this.apiUrl}promotion`, obj);
  }

  getPromotion() {
    return this.http.get(`${this.apiUrl}promotion`);
  }

  listPromotion() {
    return this.http.get(`${this.apiUrl}promotion`);
  }

  deletePromotion(promotionId) {
    return this.http.delete(`${this.apiUrl}promotion/${promotionId}`);
  }

  deleteTeamMember(memberId) {
    return this.http.delete(`${this.apiUrl}member/${memberId}`);
  }

  exportPromotions(teamId) {
    return this.http.get(`${this.apiUrl}promotion-export/${teamId}`, {
      responseType: 'arraybuffer',
    });

  }

  exportMembers(teamId) {
    return this.http.get(`${this.apiUrl}member-team-export/${teamId}`, {
      responseType: 'arraybuffer',
    });
  }

  getAllMembers(limit = 20) {
    return this.http.get(`${this.apiUrl}member?limit=${limit}`);
  }

  exportPenalties(teamId) {
    return this.http.get(`${this.apiUrl}penalty-team-export/${teamId}`, {
      responseType: 'arraybuffer',
    });

  }

  exportAllPenalty() {
    return this.http.get(`${this.apiUrl}penalty-export`, {
      responseType: 'arraybuffer',
    });
  }

  clearSeason() {
    return this.http.get(`${this.apiUrl}admin/clear`);
  }

  teamExport() {
    return this.http.get(`${this.apiUrl}team-export`, {
      responseType: 'arraybuffer',
    });
  }

  approveTeam(teamId) {
    return this.http.put(`${this.apiUrl}team/${teamId}/approve`, {});
  }

  penaltys() {
    return this.http.get(`${this.apiUrl}penalty`);
  }

  penalty(teamId) {
    return this.http.get(`${this.apiUrl}penalty/${teamId}`);
  }

  addPenalty(obj) {
    return this.http.post(`${this.apiUrl}penalty`, obj);
  }

  updatePenalty(obj, penaltyId) {
    return this.http.post(`${this.apiUrl}penalty/${penaltyId}`, obj);
  }

  addMember(obj) {
    return this.http.post(`${this.apiUrl}member`, obj);
  }

  updateMember(obj, memberId) {
    return this.http.patch(`${this.apiUrl}member/${memberId}`, obj);
  }

  exportAllMembers() {
    return this.http.get(`${this.apiUrl}member-export`, {
      responseType: 'arraybuffer',
    });
  }

  deletePenalty(penaltyId) {
    return this.http.delete(`${this.apiUrl}penalty/${penaltyId}`);
  }

  getStats() {
    return this.http.get(`${this.apiUrl}stats`);
  }

  getTeamsHasMultiAppointmentInADay() {
    return this.http.get(`${this.apiUrl}stats-has-appointment`);
  }

  checkMyTeam(){
    return this.http.get(`${this.apiUrl}my-team-check`);
  }

  checkTeamByAdmin(teamId){
    return this.http.get(`${this.apiUrl}team/${teamId}/check`);
  }

  exportQrCodes(teamId) {
    return this.http.get(`${this.apiUrl}member-qr-export/${teamId}`, {
      responseType: 'arraybuffer',
    });
  }

}
