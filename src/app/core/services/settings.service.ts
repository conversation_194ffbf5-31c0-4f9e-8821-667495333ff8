import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { HttpClient, HttpHeaders } from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export class SettingsService {

  apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) { }

  getSettings() {
    return this.http.get(`${environment.apiUrl}admin/settings`);
  }

  updateSettings(obj) {
    return this.http.post(`${environment.apiUrl}admin/settings`, obj);
  }

  updateCriteria(obj) {
    return this.http.post(`${environment.apiUrl}admin/criteria`, obj);
  }

  putCriteria(obj) {
    return this.http.put(`${environment.apiUrl}admin/criteria`, obj);
  }


  patchCriteria(obj, criteriaId) {
    return this.http.patch(`${environment.apiUrl}admin/criteria/${criteriaId}`, obj);
  }


  getCriterias(obj) {

    let params = new URLSearchParams();
    for (let key in obj) {
      if (obj[key]) {
        params.append(key, obj[key]);
      }
    }
    return this.http.get(`${environment.apiUrl}admin/criteria?${params}`);
  }

  getCriteria(criteriaId) {
    return this.http.get(`${environment.apiUrl}admin/criteria/${criteriaId}`);
  }

  importCriteria(obj){
    const headers = new HttpHeaders({
      Accept: 'application/json',
    });
    return this.http.post(`${environment.apiUrl}admin/import-criteria`, obj, { headers });
  }
}
