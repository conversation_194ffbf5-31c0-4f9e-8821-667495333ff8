import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class AssetService {

  apiUrl = environment.apiUrl;
  
  constructor(private http: HttpClient) { }


  
  upload(obj) {
    const headers = new HttpHeaders({
      Accept: 'application/json',
    });
    return this.http.post(`${this.apiUrl}asset`, obj, { headers });
  }
}
