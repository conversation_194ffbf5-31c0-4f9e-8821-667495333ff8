import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { HttpClient } from '@angular/common/http';

@Injectable({
  providedIn: 'root',
})
export class AppointmentService {


  apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) { }


  randomAppointment(obj) {
    return this.http.post(`${this.apiUrl}appointment/auto`, obj);
  }

  getAppointments(obj) {
    let params = new URLSearchParams();
    for (let key in obj) {
      if (obj[key]) {
        params.append(key, obj[key]);
      }
    }
    return this.http.get(`${this.apiUrl}appointment?${params}`);
  }

  createAppointment(obj) {
    return this.http.post(`${this.apiUrl}appointment`, obj);
  }

  removeAppointment(teamId) {
    return this.http.delete(`${this.apiUrl}appointment/${teamId}`);
  }

  getAppointment(teamId) {
    return this.http.get(`${this.apiUrl}appointment/${teamId}`);
  }

  getTeamAppointments(teamId, status: string = 'active') {
    return this.http.get(`${this.apiUrl}appointment?team_id=${teamId}&status=${status}`);
  }

  deleteAppointment(id) {
    return this.http.delete(`${this.apiUrl}appointment/${id}`);
  }

  patchAppointment(id, obj) {
    return this.http.patch(`${this.apiUrl}appointment/${id}`, obj);
  }

  joinAppointment(id, obj) {
    return this.http.patch(`${this.apiUrl}appointment/${id}`, obj);
  }

  getNotJoinedAppointment() {
    return this.http.get(`${this.apiUrl}stats-appointment`);
  }
}
