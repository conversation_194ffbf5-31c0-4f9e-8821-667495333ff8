import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class AdminService {

  apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) { }


  getUser(userId) {
    return this.http.get(`${environment.apiUrl}admin/user/${userId}`);
  }

  getUsers(obj) {
    let params = new URLSearchParams();
    for (let key in obj) {
      if (obj[key]) {
        params.append(key, obj[key]);
      }
    }
    return this.http.get(`${this.apiUrl}admin/user?${params}`);
  }

  createUser(obj) {
    return this.http.post(`${environment.apiUrl}admin/user`, obj);
  }

  updateUser(obj, userId) {
    return this.http.patch(`${environment.apiUrl}admin/user/${userId}`, obj);
  }

  getAllRoles(){
    return this.http.get(`${environment.apiUrl}admin/user-roles`).pipe(map(res => res['roles']));
  }


}
