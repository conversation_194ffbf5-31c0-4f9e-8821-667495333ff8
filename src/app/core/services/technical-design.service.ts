import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class TechnicalDesignService {

  apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) { }

  importTechnicalDesign(obj) {
    const headers = new HttpHeaders({
      Accept: 'application/json',
    });
    return this.http.post(`${this.apiUrl}technical-design-import`, obj, { headers });
  }
  
  getTechnicalDesigns(){
    return this.http.get(`${this.apiUrl}technical-design`);
  }

  updateTechnicalDesigns(obj, teamId = null) {
    return this.http.patch(`${this.apiUrl}technical-design/${teamId? teamId: obj.id}`, obj);
  }

  getTechnicalDesign(teamId){
    return this.http.get(`${this.apiUrl}technical-design/${teamId}`);
  }

  exportTechnicalDesigns() {
    return this.http.get(`${this.apiUrl}technical-design-export`, {
      responseType: 'arraybuffer'
    });  
  }
}
