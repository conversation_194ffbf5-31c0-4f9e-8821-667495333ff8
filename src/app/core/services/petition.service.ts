import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';

import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})

export class PetitionService {

  apiUrl = environment.apiUrl;

  constructor(
    private httpClient: HttpClient
  ) {}

  getAllPetitions() {
    return this.httpClient.get(`${this.apiUrl}petition`);
  }

  getPetition(id: number) {
    return this.httpClient.get(`${this.apiUrl}petition/${id}`);
  }

  addPetition(obj) {
    return this.httpClient.post(`${this.apiUrl}petition`, obj);
  }

  addCommentToPetition(petitionId: number, comment) {
    const body = {
      comment
    };
    return this.httpClient.put(`${this.apiUrl}petition/${petitionId}/comment`, body);
  }

  petitionApprove(petitionId: number) {
    return this.httpClient.put(`${this.apiUrl}petition/${petitionId}/like`, '');
  }

  petitionReject(petitionId: number) {
    return this.httpClient.put(`${this.apiUrl}petition/${petitionId}/dislike`, '');
  }

  petitionHesitant(petitionId: number) {
    return this.httpClient.put(`${this.apiUrl}petition/${petitionId}/hesitant`, null);
  }

  //Henüz kullanılmadı
  updatePetition(petitionId: number, obj) {
    return this.httpClient.patch(`${this.apiUrl}petition/${petitionId}`, obj);
  }

  deletePetition(petitionId: number) {
    return this.httpClient.delete(`${this.apiUrl}petition/${petitionId}`);
  }

  commentLike(commentId: number) {
    return this.httpClient.put(` ${this.apiUrl}comment/${commentId}/like`, '');
  }

  commentDislike(commentId: number) {
    return this.httpClient.put(` ${this.apiUrl}comment/${commentId}/dislike`, '');
  }

  commentDelete(commentId: number) {
    return this.httpClient.delete(` ${this.apiUrl}comment/${commentId}`);
  }
}
