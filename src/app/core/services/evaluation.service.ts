import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { of } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class EvaluationService {

  apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) { }

  getCriterias() {
    return this.http.get(`${this.apiUrl}evaluation`);
  }

  getEvalList(obj) {
    let params = new URLSearchParams();
    for (let key in obj) {
      if (obj[key]) {
        params.append(key, obj[key]);
      }
    }
    return this.http.get(`${this.apiUrl}evaluation?${params}`);
  }

  getPublicEvalList(obj) {
    let params = new URLSearchParams();
    for (let key in obj) {
      if (obj[key]) {
        params.append(key, obj[key]);
      }
    }
    // return of();
    return this.http.get(`${this.apiUrl}team-evaluations?${params}`);
  }

  getEvaluations(id) {
    return this.http.get(`${this.apiUrl}evaluation/${id}`);
  }
  getPublicEvaluations(id) {
    return this.http.get(`${this.apiUrl}team-evaluation/${id}`);
  }

  exportEval(evalId) {
    return this.http.get(`${this.apiUrl}evaluation/${evalId}/export`, {
      responseType: 'arraybuffer',
    });
  }

  startEvaluation(obj) {
    return this.http.post(`${this.apiUrl}evaluation`, obj);
  }

  saveEval(obj) {
    return this.http.patch(`${this.apiUrl}evaluation-detail/${obj.id}`, obj);
  }

  closeEval(teamId) {
    return this.http.put(`${this.apiUrl}evaluation/${teamId}/close`, {});
  }

  openEval(teamId) {
    return this.http.put(`${this.apiUrl}evaluation/${teamId}/reopen`, {});
  }

  updateStatus(obj, teamId) {
    return this.http.patch(`${this.apiUrl}evaluation/${teamId}`, obj);
  }

  giveSticker(evaluationId) {
    return this.http.put(`${this.apiUrl}evaluation/${evaluationId}/sticker`, null);
  }

  checkSticker(evaluationId) {
    return this.http.put(`${this.apiUrl}evaluation/${evaluationId}/check`, null);
  }

  exportStatus() {
    return this.http.get(`${this.apiUrl}evaluation-status-export`, {
      responseType: 'arraybuffer',
    });
  }

  getDdkList() {
    return this.http.get(`${this.apiUrl}evaluation-ddl-list`);
  }

  startEvaluationStatsData(filter: string = null) {
    if (filter) {
      return this.http.get(`${this.apiUrl}evaluation-stats-data?filter=${filter}`);
    }
    return this.http.get(`${this.apiUrl}evaluation-stats-data`);
  }

  getEvaluationGroupedStats() {
    return this.http.get(`${this.apiUrl}evaluation-get-grouped`);
  }

  getSingleBaseGrouped(filter: string = null) {
    return this.http.get(`${this.apiUrl}evaluation-get-single-base-grouped?filter=${filter}`);
  }
}
