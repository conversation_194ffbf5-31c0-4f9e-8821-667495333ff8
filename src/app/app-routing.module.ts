import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { AuthGuard } from './core/guards/auth.guard';
import { LayoutComponent } from './layouts/layout.component';
import { ReportsComponent } from './pages/reports/reports/reports.component';

const routes: Routes = [
  {
    path: '',
    loadChildren: () => import('./pages/home/<USER>').then(m => m.HomeModule)
  },
  {
    path: 'tuketim-tablosu',
    loadChildren: () => import('./pages/public-reports/public-reports.module').then(m => m.PublicReportsModule)
  },
  {
    path: 'yaris-sonuclari',
    component: ReportsComponent,
    canActivate: [AuthGuard]
  },
  {
    path: 'account',
    loadChildren: () => import('./account/auth/auth.module').then(m => m.AuthModule),
    canActivate: [AuthGuard]
  },
  // tslint:disable-next-line: max-line-length
  {
    path: '',
    component: LayoutComponent,
    loadChildren: () => import('./pages/pages.module').then(m => m.PagesModule),
    canActivate: [AuthGuard],
  },
  {
    path: '',
    loadChildren: () => import('./pages/inspection-detail/inspection-detail.module').then(m => m.InspectionDetailModule),
    canActivate: [AuthGuard],
  },
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, {
      scrollPositionRestoration: 'top',
      useHash: true,
      enableTracing: false
    })
  ],
  exports: [RouterModule],
})

export class AppRoutingModule {}

// canActivate: [AuthGuard]
