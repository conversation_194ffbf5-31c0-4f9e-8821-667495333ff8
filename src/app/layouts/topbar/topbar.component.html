<header id="page-topbar">
  <div class="navbar-header">
    <div class="d-flex">
      <!-- LOGO -->
      <div class="navbar-brand-box">
        <a href="/" class="logo logo-dark">
          <span class="logo-sm">
            <img src="assets/images/logo.png" alt="" height="22" />
          </span>
          <span class="logo-lg">
            <img src="assets/images/logo-dark.png" alt="" height="17" />
          </span>
        </a>

        <a href="/" class="logo logo-light" style="height: 40px">
          <span class="logo-sm">
            <img src="assets/images/logo-small.fw.png" alt="" height="22"/>
          </span>
          <span class="logo-lg">
            <img
              src="assets/images/logo-small.fw.png"
              alt=""
              width="93"
              height="70"
            />
          </span>
        </a>
      </div>
      <button
        type="button"
        class="btn btn-sm px-3 font-size-16 header-item"
        id="vertical-menu-btn"
        (click)="toggleMobileMenu($event)"
      >
        <i class="fa fa-fw fa-bars"></i>
      </button>
    </div>
    <img
      _ngcontent-iau-c133=""
      src="assets/images/logo-light.png"
      alt=""
      height="40"
    />
    <div class="d-flex">
      <div class="dropdown d-inline-block d-lg-none ml-2" ngbDropdown>
        <button
          type="button"
          class="btn header-item noti-icon d-flex align-items-center justify-content-center"
          id="page-header-search-dropdown"
          data-toggle="dropdown"
          aria-haspopup="true"
          ngbDropdownToggle
          aria-expanded="false"
        >
          <i class="mdi mdi-magnify"></i>
        </button>
      </div>

      <div class="dropdown d-none d-lg-inline-block ml-1">
        <button
          type="button"
          class="btn header-item noti-icon"
          data-toggle="fullscreen"
          (click)="fullscreen()"
        >
          <i class="bx bx-fullscreen"></i>
        </button>
        <button
          *ngIf="loggedRole"
          type="button"
          class="btn header-item noti-icon"
          data-toggle="fullscreen"
          (click)="clearSeason()"
        >
          <i class="bx bxs-eraser"></i>
        </button>
      </div>

      <div class="dropdown d-inline-block" ngbDropdown>
        <button
          type="button"
          class="btn header-item d-flex align-items-center justify-content-center"
          ngbDropdownToggle
          id="page-header-user-dropdown"
        >
          <img
            class="rounded-circle header-profile-user"
            src="assets/images/users/avatar-2.jpg"
            alt="Header Avatar"
          />
          <span class="d-none d-xl-inline-block ml-1">{{ loggedUser }}</span>
          <i class="mdi mdi-chevron-down d-none d-xl-inline-block"></i>
        </button>
        <div class="dropdown-menu dropdown-menu-right" ngbDropdownMenu>
          <!-- item-->

          <p-selectButton
            [options]="languages"
            styleClass="dropdown-item"
            [(ngModel)]="selectedLanguage"
            (onChange)="changeLanguage($event.value)"
            optionValue="code"
            optionLabel="name"
          ></p-selectButton>

          <div class="dropdown-divider"></div>
          <a class="dropdown-item" href="javascript: void(0);" routerLink="reset-password"
            ><i class="bx bx-user font-size-16 align-middle mr-1"></i>
            {{ "HEADER.LOGIN.PROFILE" | translate }}</a
          >

          <div class="dropdown-divider"></div>
          <a
            class="dropdown-item text-danger"
            href="javascript: void(0);"
            (click)="logout()"
            ><i
              class="bx bx-power-off font-size-16 align-middle mr-1 text-danger"
            ></i>
            {{ "HEADER.LOGIN.LOGOUT" | translate }}</a
          >
        </div>
      </div>
    </div>
  </div>
</header>
