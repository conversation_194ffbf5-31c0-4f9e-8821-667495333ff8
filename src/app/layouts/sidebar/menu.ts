import { MenuItem } from './menu.model';

export const MENU: MenuItem[] = [
  {
    id: 1,
    label: 'MENUITEMS.MENU.TEXT',
    isTitle: true,
  },
  {
    id: 2,
    label: 'MENUITEMS.DASHBOARDS.TEXT',
    icon: 'bx-home-circle',
    link: '/dashboard',
  },
  {
    id: 3,
    label: 'MENUITEMS.TEAMS.TEXT',
    icon: 'bx-user-circle',
    link: '/team',
  },
  {
    id: 4,
    label: 'MENUITEMS.APPOINTMENT.TEXT',
    icon: 'bx-envelope',
    link: '/appointment',
  },
  {
    id: 5,
    label: 'MENUITEMS.EVALUATION.TEXT',
    icon: 'bx-receipt',
    link: '/evaluation',
  },
  {
    id: 6,
    label: 'MENUITEMS.RACE.TEXT',
    icon: 'bx-map',
    link: '/race',
  },
  {
    id: 7,
    label: 'MENUITEMS.TECHNICALDESIGN.TEXT',
    icon: 'bx bxs-pyramid',
    link: '/technical-design',
  },
  {
    id: 8,
    label: 'MENUITEMS.REPORTS.TEXT',
    icon: 'bx-share-alt',
    link: '/reports',
  },
  {
    id: 9,
    label: 'MENUITEMS.STATISTICS.TEXT',
    icon: 'bx bx-analyse',
    link: '/statistics',
  },
  {
    id: 10,
    label: 'MENUITEMS.PETITION.TEXT',
    icon: 'bx bx-notepad',
    link: '/petitions'
  },
  {
    id: 14,
    label: 'MENUITEMS.EVALUATION-RESULTS.TEXT',
    icon: 'bx bx-notepad',
    link: '/evaluation-results',
    blank: true
  },
  {
    id: 15,
    label: 'MENUITEMS.REPORTS-DETAIL.TEXT',
    icon: 'bx-detail',
    link: '/reports-detail',
  },
  {
    id: 16,
    label: 'MENUITEMS.CONSUMPTION.TEXT',
    icon: 'bx bx-notepad',
    link: '/tuketim-tablosu',
    blank: true
  },
  {
    id: 11,
    label: 'MENUITEMS.ADMIN.TEXT',
    isTitle: true,
  },
  {
    id: 12,
    label: 'MENUITEMS.MANAGEMENT.TEXT',
    icon: 'bx-list-ul',
    link: '/user',
  },
  {
    id: 13,
    label: 'MENUITEMS.SETTINGS.TEXT',
    icon: 'bx-file',
    subItems: [
      {
        id: 48,
        label: 'MENUITEMS.SETTINGS.LIST.Technical',
        link: '/criteria',
        parentId: 47,
      },
      {
        id: 48,
        label: 'MENUITEMS.SETTINGS.LIST.Seasons',
        link: '/settings/season',
        parentId: 47,
      },
    ],
  },
];
export const DASH: MenuItem[] = [
  {
    id: 1,
    label: 'MENUITEMS.MENU.TEXT',
    isTitle: true,
  }, {
    id: 2,
    label: 'MENUITEMS.DASHBOARDS.TEXT',
    icon: 'bx-home-circle',
    link: '/dashboard',
  },
];

export const TEAM: MenuItem[] = [
  {
    id: 3,
    label: 'MENUITEMS.TEAMS.TEXT',
    icon: 'bx-user-circle',
    link: '/team',
  },
];
export const TEAM_CAPTAIN: MenuItem[] = [
];
export const APPOINTMENT: MenuItem[] = [
  {
    id: 4,
    label: 'MENUITEMS.APPOINTMENT.TEXT',
    icon: 'bx-envelope',
    link: '/appointment',
  },
];
export const EVALUATION: MenuItem[] = [
  {
    id: 3,
    label: 'MENUITEMS.TEAMS.TEXT',
    icon: 'bx-user-circle',
    link: '/team',
  },
  {
    id: 4,
    label: 'MENUITEMS.APPOINTMENT.TEXT',
    icon: 'bx-envelope',
    link: '/appointment',
  },
  {
    id: 5,
    label: 'MENUITEMS.EVALUATION.TEXT',
    icon: 'bx-receipt',
    link: '/evaluation',
  },
  {
    id: 7,
    label: 'MENUITEMS.TECHNICALDESIGN.TEXT',
    icon: 'bx bxs-pyramid',
    link: '/technical-design',
  },
  {
    id: 9,
    label: 'MENUITEMS.STATISTICS.TEXT',
    icon: 'bx bx-analyse',
    link: '/statistics',
  },
  {
    id: 10,
    label: 'MENUITEMS.PETITION.TEXT',
    icon: 'bx bx-notepad',
    link: '/petitions'
  },
  {
    id: 11,
    label: 'MENUITEMS.EVALUATION-RESULTS.TEXT',
    icon: 'bx bx-notepad',
    link: '/evaluation-results',
    blank: true
  },
];
export const GUIDE: MenuItem[] = [
  {
    id: 3,
    label: 'MENUITEMS.TEAMS.TEXT',
    icon: 'bx-user-circle',
    link: '/team',
  },
  {
    id: 4,
    label: 'MENUITEMS.APPOINTMENT.TEXT',
    icon: 'bx-envelope',
    link: '/appointment',
  },
  {
    id: 5,
    label: 'MENUITEMS.EVALUATION.TEXT',
    icon: 'bx-receipt',
    link: '/evaluation',
  },
  {
    id: 10,
    label: 'MENUITEMS.PETITION.TEXT',
    icon: 'bx bx-notepad',
    link: '/petitions'
  },
];
