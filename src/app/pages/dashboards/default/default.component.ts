import { Component, OnInit } from '@angular/core';
import { TeamsService } from '../../../core/services/teams.service';
import { AppointmentService } from '../../../core/services/appointment.service';
import { EvaluationService } from '../../../core/services/evaluation.service';
import { EvaluationStatusEnum } from '../../evaluation/evaluation-status.enum';
import { ChartType } from './../dashboard.model';
import { emailSentBarChart } from 'src/app/pages/dashboards/data';

import { Router } from '@angular/router';

@Component({
  selector: 'app-default',
  templateUrl: './default.component.html',
  styleUrls: ['./default.component.scss'],
})
export class DefaultComponent implements OnInit {

  // bread crumb items


  simplePieChart: ChartType = {
    chart: {
      height: 320,
      type: 'pie',
    },
    series: [],
    labels: ['Team Count', 'Appointment Joined', 'Eval Success At Once', 'Sticker Ready'],
    colors: ['#34c38f', '#556ee6', '#f46a6a', '#50a5f1', '#f1b44c'],
    legend: {
      show: true,
      position: 'bottom',
      horizontalAlign: 'center',
      verticalAlign: 'middle',
      floating: false,
      fontSize: '14px',
      offsetX: 0,
      offsetY: -10,
    },
    responsive: [{
      breakpoint: 600,
      options: {
        chart: {
          height: 240,
        },
        legend: {
          show: false,
        },
      },
    }],
  };


  basicRadialBarChart: ChartType = {
    chart: {
      height: 460,
      type: 'radialBar',
    },
    plotOptions: {
      radialBar: {
        dataLabels: {
          name: {
            fontSize: '62px',
          },
          value: {
            fontSize: '50px',
          },
          total: {
            show: true,
            label: 'Total',
            formatter: (w) => {
              // tslint:disable-next-line: max-line-length
              // By default this function returns the average of all series. The below is just an example to show the use of custom formatter function
              return this.totalTeamCount();
            },
          },
        },
      },
    },
    colors: ['#556ee6', '#34c38f', '#f46a6a', '#f1b44c'],
    series: [],
    labels: ['Team Count', 'Appointment Joined', 'Eval Success At Once', 'Sticker Ready'],


  };

  emailSentBarChart: ChartType;
  breadCrumbItems: Array<{}>;
  leaderBoard;
  stats: {
    team_count: number,
    eval_success_at_once: number,
    appointment_joined: number,
    sticker_ready: number,
    progress_eval_success_at_once?: number,
    progress_appointment_joined?: number,
    progress_sticker_ready?: number
  } = {
    team_count: 0,
    eval_success_at_once: 0,
    appointment_joined: 0,
    sticker_ready: 0,
  };
  hasMultiAppointmentTeams: any;
  notJoinedAppointment: any;
  evalList: any;
  currentUser;
  teamId = 0;

  constructor(
    private teamService: TeamsService,
    private appointmentService: AppointmentService,
    private evaluationService: EvaluationService,
    private router: Router,
  ) {
    this.emailSentBarChart = emailSentBarChart;
    this.currentUser = JSON.parse(localStorage.getItem('currentUser'));
    if (this.currentUser['user'] && this.currentUser['user']['team'] && this.currentUser['user']['team']['id']) {
      this.teamId = this.currentUser['user']['team']['id'];
      this.router.navigate(['/team/detail', this.teamId]);
    }
  }


  ngOnInit() {
    this.breadCrumbItems = [{ label: '' }];

    if (!this.teamId) {
      this.getStats();
      this.getHasMultiAppointmentTeams();
      this.getTeamsWhereNotJoined();

      if (this.currentUser.user.role?.includes('evaluation') || this.currentUser.user.role?.includes('superadmin')) {
        this.getEvalStatus();
      }
    }
  }

  chartReady: boolean = false;

  getStats() {
    this.teamService.getStats().subscribe((data: any) => {
      this.stats = data;

      if (this.stats.team_count != 0) {
        this.stats = Object.assign(this.stats, {
          progress_eval_success_at_once: this.stats.sticker_ready ?
            100 * this.stats.eval_success_at_once / this.stats.sticker_ready : 0,
          progress_appointment_joined: 100 * this.stats.appointment_joined / this.stats.team_count,
          progress_sticker_ready: 100 * this.stats.sticker_ready / this.stats.team_count,
        });
      }


      this.simplePieChart['series'].push(this.stats.team_count);
      this.simplePieChart['series'].push(this.stats.appointment_joined);
      this.simplePieChart['series'].push(this.stats.eval_success_at_once);
      this.simplePieChart['series'].push(Math.round(this.stats.sticker_ready));

      this.basicRadialBarChart['series'].push(this.stats.team_count);
      this.basicRadialBarChart['series'].push(this.stats.appointment_joined);
      this.basicRadialBarChart['series'].push(this.stats.eval_success_at_once);
      this.basicRadialBarChart['series'].push(Math.round(this.stats.sticker_ready));

      this.chartReady = true;
    });
  }

  totalTeamCount() {
    return this.stats.team_count;
  }

  getHasMultiAppointmentTeams() {
    this.teamService.getTeamsHasMultiAppointmentInADay().subscribe((data: any) => {
      this.hasMultiAppointmentTeams = data.data;
    });
  }

  getTeamsWhereNotJoined() {
    this.appointmentService.getNotJoinedAppointment().subscribe((data: any) => {
      this.notJoinedAppointment = data.data;
    });
  }

  getEvalStatus() {
    this.evaluationService.getEvalList({}).subscribe((data: any) => {
      this.evalList = data.data.sort(function(a, b) {
        const nameA = a.status;
        const nameB = b.status;
        if (nameA > nameB) {
          return -1;
        }
        if (nameA < nameB) {
          return 1;
        }
        return 0;
      });
    });
  }


  evalClass(status) {
    switch (status) {
      case EvaluationStatusEnum.ACTIVE:
        return 'row-color-active';
      case EvaluationStatusEnum.CLOSED:
        return 'row-color-close';
      case EvaluationStatusEnum.FAILED:
        return 'row-color-failed';
      case EvaluationStatusEnum.SUCCESS:
        return 'row-color-success';
    }
  }

  exportEvalStatus() {
    this.evaluationService.exportStatus().subscribe(data => {
      this.downLoadFile(data, 'application/ms-excel', 'evaluations_status.xlsx');
    });
  }


  downLoadFile(data: any, type: string, filename: string) {
    const blob = new Blob([data], { type: type });

    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.click();
  }

}
