<script src="../../../app.component.ts"></script>
<script src="default.component.ts"></script>
<div class="container-fluid">
  <app-page-title title="DASHBOARD" [breadCrumbItems]="breadCrumbItems"></app-page-title>
  <div class="row" *ngIf="chartReady">
    <!--  <div class="col-xl-6">
      <div class="card">
        <div class="card-body">
          <h4 class="card-title mb-4">Radial Chart</h4>
          <apx-chart class="apex-charts" dir="ltr" [series]="basicRadialBarChart.series"
            [chart]="basicRadialBarChart.chart" [plotOptions]="basicRadialBarChart.plotOptions"
            [labels]="basicRadialBarChart.labels" [colors]="basicRadialBarChart.colors">
          </apx-chart>
        </div>
      </div>
    </div>
    <div class="col-xl-6">
      <div class="card">
        <div class="card-body">
          <h4 class="card-title mb-4">Radial Chart</h4>
          <apx-chart class="apex-charts" dir="ltr" [series]="simplePieChart.series" [chart]="simplePieChart.chart"
            [labels]="simplePieChart.labels" [legend]="simplePieChart.legend" [colors]="simplePieChart.colors"
            [responsive]="simplePieChart.responsive">
          </apx-chart>
        </div>
      </div>
    </div> -->
    <div class="col-xl-6">
      <div class="card">
        <div class="card-body">
          <h4 class="card-title mb-4 float-sm-left">Değerlendirme Durumları</h4>
          <div class="clearfix"></div>
          <apx-chart dir="ltr" class="apex-charts"
                     [chart]="emailSentBarChart.chart"
                     [series]="emailSentBarChart.series"
                     [legend]="emailSentBarChart.legend"
                     [colors]="emailSentBarChart.colors"
                     [fill]="emailSentBarChart.fill"
                     [dataLabels]="emailSentBarChart.dataLabels"
                     [xaxis]="emailSentBarChart.xaxis"
                     [plotOptions]="emailSentBarChart.plotOptions">
          </apx-chart>
        </div>
      </div>
    </div>
    <div class="col-xl-6">
      <div class="card">
        <div class="card-body">
          <h4 class="card-title mb-4">Radial Chart</h4>
          <apx-chart class="apex-charts" dir="ltr"
                     [series]="simplePieChart.series"
                     [chart]="simplePieChart.chart"
                     [labels]="simplePieChart.labels"
                     [legend]="simplePieChart.legend"
                     [colors]="simplePieChart.colors"
                     [responsive]="simplePieChart.responsive">
          </apx-chart>
        </div>
      </div>
    </div>
  </div>

  <div class="row">


    <div class="col-xl-6">
      <div class="card">
        <div class="card-body">
          <h4 class="card-title mb-5">Yarışma</h4>
          <ul class="verti-timeline list-unstyled">
            <li class="event-list">
              <div class="event-timeline-dot">

                <i class="bx bx-right-arrow-circle font-size-18"></i>
              </div>
              <div class="media">
                <div class="mr-3">
                  <h5 class="font-size-14">24 Ağustos <i
                    class="bx bx-right-arrow-alt font-size-16 text-primary align-middle ml-2"></i>
                  </h5>
                </div>
                <div class="media-body">
                  <div>
                    Yarış Haftası Kayıt ve Açılış

                  </div>
                </div>
              </div>
            </li>
            <li class="event-list">
              <div class="event-timeline-dot">
                <i class="bx bx-right-arrow-circle font-size-18"></i>
              </div>
              <div class="media">
                <div class="mr-3">
                  <h5 class="font-size-14">25 - 27 Ağustos <i
                    class="bx bx-right-arrow-alt font-size-16 text-primary align-middle ml-2"></i>
                  </h5>
                </div>
                <div class="media-body">
                  <div>
                    Teknik Kontroller
                  </div>
                </div>
              </div>
            </li>
            <li class="event-list">
              <div class="event-timeline-dot">
                <i class="bx bx-right-arrow-circle font-size-18"></i>
              </div>
              <div class="media">
                <div class="mr-3">
                  <h5 class="font-size-14">28 Ağustos <i
                    class="bx bx-right-arrow-alt font-size-16 text-primary align-middle ml-2"></i>
                  </h5>
                </div>
                <div class="media-body">
                  <div>
                    İvmelenme Etkinliği
                  </div>
                </div>
              </div>
            </li>
            <li class="event-list">
              <div class="event-timeline-dot">
                <i class="bx bx-right-arrow-circle font-size-18"></i>
              </div>
              <div class="media">
                <div class="mr-3">
                  <h5 class="font-size-14">17 Temmuz <i
                    class="bx bx-right-arrow-alt font-size-16 text-primary align-middle ml-2"></i>
                  </h5>
                </div>
                <div class="media-body">
                  <div>
                    Ödül Töreni ve Kapanış
                  </div>
                </div>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <div class="col-xl-6" *ngIf="chartReady">
      <div class="card">
        <div class="card-body">
          <h4 class="card-title mb-4">Takım Istatistikleri</h4>


          <div class="text-center">
            <div class="mb-4">
              <i class="bx bx-map-pin text-primary display-4"></i>
            </div>
            <h3>{{stats.team_count}}</h3>
            <p>Toplam</p>
          </div>

          <div class="table-responsive mt-4">
            <table class="table table-centered table-nowrap">
              <tbody>
              <tr>
                <td style="width: 30%">
                  <p class="mb-0">İlk seferde
                    değerlendirmeyi geçenler</p>
                </td>
                <td style="width: 25%">
                  <h5 class="mb-0">{{stats.eval_success_at_once}}</h5>
                </td>
                <td>
                  <div class="progress bg-transparent progress-sm">
                    <div class="progress-bar bg-primary rounded" role="progressbar"
                         style="width: {{stats.progress_eval_success_at_once}}%"
                         attr.aria-valuenow="{{stats.progress_eval_success_at_once}}" attr.aria-valuemin="0"
                         attr.aria-valuemax="100">
                    </div>
                  </div>
                </td>
              </tr>
              <tr>
                <td>
                  <p class="mb-0">Randevuya gelen takımlar
                  </p>
                </td>
                <td>
                  <h5 class="mb-0">{{stats.appointment_joined}}</h5>
                </td>
                <td>
                  <div class="progress bg-transparent progress-sm">
                    <div class="progress-bar bg-success rounded" role="progressbar"
                         style="width: {{stats.progress_appointment_joined}}%"
                         attr.aria-valuenow="{{stats.progress_appointment_joined}}" attr.aria-valuemin="0"
                         attr.aria-valuemax="100">
                    </div>
                  </div>
                </td>
              </tr>
              <tr>
                <td>
                  <p class="mb-0">Sticker alan takımlar
                  </p>
                </td>
                <td>
                  <h5 class="mb-0">{{stats.sticker_ready}}</h5>
                </td>
                <td>
                  <div class="progress bg-transparent progress-sm">
                    <div class="progress-bar bg-warning rounded" role="progressbar"
                         [style.width]="stats.progress_sticker_ready"
                         attr.aria-valuenow="{{stats.progress_sticker_ready}}" attr.aria-valuemin="0"
                         attr.aria-valuemax="100">
                    </div>
                  </div>
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>


  <div class="row">
    <div class="col-xl-6">
      <div class="card">
        <div class="card-body">
          <h4 class="card-title mb-4">Bugün birden fazla randevu alan takımlar</h4>

          <div class="table-responsive mt-4">

            <table class="table table-nowrap table-hover mb-0">

              <thead>
              <tr>
                <th scope="col">Team Name</th>
                <th scope="col">University Name</th>
                <th scope="col">Vehicle Number</th>
                <th scope="col">Vehicle Name</th>

              </tr>
              </thead>
              <tbody>
              <tr *ngFor="let team of hasMultiAppointmentTeams">
                <td>{{team.team_name}}</td>
                <td>{{team.university_name}}</td>
                <td>{{team.vehicle_number}}</td>
                <td>{{team.vehicle_name}}</td>
              </tr>
              </tbody>
            </table>


          </div>
        </div>
      </div>
    </div>


    <div class="col-xl-6">
      <div class="card">
        <div class="card-body">
          <h4 class="card-title mb-4">Randevusuna Gelmeyen Takımlar</h4>

          <div class="table-responsive mt-4">

            <table class="table table-nowrap table-hover mb-0">

              <thead>
              <tr>
                <th scope="col">Team Name</th>
                <th scope="col">University Name</th>
                <th scope="col">Vehicle Number</th>
                <th scope="col">Appointment Time</th>

              </tr>
              </thead>
              <tbody>
              <tr *ngFor="let appointment of notJoinedAppointment">
                <td>{{appointment.team.team_name}}</td>
                <td>{{appointment.team.university_name}}</td>
                <td>{{appointment.team.vehicle_number}}</td>
                <td>{{appointment.start_time}}</td>
              </tr>
              </tbody>
            </table>


          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-xl-12">
      <div class="card">
        <div class="card-body">
          <h4 class="card-title mb-4">Değerlendirme Durumu
            <div class="float-right">

              <a (click)="exportEvalStatus()" title="Export Penalties" class="btn btn-success" style="color: white;"><i
                class="bx bxs-download"></i></a>

            </div>
          </h4>
          <div class="table-responsive mt-4">

            <table class="table table-nowrap table-hover mb-0">

              <thead>
              <tr>
                <th scope="col">Vehicle Number</th>
                <th scope="col">Team Name</th>
                <th scope="col">University Name</th>
                <th scope="col">Status</th>
                <th scope="col">Dynamic Drive</th>
              </tr>
              </thead>
              <tbody>
              <tr *ngFor="let eval of evalList" [className]="evalClass(eval.status)">
                <td>{{eval.team.vehicle_number}}</td>
                <td>{{eval.team.team_name}}</td>
                <td>{{eval.team.university_name}}</td>
                <td>{{eval.status}}</td>
                <td>{{eval.dynamic_drive ? 'passed' : 'failed'}}</td>
              </tr>
              </tbody>
            </table>


          </div>
        </div>
      </div>
    </div>
  </div>
</div>
