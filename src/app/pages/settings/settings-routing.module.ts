import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { CriteriasComponent } from './criterias/criterias.component';
import { CreateCriteriaComponent } from './create-criteria/create-criteria.component';
import { EditCriteriaComponent } from './edit-criteria/edit-criteria.component';


const routes: Routes = [
  {
    path: 'criterias',
    component: CriteriasComponent
  },
  {
    path: 'create',
    component: CreateCriteriaComponent
  },
  {
    path: 'edit-criteria/:id',
    component: EditCriteriaComponent
  }
];
@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class SettingsRoutingModule { }
