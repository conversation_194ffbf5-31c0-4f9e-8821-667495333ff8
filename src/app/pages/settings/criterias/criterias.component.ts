import { Component, OnInit, ViewChild } from '@angular/core';
import { SettingsService } from 'src/app/core/services/settings.service';
import { FormBuilder, FormGroup } from '@angular/forms';
import { DatatableComponent } from '@swimlane/ngx-datatable';
import { NgxSpinnerService } from 'ngx-spinner';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-criterias',
  templateUrl: './criterias.component.html',
  styleUrls: ['./criterias.component.scss'],
})
export class CriteriasComponent implements OnInit {


  // bread crumb items
  breadCrumbItems: Array<{}>;
  filterForm: FormGroup;
  settings;

  editing = {};
  rows = [];
  protected temp: any[];
  @ViewChild(DatatableComponent) table: DatatableComponent;
  columns = [];

  loadingIndicator: boolean = true;
  reorderable: boolean = true;

  constructor(private settingsService: SettingsService, private fb: FormBuilder, private spinner: NgxSpinnerService) { }

  ngOnInit() {
    this.breadCrumbItems = [{ label: 'Criteria' }, { label: 'Create List', active: true }];
    this.filterForm = this.fb.group({
      limit: [200],
    });
    this.columns = [
      { prop: 'id', width: 30 },
      { prop: 'category', width: 100 },
      { prop: 'sub_category', width: 100 },
      { prop: 'subject', width: 200 },
      { prop: 'content', width: 300 },
      //{ prop: 'rules', width: 200 },
    ];

    this.getCriterias();
  }


  getCriterias() {
    this.settingsService.getCriterias(this.filterForm.value).subscribe(data => {
      console.log(data);
      this.rows = data['data'];
      this.temp = data['data'];
    });
  }

  getSettings() {
    this.settingsService.getSettings().subscribe(settings => {
      this.settings = Object.keys(settings['technical_evals']);

      console.log('Object.keys(settings[technical_eval])');
      console.log(Object.keys(settings['technical_evals']));
      console.log(settings['technical_evals']);
    });
  }

  updateFilter(event) {
    const filterVal = event.target.value.toLowerCase();
    // filter our data
    // assign filtered matches to the active datatable
    this.rows = this.temp.filter(item => {
      for (let i = 0; i < this.columns.length; i++) {
        const colValue = item[this.columns[i].prop];

        // if no filter OR colvalue is NOT null AND contains the given filter
        if (!filterVal || (!!colValue && colValue.toString().toLowerCase().indexOf(filterVal) !== -1)) {
          // found match, return true to add to result set
          return true;
        }
      }
    });


    // Whenever the filter changes, always go back to the first page
    this.table.offset = 0;
  }

  uploadCriteriaFile(files, $event){
    this.spinner.show();
    const formData: FormData = new FormData();
    formData.append('file_name', files.item(0));
    console.log(formData);
    this.settingsService.importCriteria(formData).subscribe(data => {
      this.spinner.hide();
      //alert('Imported Successfully');
      this.position('Imported Successfully', 'success');
    }, error => {
      //alert(error);
      this.spinner.hide();
      this.position(error['message'], 'error');
    });
  }

  position(title, icon) {
    Swal.fire({
      position: 'center',
      icon: icon,
      title: title,
      timer: 1500,
      allowOutsideClick: false,
      showConfirmButton: true,
      showCancelButton: false,
    })
      .then((willDelete) => {
      });
  }
}
