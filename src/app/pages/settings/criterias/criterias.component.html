<div class="container-fluid" xmlns="http://www.w3.org/1999/html">
  <div class="row">
    <div class="col-12">
      <div class="text-center my-3">

        <a [routerLink]="['/create']" class="btn btn-success inner mr-2">
          Create Criteria </a>

          <a (click)="criteriaImport.click()" class="btn btn-success inner">
            İmport Criteria </a>

          <input type="file"
            accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
            #criteriaImport (change)="uploadCriteriaFile(criteriaImport.files, $event)" style="display: none;"/>
      </div>
    </div>
  </div>
  <app-page-title title="Criterias" [breadCrumbItems]="breadCrumbItems"></app-page-title>


  <div class="row">
    <div class="col-12">
      <!--
  	   Embed the ngx-datatable component with following property bindings/values:
       1. sorttype - allow data to be sorted on multiple columns
       2. headerHeight - Set height of table header at 50 pixels
       3. footerHeight - Set height of table footer at 50 pixels
       4. rowHeight - Set height of table rows at 50 pixels (or 'auto')
       5. rows - Derives the data for the table rows from the component class
                 property of rows
       6. columns - Derives the names for the table columns from the component
                    class property of columns
       7. columnMode - Use of standard, flex or force - Force value makes columns
                       equidistant and span the width of the parent container
       8. limit - the number of records to display before paginating the results
-->
      <!--   <ngx-datatable #mydatatable class="bootstrap" [sortType]="'multi'" [headerHeight]="50" [footerHeight]="40"
        [rowHeight]="'auto'" [rows]="rows" [columnMode]="'force'" [limit]="5" [loadingIndicator]="loadingIndicator">

        <ngx-datatable-column name="team_name">
          <ng-template ngx-datatable-cell-template let-rowIndex="rowIndex" let-value="value" let-row="row">
            <span title="Double click to edit" (dblclick)="editing[rowIndex + '-name'] = true"
              *ngIf="!editing[rowIndex + '-name']">
              {{value}}
            </span>
            <input autofocus (blur)="this.updateValue($event, 'name', rowIndex)" *ngIf="editing[rowIndex+ '-name']"
              type="text" [value]="value" />
          </ng-template>
        </ngx-datatable-column>

        <ngx-datatable-column name="university_name">
          <ng-template ngx-datatable-cell-template let-rowIndex="rowIndex" let-row="row" let-value="value">
            <span title="Double click to edit" (dblclick)="editing[rowIndex + '-university_name'] = true"
              *ngIf="!editing[rowIndex + '-university_name']">
              {{value}}
            </span>
            <select *ngIf="editing[rowIndex + '-university_name']"
              (blur)="editing[rowIndex + '-university_name'] = false"
              (change)="this.updateValue($event, 'university_name', rowIndex)" [value]="value">
              <option value="male">Male</option>
              <option value="female">Female</option>
            </select>
          </ng-template>
        </ngx-datatable-column>


        <ngx-datatable-column name="vehicle_name">
          <ng-template ngx-datatable-cell-template let-value="value">
            {{value}}
          </ng-template>
        </ngx-datatable-column>

        <ngx-datatable-column name="city_name">
          <ng-template ngx-datatable-cell-template let-value="value">
            {{value}}
          </ng-template>
        </ngx-datatable-column>

        <ngx-datatable-column name="team_leader">
          <ng-template ngx-datatable-cell-template let-value="value">
            {{value}}
          </ng-template>
        </ngx-datatable-column>

        <ngx-datatable-column name="vehicle_category">
          <ng-template ngx-datatable-cell-template let-rowIndex="rowIndex" let-row="row" let-value="value">
            <span title="Double click to edit" (dblclick)="editing[rowIndex + '-vehicle_category'] = true"
              *ngIf="!editing[rowIndex + '-vehicle_category']">
              {{value}}
            </span>
            <select *ngIf="editing[rowIndex + '-vehicle_category']"
              (blur)="editing[rowIndex + '-vehicle_category'] = false"
              (change)="this.updateValue($event, 'vehicle_category', rowIndex)" [value]="value">
              <option value="male">Male</option>
              <option value="female">Female</option>
            </select>
          </ng-template>
        </ngx-datatable-column>

      </ngx-datatable> -->

      <input
        type="text"
        class="mx-2 mb-2 form-control"
        style="width:30%;"
        placeholder="Type to filter..."
        (keyup)="updateFilter($event)"
      />
      <ngx-datatable [rows]="rows" class="bootstrap" [loadingIndicator]="loadingIndicator" [columnMode]="'force'"
                     [headerHeight]="50" [footerHeight]="50" [rowHeight]="'auto'" [limit]="10" [columns]="columns"
                     [reorderable]="reorderable">


        <ngx-datatable-column *ngFor="let column of columns; let i = index;" name="{{column.name}}"
                              prop="{{column.prop}}" [width]="column.width">

          <ng-template let-value="value" let-row="row" ngx-datatable-cell-template>

            <a *ngIf="column.prop === 'id'" href="javascript: void(0);"
               [routerLink]="['/edit-criteria/', row.id]">{{value}}</a>

            <ul *ngIf="column.prop === 'rules'">
              <li *ngFor="let rule of value">
                {{rule.content}}
              </li>
            </ul>

            <span *ngIf="column.prop !== 'id' && column.prop !== 'rules'">{{value}}</span>

          </ng-template>
        </ngx-datatable-column>


      </ngx-datatable>
    </div>
  </div>
</div>
<ngx-spinner type="ball-clip-rotate-multiple" size="medium"> </ngx-spinner>