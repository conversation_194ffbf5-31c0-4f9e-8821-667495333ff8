import { Component, OnInit } from '@angular/core';
import { FormControl, FormArray, FormGroup, FormBuilder } from '@angular/forms';
import { SettingsService } from '../../../core/services/settings.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-create-criteria',
  templateUrl: './create-criteria.component.html',
  styleUrls: ['./create-criteria.component.scss']
})
export class CreateCriteriaComponent implements OnInit {

  // bread crumb items
  breadCrumbItems: Array<{}>;
  criterias = [{
    id: 1,
    mainId: 1, //  1 electrical 2
    catId: 1, // Physical Specification,
    title: "Vehicle height",
    description: [{
      content: "At least 1 m above ground level",
    }],
    isRequired: true,
    value: 30
  }, {
    id: 2,
    mainId: 1, // hybrid 1 electrical 2
    catId: 1, // Physical Specification vs
    title: "Vehicle measurements",
    description: [{
      content: "Should fit within the lines drawn at the technical inspection",
    }, {
      content: "Technical drawing should be provided with a seperate page",
    }],
    isRequired: true,
    value: 30
  }, {
    id: 3,
    mainId: 1, // hybrid 1 electrical 2
    catId: 1, // Physical Specification vs
    title: "Door mechanism",
    description: [{
      content: "Fixed to the body with a safe connecting element",
    }, {
      content: "Door mechanism can be closed without any manual intervention",
    }, {
      content: "Can be opened from outside/no possibility of unintended opening",
    }],
    isRequired: true,
    value: 30
  }];

  main_cat_of_criteria = [{
    id: 0,
    name: "Select"
  }, {
    id: 1,
    name: "Physical Specification"
  }, {
    id: 2,
    name: "Hardware"
  }, {
    id: 3,
    name: "Electrical Safety"
  }, {
    id: 4,
    name: "Safety Hardware"
  }, {
    id: 5,
    name: "Battery"
  }, {
    id: 6,
    name: "Motor Driver"
  }, {
    id: 7,
    name: "Telemetry"
  }, {
    id: 8,
    name: "Electrical Test"
  }, {
    id: 9,
    name: "Sticker"
  }, {
    id: 10,
    name: "Test"
  }, {
    id: 11,
    name: "Hydromobile"
  }, {
    id: 12,
    name: "Domestic Parts"
  }];

  obj = []

  categoryName;

  form = new FormGroup({
    criteria: this.fb.array([])
  });


  hidden: boolean;
  selected: any;

  get criteria(): FormArray { return this.form.get('criteria') as FormArray; }

  constructor(private settingsService: SettingsService, private fb: FormBuilder, private router: Router) { }

  ngOnInit() {
    this.breadCrumbItems = [{ label: 'Criteria' }, { label: 'Create Criteria', active: true }];

    this.hidden = true;
    this.addCriteria();

    this.getSettings();
  }

  addCriteria() {
    let control = <FormArray>this.form.controls.criteria;
    control.push(
      this.fb.group({
        category: [''],
        subject: [''],
        rules: [''],
        content: [''],
        isRequired: [null],
        allowValue: [null],
      })
    )
  }

  deleteCriteria(i: number) {
    this.criteria.removeAt(i);
  }

  createCriteria() {
    console.log(this.form.value);
    this.obj = [];
    this.form.value["criteria"].forEach(data => {
      if (data["subject"]) {
        this.obj.push(
          {
            "category": this.categoryName,
            "subject": data["subject"],
            "rules": [
              {
                "content": data["content"],
                "isRequired": data["isRequired"] ? 1 : 0,
                "allowValue": data["allowValue"] ? 1 : 0
              }
            ]
          }
        )
      }
    });
    console.log("this.obj");
    console.log(this.obj);
    this.putCriteria(this.obj);
  }



  getSettings() {
    this.settingsService.getSettings().subscribe(settings => {
      console.log("settings");
      console.log(settings);
    })
  }


  putCriteria(obj) {
    this.settingsService.putCriteria(obj).subscribe(settings => {
      console.log("updateSettings");
      console.log(settings);
      //alert("Successfully created");

      this.router.navigate(['/dashboard']);
    })
  }



  onChange(event) {
    this.categoryName = event.target.value;
    // this.form.patchValue({ criteria: [{ category: this.categoryName }] });
  }



}
