import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { SettingsRoutingModule } from './settings-routing.module';
import { CriteriasComponent } from './criterias/criterias.component';


import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbDropdownModule, NgbTooltipModule, NgbNavModule } from '@ng-bootstrap/ng-bootstrap';
import { NgApexchartsModule } from 'ng-apexcharts';
import { PerfectScrollbarModule } from 'ngx-perfect-scrollbar';
import { UIModule } from 'src/app/shared/ui/ui.module';
import { WidgetModule } from 'src/app/shared/widget/widget.module';
import { CreateCriteriaComponent } from './create-criteria/create-criteria.component';

import { NgxDatatableModule } from '@swimlane/ngx-datatable';
import { EditCriteriaComponent } from './edit-criteria/edit-criteria.component';
import { NgxSpinnerModule } from 'ngx-spinner';
@NgModule({
  declarations: [CriteriasComponent, CreateCriteriaComponent, EditCriteriaComponent],
  imports: [
    CommonModule,
    SettingsRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    UIModule,
    NgbDropdownModule,
    NgbTooltipModule,
    NgbNavModule,
    WidgetModule,
    NgApexchartsModule,
    PerfectScrollbarModule,
    NgxDatatableModule,
    NgxSpinnerModule
  ]
})
export class SettingsModule { }
