import { Component, OnInit } from '@angular/core';
import { SettingsService } from 'src/app/core/services/settings.service';
import { ActivatedRoute, Router } from '@angular/router';
import { FormArray, FormBuilder, FormGroup } from '@angular/forms';

@Component({
  selector: 'app-edit-criteria',
  templateUrl: './edit-criteria.component.html',
  styleUrls: ['./edit-criteria.component.scss'],
})
export class EditCriteriaComponent implements OnInit {

  form: FormGroup;

  _criteria: any = {}

  get criteria(): FormArray { return this.form.get('criteria') as FormArray; }

  categoryName: any;
  subCategoryName: any;
  breadCrumbItems: Array<{}>;
  criteriaId;
  type: any;

  main_cat_of_criteria = [{
    id: 0,
    name: 'Select',
  }, {
    id: 1,
    name: 'Physical Specification',
  }, {
    id: 2,
    name: 'Hardware',
  }, {
    id: 3,
    name: 'Electrical Safety',
  }, {
    id: 4,
    name: 'Safety Hardware',
  }, {
    id: 5,
    name: 'Battery',
  }, {
    id: 6,
    name: 'Motor Driver',
  }, {
    id: 7,
    name: 'Telemetry',
  }, {
    id: 8,
    name: 'Electrical Test',
  }, {
    id: 9,
    name: 'Sticker',
  }, {
    id: 10,
    name: 'Test',
  }];

  constructor(private fb: FormBuilder, private settingsService: SettingsService, private router: Router, private activatedRoute: ActivatedRoute) { }

  ngOnInit(): void {
    this.breadCrumbItems = [{ label: 'Edit Criteria' }, { label: 'Criteria Wizard', active: true }];
    this.activatedRoute.params.subscribe(params => {
      this.criteriaId = params['id'];
      this.getCriteria();

    });
  }

  getCriteria() {
    this.settingsService.getCriteria(this.criteriaId).subscribe(data => {
      this._criteria = data['data'];
      //let rules = data['data']['rules'];
      this.categoryName = this._criteria.category;
      this.subCategoryName = this._criteria.sub_category;
      this.type = this._criteria.type + '';

      console.log('type', this.type);
      this.addCriteria(this._criteria, this.categoryName, this._criteria.subject);
      /*
      rules?.forEach(item => {
        this.addCriteria(item, this.categoryName, this._criteria.subject);
      });*/
    });
  }


  addCriteria(item, category, subject) {
   this.form = this.fb.group(
    {
      category: [category],
      sub_category: [item.sub_category],
      subject: [subject],
      content: [item.content],
      isRequired: [item.isRequired],
      allowValue: [item.allowValue],
      allow_approve: [item.allow_approve],
      allow_defective: [item.allow_defective],
      allow_disqualified: [item.allow_disqualified],
    }
   )
  }

  deleteCriteria(i: number) {
    this.criteria.removeAt(i);
  }

  obj = {};

  createCriteria() {
    console.log(this.form.value);
    // this.obj = [];
    /*
    this.form.value['criteria'].forEach(data => {
      if (data['subject']) {
        console.log('tst', data);
        if (this.type === 'null') {
          this.type = null;
        }
        this.obj = {
          'category': this.categoryName,
          'sub_category': this.subCategoryName,
          'subject': data['subject'],
          'type': this.type,
          'rules': [
            {
              'content': data['content'],
              'isRequired': data['isRequired'] ? 1 : 0,
              'allowValue': data['allowValue'] ? 1 : 0,
            },
          ],
          'allow_approve': data['allow_approve'] ? data['allow_approve'] : 0,
          'allow_defective': data['allow_defective'] ? data['allow_defective'] : 0,
          'allow_disqualified': data['allow_disqualified'] ? data['allow_disqualified'] : 0,
        };
      }
    });*/

    this.obj = Object.assign({}, this.form.value);
    console.log('this.obj');
    console.log(this.obj);
    this.putCriteria(this.obj);
  }


  getSettings() {
    this.settingsService.getSettings().subscribe(settings => {
      console.log('settings');
      console.log(settings);
    });
  }


  putCriteria(obj) {
    this.settingsService.patchCriteria(obj, this.criteriaId).subscribe(settings => {
      // console.log('updateSettings');
      // console.log(settings);
      //alert('Successfully updated');

      history.back();
      // this.router.navigate(['/dashboard']);
    });
  }


  onTypeChange(event) {
    this.type = event.target.value;

    console.log('typet', this.type);

  }
}
