<div class="container-fluid">

  <!-- start page title -->
  <app-page-title title="Edit Criteria" [breadCrumbItems]="breadCrumbItems"></app-page-title>
  <!-- end page title -->

  <div class="row">
    <div class="col-lg-12" *ngIf="form">
      <div class="card">
        <div class="card-body">
          <form class="outer-repeater">
            <div data-repeater-list="outer-group" class="outer">
              <div data-repeater-item class="outer" [formGroup]="form">
                <div class="form-group row mb-4">
                  <label for="type" class="col-form-label col-lg-2">Vehicle Type</label>
                  <div class="col-lg-10">
                    <select class="form-control" name="types" [ngModel]="type"  [ngModelOptions]="{standalone: true}"
                            (change)="onTypeChange($event)">
                      <option [value]="null">
                        Both
                      </option>
                      <option [value]="2">
                        Hydro
                      </option>
                      <option [value]="1">
                        Electro
                      </option>
                    </select>
                  </div>
                </div>
                <div class="form-group row mb-4">
                  <label for="category" class="col-form-label col-lg-2">Main Category</label>
                  <div class="col-lg-10">
                    <input type="text" class="form-control" placeholder="Category" formControlName="category">
                  </div>
                </div>
                <div class="form-group row mb-4">
                  <label for="sub_category" class="col-form-label col-lg-2">Sub Category</label>
                  <div class="col-lg-10">
                    
                    <input type="text" class="form-control" placeholder="Sub Category" formControlName="sub_category">
                  </div>
                </div>
                <div class="form-group row mb-4">
                  <label for="subject" class="col-form-label col-lg-2">Subject</label>
                  <div class="col-lg-10">
                    <input type="text" class="form-control" placeholder="Subject" formControlName="subject"/>
                  </div>
                </div>
                <div class="form-group row mb-4">
                  <label for="content" class="col-form-label col-lg-2">Content</label>
                  <div class="col-lg-10">
                    <input type="text" class="form-control" placeholder="Content" formControlName="content"/>
                  </div>
                </div>
                <div class="row">
                  <div class="form-group mb-4 col-4">
                    <label for="allow_approve" class="col-form-label col-12">Approve</label>
                    <div class="col-12">
                      <input type="text" class="form-control" placeholder="Approve" formControlName="allow_approve"/>
                    </div>
                  </div>
                  <div class="form-group mb-4 col-4">
                    <label for="allow_defective" class="col-form-label col-lg-12">Defective</label>
                    <div class="col-lg-12">
                      <input type="text" class="form-control" placeholder="Defective" formControlName="allow_defective"/>
                    </div>
                  </div>
                  <div class="form-group mb-4 col-4">
                    <label for="allow_disqualified" class="col-form-label col-lg-12">Disqualified</label>
                    <div class="col-12">
                      <input type="text" class="form-control" placeholder="Disqualified" formControlName="allow_disqualified"/>
                    </div>
                  </div>
                </div>
                <!--
                <div class="inner-repeater mb-4">
                  <form [formGroup]="form">
                    <ng-container formArrayName="criteria">
                      <div class="inner form-group mb-0 row">
                        <label class=" col-form-label col-lg-2">Add New Criteria</label>
                        <div class="inner col-lg-10 ml-md-auto"
                             *ngFor="let data of criteria.controls; index as i;">
                          <ng-container [formGroupName]="i">
                            <div class="mb-3 row align-items-center">
                              <div class="col-md-3">
                                <input type="text" formControlName="subject"
                                       class="inner form-control"
                                       placeholder="Enter Subject..."/>
                              </div>
                              <div class="col-md-4">
                                <div class="mt-4 mt-md-0">
                                  <input type="text" class="inner form-control"
                                         placeholder="Enter Condition..."
                                         formControlName="content"/>
                                </div>
                              </div>
                              <div class="col-md-2">
                                <div class="form-check mb-3">
                                  <input class="form-check-input" type="checkbox"
                                         formControlName="isRequired"> Is Required?
                                </div>
                              </div>
                              <div class="col-md-2">
                                <div class="form-check mb-3">
                                  <input class="form-check-input" type="checkbox"
                                         formControlName="allowValue"> Allow value?
                                </div>
                              </div>
                              <div class="col-md-1">
                                <div class="mt-2 mt-md-0">
                                  <input type="button"
                                         class="btn btn-primary btn-block inner"
                                         value="X" (click)="deleteCriteria(i)"/>
                                </div>
                              </div>
                            </div>
                          </ng-container>
                        </div>
                      </div>
                    </ng-container>
                  </form>
                </div>-->
              </div>
            </div>
          </form>
          <div class="row justify-content-end">
            <div class="col-lg-10">
              <button type="submit" class="btn btn-primary" (click)="createCriteria()">Update
                Criteria
              </button>
            </div>
          </div>

        </div>
      </div>
    </div>
  </div>
</div>
