

.table-container {
  width: 100%;
  overflow-x: auto;
}



::ng-deep .p-datatable {
  .p-datatable-thead > tr > th {
    background-color: #f8f9fa;
    border-color: #dee2e6;
    font-weight: 600;
    color: #495057;
    text-align: center;
    padding: 12px 8px;
  }

  .p-datatable-tbody > tr > td {
    border-color: #dee2e6;
    padding: 10px 8px;
  }

  .p-datatable-tbody > tr:hover {
    background-color: #f5f5f5;
  }
}


.badge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  text-align: center;
  display: inline-block;
  min-width: 60px;

  &.badge-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
  }

  &.badge-danger {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
  }

  &.badge-secondary {
    background-color: #e2e3e5;
    color: #383d41;
    border: 1px solid #d6d8db;
  }
}


@media (max-width: 768px) {
  ::ng-deep .p-datatable .p-datatable-thead > tr > th,
  ::ng-deep .p-datatable .p-datatable-tbody > tr > td {
    padding: 8px 4px;
    font-size: 12px;
    width:100px;
  }
}

.table-responsive-mobile {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;

  ::ng-deep .p-datatable-table {
    min-width: 900px;
  }
}

.team-column {
  max-width: 250px;
  white-space: normal;
  overflow: visible;
  text-overflow: initial;
  word-break: break-word;
}

@media (max-width: 768px) {
  .team-column {
    max-width: 200px;
    font-size: 12px;
    white-space: normal;
    word-break: break-word;
    text-overflow: initial;
    overflow: visible;
  }
}

.sortable {
  cursor: pointer;
  margin-left: 4px;
  font-size: 16px;
  vertical-align: middle;
  color: #888;
  transition: color 0.2s;
}
.sortable:hover {
  color: #007bff;
}
