<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <h4>Reports Detail</h4>
          <div class="row mb-3 align-items-center">
            <div class="col d-flex justify-content-between">
              <div>
                <h2>Dinamik Sürüş Tablosu</h2>
              </div>
              <div class="col d-flex justify-content-end" style="gap: 1rem;">
                <div class="form-check d-flex align-items-center">
                  <input type="checkbox" class="form-check-input" id="filterPassedOnly" [(ngModel)]="dynamicDriveFilterPassedOnly" (change)="applyDynamicDriveFilters()">
                  <label class="form-check-label" for="filterPassedOnly">Sadece Geçti</label>
                </div>
                <button class="btn btn-primary btn-sm" (click)="applyDynamicDriveFilters()">Ara</button>
                <button class="btn btn-secondary btn-sm" (click)="clearDynamicDriveFilters()">Filtrey<PERSON>zle</button>
                <button type="button" class="btn btn-success" (click)="exportDynamicDriveToExcel()">
                  <i class="bx bx-download"></i> Export Excel
                </button>
              </div>
            </div>
          </div>
          <div class="table-responsive-mobile table-container">
            <p-table [value]="dynamicDriveFiltered" [rows]="15" [paginator]="true" [loading]="loading" styleClass="p-datatable-striped">
              <ng-template pTemplate="header">
                <tr>
                  <th style="width: 120px;">Araç No <span class="sortable" (click)="sortDynamicDrive('vehicle_number')"><i [ngClass]="getSortIcon('vehicle_number')"></i></span></th>
                  <th style="width: 100px;">ID <span class="sortable" (click)="sortDynamicDrive('team_id')"><i [ngClass]="getSortIcon('team_id')"></i></span></th>
                  <th class="team-column" style="width: 200px;">TAKIM <span class="sortable" (click)="sortDynamicDrive('team_name')"><i [ngClass]="getSortIcon('team_name')"></i></span></th>
                  <th style="width: 220px;">Liseler Arası/Uluslararası <span class="sortable" (click)="sortDynamicDrive('school_type_name')"><i [ngClass]="getSortIcon('school_type_name')"></i></span></th>
                  <th style="width: 180px;">Yarışma Kategorisi <span class="sortable" (click)="sortDynamicDrive('vehicle_category')"><i [ngClass]="getSortIcon('vehicle_category')"></i></span></th>
                  <th style="width: 120px;">Eval Date <span class="sortable" (click)="sortDynamicDrive('eval_date')"><i [ngClass]="getSortIcon('eval_date')"></i></span></th>
                  <th style="width: 160px;">Evaluation Status <span class="sortable" (click)="sortDynamicDrive('evaluation_status')"><i [ngClass]="getSortIcon('evaluation_status')"></i></span></th>
                  <ng-container *ngFor="let day of dynamicDays; let i = index">
                    <th style="width: 220px;">{{i+1}}. Gün dinamik sürüş <span class="sortable" (click)="sortDynamicDrive('day'+(i+1)+'_dynamic')"><i [ngClass]="getSortIcon('day'+(i+1)+'_dynamic')"></i></span></th>
                  </ng-container>
                </tr>
                <tr>
                  <th>
                    <input type="text" class="form-control form-control-sm" [(ngModel)]="dynamicDriveFilters.vehicle_number" placeholder="Araç No" (keyup.enter)="applyDynamicDriveFilters()">
                  </th>
                  <th>
                    <input type="text" class="form-control form-control-sm" [(ngModel)]="dynamicDriveFilters.team_id" placeholder="ID" (keyup.enter)="applyDynamicDriveFilters()">
                  </th>
                  <th>
                    <input type="text" class="form-control form-control-sm" [(ngModel)]="dynamicDriveFilters.team_name" placeholder="Takım" (keyup.enter)="applyDynamicDriveFilters()">
                  </th>
                  <th>
                    <select class="form-control form-control-sm" [(ngModel)]="dynamicDriveFilters.school_type_name" (change)="applyDynamicDriveFilters()">
                      <option value="">Tümü</option>
                      <option *ngFor="let opt of schoolTypeOptions" [value]="opt">{{ opt }}</option>
                    </select>
                  </th>
                  <th>
                    <select class="form-control form-control-sm" [(ngModel)]="dynamicDriveFilters.vehicle_category" (change)="applyDynamicDriveFilters()">
                      <option value="">Tümü</option>
                      <option *ngFor="let opt of vehicleCategoryOptions" [value]="opt">{{ opt }}</option>
                    </select>
                  </th>
                  <th>
                    <input type="text" class="form-control form-control-sm" [(ngModel)]="dynamicDriveFilters.eval_date" placeholder="Eval Date" (keyup.enter)="applyDynamicDriveFilters()">
                  </th>
                  <th>
                    <select class="form-control form-control-sm" [(ngModel)]="dynamicDriveFilters.evaluation_status" (change)="applyDynamicDriveFilters()">
                      <option value="">Tümü</option>
                      <option *ngFor="let opt of evaluationStatusOptions" [value]="opt">{{ opt }}</option>
                    </select>
                  </th>
                  <ng-container *ngFor="let day of dynamicDays; let i = index">
                    <th>
                      <select class="form-control form-control-sm" [(ngModel)]="dynamicDriveFilters['day'+(i+1)+'_dynamic']" (change)="applyDynamicDriveFilters()">
                        <option value="">Tümü</option>
                        <option value="Geçti">Geçti</option>
                        <option value="Geçemedi">Geçemedi</option>
                      </select>
                    </th>
                  </ng-container>
                </tr>
              </ng-template>
              <ng-template pTemplate="body" let-row>
                <tr>
                  <td style="text-align: center;">{{ row.team?.vehicle_number }}</td>
                  <td style="text-align: center;">{{ row.team?.team_id }}</td>
                  <td class="team-column" style="text-align: center;">{{ row.team?.team_name }}</td>
                  <td style="text-align: center;">{{ dynamicDriveConfig.getRowExportData(row)['Liseler Arası/Uluslararası'] }}</td>
                  <td style="text-align: center;">{{ dynamicDriveConfig.getRowExportData(row)['Yarışma Kategorisi'] }}</td>
                  <td style="text-align: center;">{{ row.eval_date }}</td>
                  <td style="text-align: center;">
                    <span [ngClass]="row.evaluation_status?.toLowerCase() === 'success' ? 'badge badge-success' : row.evaluation_status?.toLowerCase() === 'closed' ? 'badge badge-danger' : row.evaluation_status?.toLowerCase() === 'active' ? 'badge badge-secondary' : 'badge'">
                      {{ row.evaluation_status }}
                    </span>
                  </td>
                  <ng-container *ngFor="let day of dynamicDays; let i = index">
                    <td style="text-align: center;">
                      <ng-container [ngSwitch]="row['day'+(i+1)+'_dynamic']">
                        <span *ngSwitchCase="'Geçti'" class="badge badge-success">{{ row['day'+(i+1)+'_dynamic'] }}</span>
                        <span *ngSwitchCase="'Geçemedi'" class="badge badge-danger">{{ row['day'+(i+1)+'_dynamic'] }}</span>
                        <span *ngSwitchCase="'Katılmadı'" class="badge badge-secondary">{{ row['day'+(i+1)+'_dynamic'] }}</span>
                        <span *ngSwitchDefault>{{ row['day'+(i+1)+'_dynamic'] }}</span>
                      </ng-container>
                    </td>
                  </ng-container>
                </tr>
              </ng-template>
              <ng-template pTemplate="emptymessage">
                <tr>
                  <td colspan="100%" class="text-center">Veri bulunamadı</td>
                </tr>
              </ng-template>
            </p-table>
          </div>
          <div class="card mt-4">
            <div class="card-body">
              <div class="btn-group mb-2">
                <button type="button" class="btn btn-outline-primary" [class.active]="dynamicStatsType === 'elektromobil'" (click)="selectDynamicStatsType('elektromobil')">
                  Uluslararası Elektromobil
                </button>
                <button type="button" class="btn btn-outline-primary" [class.active]="dynamicStatsType === 'hidro'" (click)="selectDynamicStatsType('hidro')">
                  Uluslararası Hidromobil
                </button>
                <button type="button" class="btn btn-outline-primary" [class.active]="dynamicStatsType === 'lise'" (click)="selectDynamicStatsType('lise')">
                  Lise
                </button>
              </div>
              <div class="d-flex justify-content-between align-items-center mb-2">
                <h5>İstatistik</h5>
                <button type="button" class="btn btn-success btn-sm" (click)="exportStatisticsToExcel()">
                  <i class="bx bx-download"></i> Export Excel
                </button>
              </div>
              <div class="table-responsive">
                <table class="table table-bordered">
                  <thead>
                  <tr>
                    <th>Gün</th>
                    <ng-container *ngFor="let day of dynamicDays; let i = index">
                      <th>{{i+1}}. GÜN</th>
                    </ng-container>
                  </tr>
                  </thead>
                  <tbody>
                  <tr>
                    <td>Randevuya Gelen</td>
                    <ng-container *ngFor="let day of dynamicDays; let i = index">
                      <td>
                        <span class="counter" style="cursor:pointer" (click)="exportTeamsForStat('joined', i)">
                          {{ filteredDynamicDriveStats['day'+(i+1)]?.joined || 0 }}
                        </span>
                      </td>
                    </ng-container>
                  </tr>
                  <tr>
                    <td>Randevuya Gelmeyen</td>
                    <ng-container *ngFor="let day of dynamicDays; let i = index">
                      <td>
                        <span class="counter" style="cursor:pointer" (click)="exportTeamsForStat('notJoined', i)">
                          {{ filteredDynamicDriveStats['day'+(i+1)]?.notJoined || 0 }}
                        </span>
                      </td>
                    </ng-container>
                  </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <div class="card mt-4">
            <div class="card-body">
              <div class="btn-group mb-2">
                <button type="button" class="btn btn-outline-primary" [class.active]="dynamicStatsType === 'elektromobil'" (click)="selectDynamicStatsType('elektromobil')">
                  Uluslararası Elektromobil
                </button>
                <button type="button" class="btn btn-outline-primary" [class.active]="dynamicStatsType === 'hidro'" (click)="selectDynamicStatsType('hidro')">
                  Uluslararası Hidromobil
                </button>
                <button type="button" class="btn btn-outline-primary" [class.active]="dynamicStatsType === 'lise'" (click)="selectDynamicStatsType('lise')">
                  Lise
                </button>
              </div>
              <div class="d-flex justify-content-between align-items-center mb-2">
                <h5>Gelişmiş İstatistik</h5>
                <button type="button" class="btn btn-success btn-sm" (click)="exportAdvancedStatsToExcel()">
                  <i class="bx bx-download"></i> Export Excel
                </button>
              </div>
              <div class="table-responsive">
                <table class="table table-bordered">
                  <thead>
                  <tr>
                    <th>İstatistik</th>
                    <th>Değer</th>
                  </tr>
                  </thead>
                  <tbody>
                  <tr>
                    <td>Teknik Kontrole Gelen Takım Sayısı</td>
                    <td>
                      <span class="counter" style="cursor:pointer" (click)="exportTeamsForStat('attendedTeams')">
                        {{ filteredDynamicDriveAdvancedStats.attendedTeams || 0 }}
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td>Teknik Kontrole Hiç Gelmeyen Takım Sayısı</td>
                    <td>
                      <span class="counter" style="cursor:pointer" (click)="exportTeamsForStat('notAttendedTeams')">
                        {{ filteredDynamicDriveAdvancedStats.notAttendedTeams || 0 }}
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td>Teknik Kontrole Gelip Dinamik Sürüş Testini Geçen Takım Sayısı</td>
                    <td>
                      <span class="counter" style="cursor:pointer" (click)="exportTeamsForStat('passedTeams')">
                        {{ filteredDynamicDriveAdvancedStats.passedTeams || 0 }}
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td>Teknik Kontrole Gelip Dinamik Sürüş Testini Geçemeyen Takım Sayısı</td>
                    <td>
                      <span class="counter" style="cursor:pointer" (click)="exportTeamsForStat('failedTeams')">
                        {{ filteredDynamicDriveAdvancedStats.failedTeams || 0 }}
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td>Teknik Kontrol Aşamasından Başarılı Olan Takım Sayısı</td>
                    <td>
                      <span class="counter" style="cursor:pointer" (click)="exportTeamsForStat('techSuccessfulTeams')">
                        {{ filteredDynamicDriveAdvancedStats.techSuccessfulTeams || 0 }}
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td>Teknik Kontrol Aşamasından Başarılı Olmayan Takım Sayısı</td>
                    <td>
                      <span class="counter" style="cursor:pointer" (click)="exportTeamsForStat('techUnsuccessfulTeams')">
                        {{ filteredDynamicDriveAdvancedStats.techUnsuccessfulTeams || 0 }}
                      </span>
                    </td>
                  </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <div class="card mt-4">
            <div class="card-body">
              <div class="btn-group mb-2">
                <button type="button" class="btn btn-outline-primary" [class.active]="currentEvalStatsType === 'elektromobil'" (click)="selectEvaluationStatsType('elektromobil')">
                  Uluslararası Elektromobil
                </button>
                <button type="button" class="btn btn-outline-primary" [class.active]="currentEvalStatsType === 'hidro'" (click)="selectEvaluationStatsType('hidro')">
                  Uluslararası Hidromobil
                </button>
                <button type="button" class="btn btn-outline-primary" [class.active]="currentEvalStatsType === 'lise'" (click)="selectEvaluationStatsType('lise')">
                  Lise
                </button>
              </div>
              <div class="d-flex justify-content-between align-items-center mb-2">
                <h5>
                  Değerlendirme İstatistik Tablosu -
                  {{ currentEvalStatsType === 'elektromobil' ? 'Uluslararası Elektromobil' : (currentEvalStatsType === 'hidro' ? 'Uluslararası Hidromobil' : 'Lise') }}
                </h5>
                <button type="button" class="btn btn-success btn-sm" (click)="exportEvaluationStatsToExcel(filteredEvalStatsData, currentEvalStatsType)">
                  <i class="bx bx-download"></i> Export Excel
                </button>
              </div>
              <div class="mb-2">
                <button type="button" class="btn btn-primary btn-sm" (click)="applyEvalStatsFilters()">Filtreyi Uygula</button>
                <button type="button" class="btn btn-secondary btn-sm" (click)="clearEvalStatsFilters()">Filtreleri Temizle</button>
              </div>
            </div>
          </div>
          <div class="table-responsive-mobile table-container" *ngIf="evalStatsVisible">
            <ng-container *ngIf="filteredEvalStatsData.length > 0; else noEvalStatsDataTpl">
              <p-table [value]="filteredEvalStatsData" [rows]="15" [paginator]="true" [loading]="loading" styleClass="p-datatable-striped">
                <ng-template pTemplate="header">
                  <tr>
                    <th *ngFor="let col of evalStatsConfig.headers; let i = index" style="text-align: center;">
                      {{ col }}
                      <span class="sortable" (click)="sortEvalStats(evalStatsConfig.columns[i])">
                        <i [ngClass]="getEvalStatsSortIcon(evalStatsConfig.columns[i])"></i>
                      </span>
                    </th>
                  </tr>
                  <tr>
                    <th *ngFor="let col of evalStatsConfig.columns">
                      <input type="text" name="{{col}}" class="form-control form-control-sm" [(ngModel)]="evalStatsFilters[col]" (keyup.enter)="applyEvalStatsFilters()">
                    </th>
                  </tr>
                </ng-template>
                <ng-template pTemplate="body" let-row>
                  <tr>
                    <td style="text-align: center;">{{ row.criteria_id }}</td>
                    <td style="text-align: center;">{{ row.criteria_category }}</td>
                    <td style="text-align: center;">{{ row.criteria_subject }}</td>
                    <td style="text-align: center;">{{ row.criteria_total }}</td>
                    <td style="text-align: center;">{{ row.criteria_bos }}</td>
                    <td style="text-align: center;">{{ row.criteria_uygun }}</td>
                    <td style="text-align: center;">{{ row.criteria_kusurlu }}</td>
                    <td style="text-align: center;">{{ row.criteria_disk }}</td>
                  </tr>
                </ng-template>
                <ng-template pTemplate="emptymessage">
                  <tr>
                    <td colspan="100%" class="text-center">Veri bulunamadı</td>
                  </tr>
                </ng-template>
              </p-table>
            </ng-container>
            <ng-template #noEvalStatsDataTpl>
              <table class="table table-bordered mb-0">
                <thead>
                  <tr>
                    <th *ngFor="let col of evalStatsConfig.headers" style="text-align: center;">{{ col }}</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td [attr.colspan]="evalStatsConfig.headers.length" class="text-center py-4 text-muted" style="font-size: 1.1em;">
                      <i class="bx bx-info-circle" style="font-size: 1.5em;"></i><br>
                      Veri bulunamadı
                    </td>
                  </tr>
                </tbody>
              </table>
            </ng-template>
          </div>
          <!-- GDATA TABLE START -->
          <div class="card mt-4">
            <div class="card-body">
              <div class="row mb-3 align-items-center">
                <div class="col d-flex justify-content-between">
                  <div>
                    <h2>Takım Gelişmiş Kontrol Tablosu</h2>
                  </div>
                  <div class="col d-flex justify-content-end" style="gap: 1rem;">
                    <button class="btn btn-primary btn-sm" (click)="applyGDataTableFilters()">Ara</button>
                    <button class="btn btn-secondary btn-sm" (click)="clearGDataTableFilters()">Filtreyi Temizle</button>
                    <button type="button" class="btn btn-success" (click)="exportGDataTableToExcel()">
                      <i class="bx bx-download"></i> Export Excel
                    </button>
                  </div>
                </div>
              </div>
              <!-- Tabloyu yatay kaydırılabilir yap -->
              <div style="overflow-x: auto; width: 100%;">
                <div class="table-responsive-mobile table-container" *ngIf="!gDataTableLoading">
                  <p-table [value]="gDataTableFilteredRows" [rows]="15" [paginator]="true" [loading]="gDataTableLoading" styleClass="p-datatable-striped">
                    <ng-template pTemplate="header">
                      <tr>
                        <th *ngFor="let col of gDataTableColumns"
                            [style.width]="getGDataColWidth(col)"
                            style="text-align: center; white-space: nowrap;">
                          <ng-container [ngSwitch]="col">
                            <span *ngSwitchCase="'team_id'">Takım ID</span>
                            <span *ngSwitchCase="'team_name'">Takım Adı</span>
                            <span *ngSwitchCase="'vehicle_category'">Araç Kategorisi</span>
                            <span *ngSwitchCase="'school_type_name'">Kurum Tipi</span>
                            <span *ngSwitchCase="'dynamic_drive_result'">Dinamik Sürüş</span>
                            <span *ngSwitchCase="'eval_status'">Eval Status</span>
                            <span *ngSwitchDefault>{{ col }}</span>
                          </ng-container>
                          <span class="sortable" (click)="onGDataTableSort(col)">
                            <i [ngClass]="getGDataTableSortIcon(col)"></i>
                          </span>
                        </th>
                      </tr>
                      <tr>
                        <th *ngFor="let col of gDataTableColumns"
                            [style.width]="getGDataColWidth(col)">
                          <!-- Araç Kategorisi dropdown -->
                          <ng-container *ngIf="col === 'vehicle_category'">
                            <select class="form-control form-control-sm"
                              [(ngModel)]="gDataTableFilters[col]"
                              (change)="applyGDataTableFilters()">
                              <option value="">Tümü</option>
                              <option *ngFor="let opt of gDataVehicleCategoryOptions" [value]="opt">{{ opt }}</option>
                            </select>
                          </ng-container>
                          <!-- Kurum Tipi dropdown -->
                          <ng-container *ngIf="col === 'school_type_name'">
                            <select class="form-control form-control-sm"
                              [(ngModel)]="gDataTableFilters[col]"
                              (change)="applyGDataTableFilters()">
                              <option value="">Tümü</option>
                              <option *ngFor="let opt of gDataSchoolTypeOptions" [value]="opt">{{ opt }}</option>
                            </select>
                          </ng-container>
                          <!-- Dinamik Sürüş dropdown -->
                          <ng-container *ngIf="col === 'dynamic_drive_result'">
                            <select class="form-control form-control-sm"
                              [(ngModel)]="gDataTableFilters[col]"
                              (change)="applyGDataTableFilters()">
                              <option value="">Tümü</option>
                              <option *ngFor="let opt of gDataDynamicDriveOptions" [value]="opt">{{ opt }}</option>
                            </select>
                          </ng-container>
                          <!-- Eval Status dropdown -->
                          <ng-container *ngIf="col === 'eval_status'">
                            <select class="form-control form-control-sm"
                              [(ngModel)]="gDataTableFilters[col]"
                              (change)="applyGDataTableFilters()">
                              <option value="">Tümü</option>
                              <option *ngFor="let opt of gDataEvalStatusOptions" [value]="opt">{{ opt }}</option>
                            </select>
                          </ng-container>
                          <!-- gData sütunları için dropdown -->
                          <ng-container *ngIf="gDataColumnOptions[col]">
                            <select class="form-control form-control-sm"
                              [(ngModel)]="gDataTableFilters[col]"
                              (change)="applyGDataTableFilters()">
                              <option value="">Tümü</option>
                              <option *ngFor="let opt of gDataColumnOptions[col]" [value]="opt">{{ opt }}</option>
                            </select>
                          </ng-container>
                          <!-- Diğer sütunlar için sadece input, placeholder yok -->
                          <ng-container *ngIf="!['vehicle_category','school_type_name','dynamic_drive_result','eval_status'].includes(col) && !gDataColumnOptions[col]">
                            <input type="text" class="form-control form-control-sm"
                              [(ngModel)]="gDataTableFilters[col]"
                              (keyup.enter)="applyGDataTableFilters()">
                          </ng-container>
                        </th>
                      </tr>
                    </ng-template>
                    <ng-template pTemplate="body" let-row>
                      <tr>
                        <td *ngFor="let col of gDataTableColumns"
                            class="text-center"
                            [style.width]="getGDataColWidth(col)"
                            style="white-space: nowrap;">
                          {{ row[col] }}
                        </td>
                      </tr>
                    </ng-template>
                    <ng-template pTemplate="emptymessage">
                      <tr>
                        <td [attr.colspan]="gDataTableColumns.length" class="text-center py-4 text-muted" style="font-size: 1.1em;">
                          <i class="bx bx-info-circle" style="font-size: 1.5em;"></i><br>
                          Veri bulunamadı
                        </td>
                      </tr>
                    </ng-template>
                  </p-table>
                </div>
                <div *ngIf="gDataTableLoading" class="text-center py-4">
                  <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                  Yükleniyor...
                </div>
              </div>
            </div>
          </div>
          <!-- GDATA TABLE END -->
          <!-- ORIGINAL DESIGNED STATS TABLE START -->
          <div class="card mt-4">
            <div class="card-body">
              <div class="row mb-3 align-items-center">
                <div class="col d-flex justify-content-between">
                  <div>
                    <h2>Takım Orijinal Tasarım Tablosu</h2>
                  </div>
                  <div class="col d-flex justify-content-end" style="gap: 1rem;">
                    <button class="btn btn-primary btn-sm" (click)="applyOriginalDesignedStatsFilters()">Ara</button>
                    <button class="btn btn-secondary btn-sm" (click)="clearOriginalDesignedStatsFilters()">Filtreyi Temizle</button>
                    <button type="button" class="btn btn-success" (click)="exportOriginalDesignedStatsToExcel()">
                      <i class="bx bx-download"></i> Export Excel
                    </button>
                  </div>
                </div>
              </div>
              <div style="overflow-x: auto; width: 100%;">
                <div class="table-responsive-mobile table-container" *ngIf="!originalDesignedStatsLoading">
                  <p-table [value]="originalDesignedStatsFilteredData" [rows]="15" [paginator]="true" [loading]="originalDesignedStatsLoading" styleClass="p-datatable-striped">
                    <ng-template pTemplate="header">
                      <tr>
                        <th *ngFor="let col of originalDesignedStatsTeamColumns"
                            [style.width]="getOriginalStatsColWidth(col.key)"
                            style="text-align: center; white-space: nowrap; min-width: 120px;">
                          {{ col.label }}
                          <span class="sortable" (click)="onOriginalStatsSort(col.key)">
                            <i [ngClass]="getOriginalStatsSortIcon(col.key)"></i>
                          </span>
                        </th>
                        <th *ngFor="let header of originalDesignedStatsHeaders"
                            [style.width]="getOriginalStatsColWidth(header)"
                            style="text-align: center; white-space: nowrap; min-width: 120px;">
                          {{ header }}
                          <span class="sortable" (click)="onOriginalStatsSort(header)">
                            <i [ngClass]="getOriginalStatsSortIcon(header)"></i>
                          </span>
                        </th>
                      </tr>
                      <tr>
                        <th *ngFor="let col of originalDesignedStatsTeamColumns" [style.width]="getOriginalStatsColWidth(col.key)">
                          <ng-container [ngSwitch]="col.key">
                            <ng-container *ngSwitchCase="'school_type'">
                              <select class="form-control form-control-sm" [(ngModel)]="originalDesignedStatsFilters[col.key]" (change)="applyOriginalDesignedStatsFilters()">
                                <option value="">Tümü</option>
                                <option *ngFor="let opt of originalDesignedStatsSchoolTypeOptions" [value]="opt">{{ opt }}</option>
                              </select>
                            </ng-container>
                            <ng-container *ngSwitchCase="'vehicle_category'">
                              <select class="form-control form-control-sm" [(ngModel)]="originalDesignedStatsFilters[col.key]" (change)="applyOriginalDesignedStatsFilters()">
                                <option value="">Tümü</option>
                                <option *ngFor="let opt of originalDesignedStatsVehicleCategoryOptions" [value]="opt">{{ opt }}</option>
                              </select>
                            </ng-container>
                            <ng-container *ngSwitchDefault>
                              <input type="text" class="form-control form-control-sm" [(ngModel)]="originalDesignedStatsFilters[col.key]" (keyup.enter)="applyOriginalDesignedStatsFilters()">
                            </ng-container>
                          </ng-container>
                        </th>
                        <th *ngFor="let header of originalDesignedStatsHeaders" [style.width]="getOriginalStatsColWidth(header)">
                          <ng-container *ngIf="originalDesignedStatsHeaderOptions[header]?.length > 0; else textInput">
                            <select class="form-control form-control-sm" [(ngModel)]="originalDesignedStatsFilters[header]" (change)="applyOriginalDesignedStatsFilters()">
                              <option value="">Tümü</option>
                              <option *ngFor="let opt of originalDesignedStatsHeaderOptions[header]" [value]="opt">{{ opt }}</option>
                            </select>
                          </ng-container>
                          <ng-template #textInput>
                            <input type="text" class="form-control form-control-sm" [(ngModel)]="originalDesignedStatsFilters[header]" (keyup.enter)="applyOriginalDesignedStatsFilters()">
                          </ng-template>
                        </th>
                      </tr>
                    </ng-template>
                    <ng-template pTemplate="body" let-row>
                      <tr>
                        <td *ngFor="let col of originalDesignedStatsTeamColumns" class="text-center" [style.width]="getOriginalStatsColWidth(col.key)" style="white-space: nowrap; min-width: 120px;">
                          <ng-container [ngSwitch]="col.key">
                            <ng-container *ngSwitchCase="'school_type'">{{ getSchoolTypeDisplay(row['school_type']) }}</ng-container>
                            <ng-container *ngSwitchCase="'vehicle_category'">{{ getVehicleCategoryDisplay(row['vehicle_category']) }}</ng-container>
                            <ng-container *ngSwitchDefault>{{ row[col.key] }}</ng-container>
                          </ng-container>
                        </td>
                        <td *ngFor="let header of originalDesignedStatsHeaders" class="text-center" [style.width]="getOriginalStatsColWidth(header)" style="white-space: nowrap; min-width: 120px;">{{ row[header] }}</td>
                      </tr>
                    </ng-template>
                    <ng-template pTemplate="emptymessage">
                      <tr>
                        <td [attr.colspan]="originalDesignedStatsTeamColumns.length + originalDesignedStatsHeaders.length" class="text-center py-4 text-muted" style="font-size: 1.1em;">
                          <i class="bx bx-info-circle" style="font-size: 1.5em;"></i><br>
                          Veri bulunamadı
                        </td>
                      </tr>
                    </ng-template>
                  </p-table>
                </div>
                <div *ngIf="originalDesignedStatsLoading" class="text-center py-4">
                  <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                  Yükleniyor...
                </div>
              </div>
            </div>
          </div>
          <!-- ORIGINAL DESIGNED STATS TABLE END -->
          <!-- LOCAL DESIGNED STATS TABLE START -->
          <div class="card mt-4">
            <div class="card-body">
              <div class="row mb-3 align-items-center">
                <div class="col d-flex justify-content-between">
                  <div>
                    <h2>Takım Yerli Tasarım Tablosu</h2>
                  </div>
                  <div class="col d-flex justify-content-end" style="gap: 1rem;">
                    <button class="btn btn-primary btn-sm" (click)="applyLocalDesignedStatsFilters()">Ara</button>
                    <button class="btn btn-secondary btn-sm" (click)="clearLocalDesignedStatsFilters()">Filtreyi Temizle</button>
                    <button type="button" class="btn btn-success" (click)="exportLocalDesignedStatsToExcel()">
                      <i class="bx bx-download"></i> Export Excel
                    </button>
                  </div>
                </div>
              </div>
              <div style="overflow-x: auto; width: 100%;">
                <div class="table-responsive-mobile table-container" *ngIf="!localDesignedStatsLoading">
                  <p-table [value]="localDesignedStatsFilteredData" [rows]="15" [paginator]="true" [loading]="localDesignedStatsLoading" styleClass="p-datatable-striped">
                    <ng-template pTemplate="header">
                      <tr>
                        <th *ngFor="let col of localDesignedStatsTeamColumns"
                            [style.width]="getLocalStatsColWidth(col.key)"
                            style="text-align: center; white-space: nowrap; min-width: 120px;">
                          {{ col.label }}
                          <span class="sortable" (click)="onLocalStatsSort(col.key)">
                            <i [ngClass]="getLocalStatsSortIcon(col.key)"></i>
                          </span>
                        </th>
                        <th *ngFor="let header of localDesignedStatsHeaders"
                            [style.width]="getLocalStatsColWidth(header)"
                            style="text-align: center; white-space: nowrap; min-width: 120px;">
                          {{ header }}
                          <span class="sortable" (click)="onLocalStatsSort(header)">
                            <i [ngClass]="getLocalStatsSortIcon(header)"></i>
                          </span>
                        </th>
                      </tr>
                      <tr>
                        <th *ngFor="let col of localDesignedStatsTeamColumns" [style.width]="getLocalStatsColWidth(col.key)">
                          <ng-container [ngSwitch]="col.key">
                            <ng-container *ngSwitchCase="'school_type'">
                              <select class="form-control form-control-sm" [(ngModel)]="localDesignedStatsFilters[col.key]" (change)="applyLocalDesignedStatsFilters()">
                                <option value="">Tümü</option>
                                <option *ngFor="let opt of localDesignedStatsSchoolTypeOptions" [value]="opt">{{ opt }}</option>
                              </select>
                            </ng-container>
                            <ng-container *ngSwitchCase="'vehicle_category'">
                              <select class="form-control form-control-sm" [(ngModel)]="localDesignedStatsFilters[col.key]" (change)="applyLocalDesignedStatsFilters()">
                                <option value="">Tümü</option>
                                <option *ngFor="let opt of localDesignedStatsVehicleCategoryOptions" [value]="opt">{{ opt }}</option>
                              </select>
                            </ng-container>
                            <ng-container *ngSwitchDefault>
                              <input type="text" class="form-control form-control-sm" [(ngModel)]="localDesignedStatsFilters[col.key]" (keyup.enter)="applyLocalDesignedStatsFilters()">
                            </ng-container>
                          </ng-container>
                        </th>
                        <th *ngFor="let header of localDesignedStatsHeaders" [style.width]="getLocalStatsColWidth(header)">
                          <ng-container *ngIf="localDesignedStatsHeaderOptions[header]?.length > 0; else textInputLocal">
                            <select class="form-control form-control-sm" [(ngModel)]="localDesignedStatsFilters[header]" (change)="applyLocalDesignedStatsFilters()">
                              <option value="">Tümü</option>
                              <option *ngFor="let opt of localDesignedStatsHeaderOptions[header]" [value]="opt">{{ opt }}</option>
                            </select>
                          </ng-container>
                          <ng-template #textInputLocal>
                            <input type="text" class="form-control form-control-sm" [(ngModel)]="localDesignedStatsFilters[header]" (keyup.enter)="applyLocalDesignedStatsFilters()">
                          </ng-template>
                        </th>
                      </tr>
                    </ng-template>
                    <ng-template pTemplate="body" let-row>
                      <tr>
                        <td *ngFor="let col of localDesignedStatsTeamColumns" class="text-center" [style.width]="getLocalStatsColWidth(col.key)" style="white-space: nowrap; min-width: 120px;">
                          <ng-container [ngSwitch]="col.key">
                            <ng-container *ngSwitchCase="'school_type'">{{ getSchoolTypeDisplay(row['school_type']) }}</ng-container>
                            <ng-container *ngSwitchCase="'vehicle_category'">{{ getVehicleCategoryDisplay(row['vehicle_category']) }}</ng-container>
                            <ng-container *ngSwitchDefault>{{ row[col.key] }}</ng-container>
                          </ng-container>
                        </td>
                        <td *ngFor="let header of localDesignedStatsHeaders" class="text-center" [style.width]="getLocalStatsColWidth(header)" style="white-space: nowrap; min-width: 120px;">{{ row[header] }}</td>
                      </tr>
                    </ng-template>
                    <ng-template pTemplate="emptymessage">
                      <tr>
                        <td [attr.colspan]="localDesignedStatsTeamColumns.length + localDesignedStatsHeaders.length" class="text-center py-4 text-muted" style="font-size: 1.1em;">
                          <i class="bx bx-info-circle" style="font-size: 1.5em;"></i><br>
                          Veri bulunamadı
                        </td>
                      </tr>
                    </ng-template>
                  </p-table>
                </div>
                <div *ngIf="localDesignedStatsLoading" class="text-center py-4">
                  <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                  Yükleniyor...
                </div>
              </div>
            </div>
          </div>
          <!-- LOCAL DESIGNED STATS TABLE END -->
        </div>
      </div>
    </div>
  </div>
</div>
