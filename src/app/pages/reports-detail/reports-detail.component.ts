import { Component, OnInit } from '@angular/core';
import { EvaluationService } from '../../core/services/evaluation.service';
import * as XLSX from 'xlsx';
import { AppointmentService } from '../../core/services/appointment.service';
interface TableColumnFilter { [key: string]: string; }
interface TableConfig { columns: string[]; headers: string[]; excelSheetName: string; getRowExportData: (row: any) => any; }
@Component({
  selector: 'app-reports-detail',
  templateUrl: './reports-detail.component.html',
  styleUrls: ['./reports-detail.component.scss']
})
export class ReportsDetailComponent implements OnInit {

  constructor(private evalService: EvaluationService, private appointmentService: AppointmentService) {
    this.currentUser = JSON.parse(localStorage.getItem('currentUser'));
    this.dynamicDriveConfig.columns.forEach(col => this.dynamicDriveFilters[col] = '');

    this.evalStatsConfig.columns.forEach(col => {
      this.evalStatsFilters[col] = '';
    });
  }


  private static originalDesignValueMap: { [key: string]: string } = {
    approve: 'Geçti',
    empty: 'Boş',
    disqualified: 'Diskalifiye',
    defective: 'Arızalı'
  };
  private static originalDesignReverseValueMap: { [key: string]: string } = {
    Geçti: 'approve',
    Boş: 'empty',
    Diskalifiye: 'disqualified',
    Arızalı: 'defective'
  };
  dynamicDriveData: any[] = [];
  dynamicDriveFiltered: any[] = [];
  dynamicDriveFilters: TableColumnFilter = {};
  dynamicDays: string[] = [];
  dynamicDriveConfig: TableConfig = {
    columns: ['vehicle_number', 'team_id', 'team_name', 'school_type_name', 'vehicle_category', 'eval_date', 'evaluation_status'],
    headers: ['Araç No', 'ID', 'Takım', 'Liseler Arası/Uluslararası', 'Yarışma Kategorisi', 'Eval Date', 'Evaluation Status'],
    excelSheetName: 'Dinamik Sürüş',
    getRowExportData: (item) => {
      const rowData: any = {
        'Araç No': item.team?.vehicle_number || '',
        ID: item.team?.team_id || '',
        Takım: item.team?.team_name || '',
        'Liseler Arası/Uluslararası': ReportsDetailComponent.getSchoolTypeDisplay(item.team?.school_type_name),
        'Yarışma Kategorisi': ReportsDetailComponent.getCategoryDisplay(item.team?.vehicle_category),
        'Eval Date': item.eval_date || '',
        'Evaluation Status': item.evaluation_status || ''
      };
      this.dynamicDays.forEach((_, index) => {
        const key = `day${index + 1}_dynamic`;
        const header = `${index + 1}. Gün dinamik sürüş`;
        rowData[header] = item[key];
      });
      return rowData;
    }
  };
  first = 0;
  rows = 15;
  loading = false;
  currentUser: { user: { role: string | string[]; } };
  schoolTypeOptions: string[] = [];
  vehicleCategoryOptions: string[] = [];
  evaluationStatusOptions: string[] = [];
  dynamicDriveSortField = '';
  dynamicDriveSortOrder: 'asc' | 'desc' | '' = '';
  dynamicDriveStats: any = {};
  dynamicDriveAdvancedStats: any = {};

  evalStatsConfig: TableConfig = {
    // tslint:disable-next-line:max-line-length
    columns: ['criteria_id', 'criteria_category', 'criteria_subject', 'criteria_total', 'criteria_bos', 'criteria_uygun', 'criteria_kusurlu', 'criteria_disk'],
    headers: ['Kriter ID', 'Kriter Kategorisi', 'Kriter', 'Toplam', 'BOŞ', 'UYGUN', 'KUSURLU', 'DİSK'],
    excelSheetName: 'Değerlendirme İstatistik',
    getRowExportData: (item) => {
      return {
        'Kriter ID': item.criteria_id,
        'Kriter Kategorisi': item.criteria_category,
        Kriter: item.criteria_subject,
        Toplam: item.criteria_total,
        BOŞ: item.criteria_bos,
        UYGUN: item.criteria_uygun,
        KUSURLU: item.criteria_kusurlu,
        DİSK: item.criteria_disk
      };
    }
  };

  evalStatsData: any[] = [];
  filteredEvalStatsData: any[] = [];
  evalStatsFilters: TableColumnFilter = {};
  evalStatsSortField = '';
  evalStatsSortOrder: 'asc' | 'desc' | '' = '';
  currentEvalStatsType = 'elektromobil';
  evalStatsVisible = false;
  dynamicDriveFilterPassedOnly = false;

  dynamicStatsType: 'elektromobil' | 'hidro' | 'lise' = 'elektromobil';
  filteredDynamicDriveStats: any = {};
  filteredDynamicDriveAdvancedStats: any = {};


  gDataTableLoading = true;
  gDataTableColumns: string[] = [];
  gDataTableRows: any[] = [];
  gDataTableFilteredRows: any[] = [];
  gDataTableFilters: { [key: string]: string } = {};
  gDataTableSortField = '';
  gDataTableSortOrder: 'asc' | 'desc' | '' = '';


  gDataColWidths: { [key: string]: number } = {};


  gDataVehicleCategoryOptions: string[] = [];
  gDataSchoolTypeOptions: string[] = [];
  gDataDynamicDriveOptions: string[] = ['Geçti', 'Kaldı'];
  gDataEvalStatusOptions: string[] = [];
  gDataColumnOptions: { [key: string]: string[] } = {};

  originalDesignedStatsData: any[] = [];
  originalDesignedStatsHeaders: string[] = [];
  originalDesignedStatsLoading = false;
  originalDesignedStatsTeamColumns = [
    { key: 'team_name', label: 'Takım Adı' },
    { key: 'personel', label: 'Personel' },
    { key: 'school_type', label: 'Kurum Tipi' },
    { key: 'vehicle_category', label: 'Araç Tipi' }
  ];

  originalDesignedStatsFilters: { [key: string]: string } = {};
  originalDesignedStatsSortField = '';
  originalDesignedStatsSortOrder: 'asc' | 'desc' | '' = '';
  originalDesignedStatsFilteredData: any[] = [];


  originalDesignedStatsSchoolTypeOptions: string[] = [];
  originalDesignedStatsVehicleCategoryOptions: string[] = [];

  originalDesignedStatsHeaderOptions: { [key: string]: string[] } = {};

  localDesignedStatsData: any[] = [];
  localDesignedStatsHeaders: string[] = [];
  localDesignedStatsLoading = false;
  localDesignedStatsTeamColumns = [
    { key: 'team_name', label: 'Takım Adı' },
    { key: 'personel', label: 'Personel' },
    { key: 'school_type', label: 'Kurum Tipi' },
    { key: 'vehicle_category', label: 'Araç Tipi' }
  ];
  localDesignedStatsFilters: { [key: string]: string } = {};
  localDesignedStatsSortField = '';
  localDesignedStatsSortOrder: 'asc' | 'desc' | '' = '';
  localDesignedStatsFilteredData: any[] = [];
  localDesignedStatsSchoolTypeOptions: string[] = [];
  localDesignedStatsVehicleCategoryOptions: string[] = [];
  localDesignedStatsHeaderOptions: { [key: string]: string[] } = {};

  static getSchoolTypeDisplay(type: string): string {
    if (!type) { return ''; }
    if (type.toLowerCase() === 'high school') { return 'Liseler Arası'; }
    if (type.toLowerCase() === 'international') { return 'Uluslararası'; }
    return type;
  }
  static getCategoryDisplay(category: string): string {
    if (!category) { return ''; }
    if (category.toLowerCase() === 'hydromobile') { return 'Hidromobil'; }
    if (category.toLowerCase() === 'electromobile') { return 'Elektromobil'; }
    return category;
  }


  clearTableFilters(filters: TableColumnFilter, config: TableConfig): void {
    config.columns.forEach(col => filters[col] = '');
  }
  ngOnInit(): void {
    if (this.currentUser.user.role?.includes('evaluation') || this.currentUser.user.role?.includes('superadmin')) {
      this.loadDynamicDriveData();
      this.loadEvaluationStatsData('elektromobil');
    }
    this.dynamicStatsType = 'elektromobil';
    this.filterDynamicDriveStats();
    this.loadOriginalDesignedStats();
    this.loadLocalDesignedStats();
  }

  loadOriginalDesignedStats(): void {
    this.originalDesignedStatsLoading = true;
    this.evalService.getSingleBaseGrouped('Orijinal Tasarım').subscribe((res: any) => {
      const data = res['data'] || [];
      const headerSet = new Set<string>();
      const headerValueSets: { [key: string]: Set<string> } = {};
      data.forEach((team: any) => {
        (team.original_design || []).forEach((crit: any) => {
          const header = `${crit.criteria_category_name} ${crit.criteria_subject}`;
          headerSet.add(header);
          if (!headerValueSets[header]) { headerValueSets[header] = new Set<string>(); }
          if (crit.value) { headerValueSets[header].add(crit.value); }
        });
      });
      this.originalDesignedStatsHeaders = Array.from(headerSet);
      this.originalDesignedStatsData = data.map((team: any) => {
        const row: any = {
          team_name: team.team_name || '',
          personel: team.updated_by ? ((team.updated_by.first_name || '') + ' ' + (team.updated_by.last_name || '')).trim() : '',
          school_type: team.school_type || '',
          vehicle_category: team.vehicle_category || ''
        };
        (team.original_design || []).forEach((crit: any) => {
          const header = `${crit.criteria_category_name} ${crit.criteria_subject}`;

          row[header] = ReportsDetailComponent.originalDesignValueMap[crit.value] || crit.value || '';
        });
        this.originalDesignedStatsHeaders.forEach(h => {
          if (!(h in row)) { row[h] = ''; }
        });
        return row;
      });

      this.originalDesignedStatsHeaderOptions = {};
      this.originalDesignedStatsHeaders.forEach(h => {
        const opts = Array.from(headerValueSets[h] || []).map(v => ReportsDetailComponent.originalDesignValueMap[v] || v).filter(Boolean);
        this.originalDesignedStatsHeaderOptions[h] = opts;
      });
      this.originalDesignedStatsTeamColumns.forEach(col => this.originalDesignedStatsFilters[col.key] = '');
      this.originalDesignedStatsHeaders.forEach(h => this.originalDesignedStatsFilters[h] = '');
      this.originalDesignedStatsFilteredData = [...this.originalDesignedStatsData];
      // tslint:disable-next-line:max-line-length
      this.originalDesignedStatsSchoolTypeOptions = Array.from(new Set(this.originalDesignedStatsData.map(row => this.getSchoolTypeDisplay(row.school_type)).filter(val => !!val)));
      // tslint:disable-next-line:max-line-length
      this.originalDesignedStatsVehicleCategoryOptions = Array.from(new Set(this.originalDesignedStatsData.map(row => this.getVehicleCategoryDisplay(row.vehicle_category)).filter(val => !!val)));
      this.originalDesignedStatsLoading = false;
    }, _ => { this.originalDesignedStatsLoading = false; });
  }

  loadLocalDesignedStats(): void {
    this.localDesignedStatsLoading = true;
    this.evalService.getSingleBaseGrouped('Yerli').subscribe((res: any) => {
      const data = res['data'] || [];
      const headerSet = new Set<string>();
      const headerValueSets: { [key: string]: Set<string> } = {};
      data.forEach((team: any) => {
        (team.original_design || []).forEach((crit: any) => {
          let header = '';
          if (
            crit.criteria_sub_category_name &&
            crit.criteria_subject &&
            crit.criteria_subject.includes(crit.criteria_sub_category_name)
          ) {
            if (
              crit.criteria_category_name &&
              crit.criteria_subject.includes(crit.criteria_category_name)
            ) {
              header = crit.criteria_subject;
            } else {
              header = `${crit.criteria_category_name} ${crit.criteria_subject}`;
            }
          } else {
            header = `${crit.criteria_sub_category_name} ${crit.criteria_subject}`;
          }
          headerSet.add(header);
          if (!headerValueSets[header]) { headerValueSets[header] = new Set<string>(); }
          if (crit.value) { headerValueSets[header].add(crit.value); }
        });
      });
      this.localDesignedStatsHeaders = Array.from(headerSet);
      this.localDesignedStatsData = data.map((team: any) => {
        const row: any = {
          team_name: team.team_name || '',
          personel: team.updated_by ? ((team.updated_by.first_name || '') + ' ' + (team.updated_by.last_name || '')).trim() : '',
          school_type: team.school_type || '',
          vehicle_category: team.vehicle_category || ''
        };
        (team.original_design || []).forEach((crit: any) => {
          let header = '';
          if (
            crit.criteria_sub_category_name &&
            crit.criteria_subject &&
            crit.criteria_subject.includes(crit.criteria_sub_category_name)
          ) {
            if (
              crit.criteria_category_name &&
              crit.criteria_subject.includes(crit.criteria_category_name)
            ) {
              header = crit.criteria_subject;
            } else {
              header = `${crit.criteria_category_name} ${crit.criteria_subject}`;
            }
          } else {
            header = `${crit.criteria_sub_category_name} ${crit.criteria_subject}`;
          }
          row[header] = ReportsDetailComponent.originalDesignValueMap[crit.value] || crit.value || '';
        });
        this.localDesignedStatsHeaders.forEach(h => {
          if (!(h in row)) { row[h] = ''; }
        });
        return row;
      });
      this.localDesignedStatsHeaderOptions = {};
      this.localDesignedStatsHeaders.forEach(h => {
        const opts = Array.from(headerValueSets[h] || []).map(v => ReportsDetailComponent.originalDesignValueMap[v] || v).filter(Boolean);
        this.localDesignedStatsHeaderOptions[h] = opts;
      });
      this.localDesignedStatsTeamColumns.forEach(col => this.localDesignedStatsFilters[col.key] = '');
      this.localDesignedStatsHeaders.forEach(h => this.localDesignedStatsFilters[h] = '');
      this.localDesignedStatsFilteredData = [...this.localDesignedStatsData];
      // tslint:disable-next-line:max-line-length
      this.localDesignedStatsSchoolTypeOptions = Array.from(new Set(this.localDesignedStatsData.map(row => this.getSchoolTypeDisplay(row.school_type)).filter(val => !!val)));
      // tslint:disable-next-line:max-line-length
      this.localDesignedStatsVehicleCategoryOptions = Array.from(new Set(this.localDesignedStatsData.map(row => this.getVehicleCategoryDisplay(row.vehicle_category)).filter(val => !!val)));
      this.localDesignedStatsLoading = false;
    }, _ => { this.localDesignedStatsLoading = false; });
  }

  loadDynamicDriveData() {
    this.loading = true;
    this.gDataTableLoading = true;
    this.appointmentService.getAppointments(null).subscribe(appointments => {
      const appointmentsArray = appointments['data'] || [];
      // tslint:disable-next-line:max-line-length
      const uniqueDays = Array.from(new Set(appointmentsArray.map((app: { start_time: string; }) => app.start_time.substring(0, 10)))).sort();
      this.dynamicDays = uniqueDays as string[];
      const dynamicDriveColumns = this.dynamicDays.map((_, index) => `day${index + 1}_dynamic`);
      const dynamicDriveHeaders = this.dynamicDays.map((_, index) => `${index + 1}. Gün dinamik sürüş`);
      this.dynamicDriveConfig.columns = ['vehicle_number', 'team_id', 'team_name', 'school_type_name', 'vehicle_category', 'eval_date', 'evaluation_status', ...dynamicDriveColumns];
      this.dynamicDriveConfig.headers = ['Araç No', 'ID', 'Takım', 'Liseler Arası/Uluslararası', 'Yarışma Kategorisi', 'Eval Date', 'Evaluation Status', ...dynamicDriveHeaders];
      this.evalService.getEvalList({}).subscribe((data: any) => {
        this.dynamicDriveData = data['data'].sort((a: { status: any; }, b: { status: any; }) => {
          const nameA = a.status;
          const nameB = b.status;
          if (nameA > nameB) { return -1; }
          if (nameA < nameB) { return 1; }
          return 0;
        }).map((item: any) => {
          const dynamicResults: any = {};
          let passed = false;
          for (let i = 0; i < this.dynamicDays.length; i++) {
            const dayNumber = i + 1;
            if (passed) { dynamicResults[`day${dayNumber}_dynamic`] = ''; continue; }
            const date = this.dynamicDays[i];
            const teamAppointments = appointmentsArray.filter((app: { team: { team_id: any; }; start_time: string; }) =>
              app.team.team_id === item.team.team_id &&
              date &&
              app.start_time.startsWith(date)
            );
            if (teamAppointments.length === 0) { dynamicResults[`day${dayNumber}_dynamic`] = ''; }
            else if (teamAppointments.some((app: { status: string; }) => app.status === 'joined')) {
              const joinedAppointment = teamAppointments.find((app: { status: string; }) => app.status === 'joined');
              if (joinedAppointment) {
                const result = (item.eval_day === dayNumber && item.dynamic_drive !== null) ? 'Geçti' : 'Geçemedi';
                dynamicResults[`day${dayNumber}_dynamic`] = result;
                if (result === 'Geçti') { passed = true; }
              } else { dynamicResults[`day${dayNumber}_dynamic`] = 'Geçemedi'; }
            } else { dynamicResults[`day${dayNumber}_dynamic`] = ''; }
          }
          return { ...item, ...dynamicResults,
            appointments: appointmentsArray.filter((app: { team: { team_id: any; }; }) => app.team.team_id === item.team.team_id),
            eval_date: item.eval_day ?? '', evaluation_status: item.status ?? '' };
        });
        this.dynamicDriveStats = {};
        for (let i = 0; i < this.dynamicDays.length; i++) {
          const dayNumber = i + 1;
          const date = this.dynamicDays[i];
          if (date) {
            const appointmentsForDay = appointmentsArray.filter((app: { start_time: string; }) => app.start_time.startsWith(date));
            const joined = appointmentsForDay.filter((app: { status: string; }) => app.status === 'joined').length;
            const notJoined = appointmentsForDay.filter((app: { status: string; }) => app.status === 'not_joined').length;
            this.dynamicDriveStats[`day${dayNumber}`] = { joined, notJoined };
          } else { this.dynamicDriveStats[`day${dayNumber}`] = { joined: 0, notJoined: 0 }; }
        }
        let attendedTeams = 0;
        let passedTeams = 0;
        let failedTeams = 0;
        this.dynamicDriveData.forEach(item => {
          let itemAttended = false;
          let itemPassed = false;
          for (let i = 0; i < this.dynamicDays.length; i++) {
            const key = `day${i + 1}_dynamic`;
            const res = item[key];
            if (res) { itemAttended = true; }
            if (res === 'Geçti') { itemPassed = true; break; }
          }
          if (itemAttended) {
            attendedTeams++;
            if (itemPassed) { passedTeams++; } else { failedTeams++; }
          }
        });
        const notAttendedTeams = this.dynamicDriveData.length - attendedTeams;
        this.dynamicDriveAdvancedStats = { attendedTeams, notAttendedTeams, passedTeams, failedTeams };
        this.dynamicDriveAdvancedStats.techSuccessfulTeams = this.dynamicDriveData.filter(
          item => item.evaluation_status?.toLowerCase() === 'success'
        ).length;
        this.dynamicDriveAdvancedStats.techUnsuccessfulTeams = this.dynamicDriveData.filter(
          item => item.evaluation_status?.toLowerCase() === 'closed' &&
            this.dynamicDays.some((_, index) => item[`day${index + 1}_dynamic`] === 'Geçti')
        ).length;
        // tslint:disable-next-line:max-line-length
        this.schoolTypeOptions = Array.from(new Set(this.dynamicDriveData.map(item => ReportsDetailComponent.getSchoolTypeDisplay(item.team?.school_type_name)).filter(val => !!val)));
        // tslint:disable-next-line:max-line-length
        this.vehicleCategoryOptions = Array.from(new Set(this.dynamicDriveData.map(item => ReportsDetailComponent.getCategoryDisplay(item.team?.vehicle_category)).filter(val => !!val)));
        this.evaluationStatusOptions = Array.from(new Set(this.dynamicDriveData.map(item => item.evaluation_status).filter(val => !!val)));
        this.dynamicDriveFiltered = [...this.dynamicDriveData];
        this.loading = false;
        this.filterDynamicDriveStats();
        this.evalService.getEvaluationGroupedStats().subscribe(groupedStats => {
          const gData = groupedStats['data'] || [];
          this.dynamicDriveData.forEach(item => {
            item.gData = gData.filter((g: { team_id: any; }) => g.team_id === item.team_id)[0]?.details || [];
          });

          console.log(this.dynamicDriveData);


          const gDataKeysSet = new Set<string>();
          const vehicleCategorySet = new Set<string>();
          const schoolTypeSet = new Set<string>();
          const evalStatusSet = new Set<string>();
          const gDataColumnOptions: { [key: string]: Set<string> } = {};

          this.dynamicDriveData.forEach(item => {
            if (item.gData && typeof item.gData === 'object') {
              Object.keys(item.gData).forEach(key => {
                gDataKeysSet.add(key);
                if (!gDataColumnOptions[key]) { gDataColumnOptions[key] = new Set<string>(); }
                if (item.gData[key]?.value) { gDataColumnOptions[key].add(item.gData[key].value); }
              });
            }

            const vcat = this.getVehicleCategoryDisplay(item.team?.vehicle_category);
            if (vcat) { vehicleCategorySet.add(vcat); }
            const stype = this.getSchoolTypeDisplay(item.team?.school_type_name);
            if (stype) { schoolTypeSet.add(stype); }


            if (item.evaluation_status) { evalStatusSet.add(item.evaluation_status); }
          });
          const gDataKeys = Array.from(gDataKeysSet);


          this.gDataTableColumns = [
            'team_id',
            'team_name',
            'vehicle_category',
            'school_type_name',
            'dynamic_drive_result',
            'eval_status',
            ...gDataKeys
          ];

          this.gDataTableColumns.forEach(col => this.gDataTableFilters[col] = '');

          this.gDataTableRows = this.dynamicDriveData.map(item => {
            const row: any = {
              team_id: item.team?.team_id || '',
              team_name: item.team?.team_name || '',
              vehicle_category: this.getVehicleCategoryDisplay(item.team?.vehicle_category),
              school_type_name: this.getSchoolTypeDisplay(item.team?.school_type_name),
              dynamic_drive_result: item.dynamic_drive !== null ? 'Geçti' : 'Kaldı',
              eval_status: item.evaluation_status || ''
            };
            gDataKeys.forEach(key => {
              row[key] = item.gData?.[key]?.value || '';
            });
            return row;
          });


          this.gDataVehicleCategoryOptions = Array.from(vehicleCategorySet);
          this.gDataSchoolTypeOptions = Array.from(schoolTypeSet);
          this.gDataEvalStatusOptions = Array.from(evalStatusSet);
          this.gDataColumnOptions = {};
          Object.keys(gDataColumnOptions).forEach(key => {
            this.gDataColumnOptions[key] = Array.from(gDataColumnOptions[key]);
          });

          this.gDataTableFilteredRows = [...this.gDataTableRows];

          this.gDataTableLoading = false;

        });
      });
    });
  }

  getVehicleCategoryDisplay(category: string): string {
    if (!category) { return ''; }
    if (category.toLowerCase() === 'hydromobile') { return 'Hidromobil'; }
    if (category.toLowerCase() === 'electromobile') { return 'Elektromobil'; }
    return category;
  }

  getSchoolTypeDisplay(type: string): string {
    if (!type) { return ''; }
    if (type.toLowerCase() === 'international') { return 'Uluslararası'; }
    return 'Lise';
  }

  loadEvaluationStatsData(type: string): void {
    this.evalStatsVisible = false;
    this.evalService.startEvaluationStatsData(type).subscribe((data: any[]) => {
      this.evalStatsData = data;
      this.filteredEvalStatsData = [...this.evalStatsData];
      this.evalStatsVisible = true;
    });
  }

  selectEvaluationStatsType(type: string): void {
    if (this.currentEvalStatsType !== type) {
      this.currentEvalStatsType = type;
      this.evalStatsData = [];
      this.filteredEvalStatsData = [];
      this.clearTableFilters(this.evalStatsFilters, this.evalStatsConfig);
      this.evalStatsSortField = '';
      this.evalStatsSortOrder = '';
      this.evalStatsVisible = false;
      this.loadEvaluationStatsData(type);
    }
  }

  selectDynamicStatsType(type: 'elektromobil' | 'hidro' | 'lise') {
    if (this.dynamicStatsType !== type) {
      this.dynamicStatsType = type;
      this.filterDynamicDriveStats();
    }
  }
  getFilteredTeamsByStatsType(statsType: 'elektromobil' | 'hidro' | 'lise'): any[] {
    if (statsType === 'lise') {
      return this.dynamicDriveData.filter(item => item.team?.school_type === 2);
    } else if (statsType === 'hidro') {
      return this.dynamicDriveData.filter(item => item.team?.school_type !== 2 && item.team?.vehicle_category === 'hydromobile');
    } else {
      return this.dynamicDriveData.filter(item =>
        item.team?.school_type !== 2 &&
        item.team?.vehicle_category === 'electromobile'
      );
    }
  }

  filterDynamicDriveStats() {
    const filteredTeams = this.getFilteredTeamsByStatsType(this.dynamicStatsType);

    this.filteredDynamicDriveStats = {};
    for (let i = 0; i < this.dynamicDays.length; i++) {
      const dayNumber = i + 1;
      let joined = 0;
      let notJoined = 0;
      filteredTeams.forEach(item => {
        const appointments = item.appointments || [];
        const date = this.dynamicDays[i];
        const dayAppointments = appointments.filter((app: { start_time: string; }) => app.start_time.startsWith(date));
        joined += dayAppointments.filter((app: { status: string; }) => app.status === 'joined').length;
        notJoined += dayAppointments.filter((app: { status: string; }) => app.status === 'not_joined').length;
      });
      this.filteredDynamicDriveStats[`day${dayNumber}`] = { joined, notJoined };
    }

    let attendedTeams = 0;
    let passedTeams = 0;
    let failedTeams = 0;
    let techSuccessfulTeams = 0;
    let techUnsuccessfulTeams = 0;
    filteredTeams.forEach(item => {
      let itemAttended = false;
      let itemPassed = false;
      for (let i = 0; i < this.dynamicDays.length; i++) {
        const key = `day${i + 1}_dynamic`;
        const res = item[key];
        if (res) { itemAttended = true; }
        if (res === 'Geçti') { itemPassed = true; break; }
      }
      if (itemAttended) {
        attendedTeams++;
        if (itemPassed) { passedTeams++; } else { failedTeams++; }
      }
      if (item.evaluation_status?.toLowerCase() === 'success') {
        techSuccessfulTeams++;
      }
      if (
        item.evaluation_status?.toLowerCase() === 'closed' &&
        this.dynamicDays.some((_, index) => item[`day${index + 1}_dynamic`] === 'Geçti')
      ) {
        techUnsuccessfulTeams++;
      }
    });
    const notAttendedTeams = filteredTeams.length - attendedTeams;
    this.filteredDynamicDriveAdvancedStats = {
      attendedTeams,
      notAttendedTeams,
      passedTeams,
      failedTeams,
      techSuccessfulTeams,
      techUnsuccessfulTeams
    };
  }

  applyEvalStatsFilters(): void {
    this.filteredEvalStatsData = this.evalStatsData.filter(row => {
      return this.evalStatsConfig.columns.every(col => {
        const filterValue = (this.evalStatsFilters[col] || '').toString().toLowerCase();
        let cellValue = '';
        if (col === 'school_type_name') {
          cellValue = ReportsDetailComponent.getSchoolTypeDisplay(row.team?.school_type_name) || '';
        } else if (col === 'vehicle_category') {
          cellValue = ReportsDetailComponent.getCategoryDisplay(row.team?.vehicle_category) || '';
        } else if (col.startsWith('day')) {
          cellValue = row[col] || '';
        } else if (col === 'team_name') {
          cellValue = row.team?.team_name || '';
        } else if (col === 'vehicle_number') {
          cellValue = row.team?.vehicle_number?.toString() || '';
        } else if (col === 'team_id') {
          cellValue = row.team?.team_id?.toString() || '';
        } else if (col === 'eval_date') {
          cellValue = row.eval_date?.toString() || '';
        } else if (col === 'evaluation_status') {
          cellValue = row.evaluation_status?.toString() || '';
        } else {
          cellValue = (row[col] || '').toString();
        }
        return cellValue.toLowerCase().includes(filterValue);
      });
    });
  }
  clearEvalStatsFilters(): void {
    this.clearTableFilters(this.evalStatsFilters, this.evalStatsConfig);
    this.evalStatsSortField = '';
    this.evalStatsSortOrder = '';
    this.filteredEvalStatsData = [...this.evalStatsData];
  }
  sortEvalStats(field: string): void {
    if (this.evalStatsSortField === field) {
      this.evalStatsSortOrder = this.evalStatsSortOrder === 'asc' ? 'desc' : (this.evalStatsSortOrder === 'desc' ? '' : 'asc');
      if (!this.evalStatsSortOrder) { this.evalStatsSortField = ''; }
    } else {
      this.evalStatsSortField = field;
      this.evalStatsSortOrder = 'asc';
    }
    let filtered = this.applyTableFilters(this.evalStatsData, this.evalStatsFilters, this.evalStatsConfig);
    if (this.evalStatsSortField && this.evalStatsSortOrder) {
      filtered = this.sortData(filtered, this.evalStatsSortField, this.evalStatsSortOrder);
    }
    this.filteredEvalStatsData = filtered;
  }
  getEvalStatsSortIcon(field: string): string {
    if (this.evalStatsSortField !== field) { return 'bx bx-sort'; }
    if (this.evalStatsSortOrder === 'asc') { return 'bx bx-sort-up'; }
    if (this.evalStatsSortOrder === 'desc') { return 'bx bx-sort-down'; }
    return 'bx bx-sort';
  }
  exportTableToExcel(data: any[], config: TableConfig, filename: string) {
    if (!data || data.length === 0) { return; }
    const exportData = data.map(config.getRowExportData);
    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(exportData, { header: config.headers });
    config.headers.forEach((_, idx) => {
      const cell = ws[XLSX.utils.encode_cell({ r: 0, c: idx })];
      if (cell) { cell.s = { fill: { fgColor: { rgb: 'D9EAF7' } }, font: { bold: true } }; }
    });
    ws['!cols'] = config.headers.map((header, _) => {
      const maxContent = Math.max(header.length, ...exportData.map(row => (row[header] ? row[header].toString().length : 0)));
      return { wch: maxContent + 2 };
    });
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, config.excelSheetName);
    XLSX.writeFile(wb, `${filename}-${new Date().getTime()}.xlsx`, { cellStyles: true });
  }
  applyDynamicDriveFilters() {
    let filtered = this.applyTableFilters(this.dynamicDriveData, this.dynamicDriveFilters, this.dynamicDriveConfig);
    if (this.dynamicDriveFilterPassedOnly) {
      filtered = filtered.filter(item =>
        this.dynamicDays.some((_, index) => item[`day${index + 1}_dynamic`] === 'Geçti')
      );
    }
    if (this.dynamicDriveSortField) {
      filtered = this.sortData(filtered, this.dynamicDriveSortField, this.dynamicDriveSortOrder);
    }
    this.dynamicDriveFiltered = filtered;
  }
  sortDynamicDrive(field: string) {
    if (this.dynamicDriveSortField === field) {
      this.dynamicDriveSortOrder = this.dynamicDriveSortOrder === 'asc' ? 'desc' : (this.dynamicDriveSortOrder === 'desc' ? '' : 'asc');
      if (this.dynamicDriveSortOrder === '') { this.dynamicDriveSortField = ''; }
    } else {
      this.dynamicDriveSortField = field;
      this.dynamicDriveSortOrder = 'asc';
    }
    this.applyDynamicDriveFilters();
  }
  getSortIcon(field: string): string {
    if (this.dynamicDriveSortField !== field) { return 'bx bx-sort'; }
    if (this.dynamicDriveSortOrder === 'asc') { return 'bx bx-sort-up'; }
    if (this.dynamicDriveSortOrder === 'desc') { return 'bx bx-sort-down'; }
    return 'bx bx-sort';
  }
  sortData(data: any[], field: string, order: 'asc' | 'desc' | ''): any[] {
    if (!field || !order) { return data; }
    return [...data].sort((a, b) => {
      let aValue = this.getSortValue(a, field);
      let bValue = this.getSortValue(b, field);
      if (aValue == null) { aValue = ''; }
      if (bValue == null) { bValue = ''; }
      if (typeof aValue === 'string') { aValue = aValue.toLowerCase(); }
      if (typeof bValue === 'string') { bValue = bValue.toLowerCase(); }
      if (aValue < bValue) { return order === 'asc' ? -1 : 1; }
      if (aValue > bValue) { return order === 'asc' ? 1 : -1; }
      return 0;
    });
  }
  getSortValue(row: any, field: string): any {
    if (field === 'school_type_name') { return ReportsDetailComponent.getSchoolTypeDisplay(row.team?.school_type_name); }
    if (field === 'vehicle_category') { return ReportsDetailComponent.getCategoryDisplay(row.team?.vehicle_category); }
    if (field === 'team_name') { return row.team?.team_name || ''; }
    if (field === 'vehicle_number') { return row.team?.vehicle_number || ''; }
    if (field === 'team_id') { return row.team?.team_id || ''; }
    if (field === 'eval_date') { return row.eval_date || ''; }
    if (field === 'evaluation_status') { return row.evaluation_status || ''; }
    if (field.startsWith('day')) { return row[field] || ''; }
    return row[field] || '';
  }
  clearDynamicDriveFilters() {
    this.clearTableFilters(this.dynamicDriveFilters, this.dynamicDriveConfig);
    this.dynamicDriveSortField = '';
    this.dynamicDriveSortOrder = '';
    this.dynamicDriveFiltered = [...this.dynamicDriveData];
  }
  exportDynamicDriveToExcel() {
    this.exportTableToExcel(this.dynamicDriveFiltered, this.dynamicDriveConfig, 'dinamik-surus');
  }
  exportStatisticsToExcel(): void {
    const data: any[] = [];
    const rowGelen: any = {};
    const rowGelmeyen: any = {};
    for (let i = 0; i < this.dynamicDays.length; i++) {
      const key = 'day' + (i + 1);
      rowGelen[`${i + 1}. GÜN`] = this.dynamicDriveStats[key]?.joined || 0;
      rowGelmeyen[`${i + 1}. GÜN`] = this.dynamicDriveStats[key]?.notJoined || 0;
    }
    data.push({ İstatistik: 'Randevuya Gelen', ...rowGelen });
    data.push({ İstatistik: 'Randevuya Gelmeyen', ...rowGelmeyen });
    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(data);
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'İstatistik');
    XLSX.writeFile(wb, `istatistik-${new Date().getTime()}.xlsx`);
  }
  exportAdvancedStatsToExcel(): void {
    const data: any[] = [];
    data.push({ İstatistik: 'Teknik Kontrole Gelen Takım Sayısı', Değer: this.dynamicDriveAdvancedStats.attendedTeams || 0 });
    data.push({ İstatistik: 'Teknik Kontrole Hiç Gelmeyen Takım Sayısı', Değer: this.dynamicDriveAdvancedStats.notAttendedTeams || 0 });
    // tslint:disable-next-line:max-line-length
    data.push({ İstatistik: 'Teknik Kontrole Gelip Dinamik Sürüş Testini Geçen Takım Sayısı', Değer: this.dynamicDriveAdvancedStats.passedTeams || 0 });
    // tslint:disable-next-line:max-line-length
    data.push({ İstatistik: 'Teknik Kontrole Gelip Dinamik Sürüş Testini Geçemeyen Takım Sayısı', Değer: this.dynamicDriveAdvancedStats.failedTeams || 0 });
    // tslint:disable-next-line:max-line-length
    data.push({ İstatistik: 'Teknik Kontrol Aşamasından Başarılı Olan Takım Sayısı', Değer: this.dynamicDriveAdvancedStats.techSuccessfulTeams || 0 });
    // tslint:disable-next-line:max-line-length
    data.push({ İstatistik: 'Teknik Kontrol Aşamasından Başarısız Olan Takım Sayısı', Değer: this.dynamicDriveAdvancedStats.techUnsuccessfulTeams || 0 });
    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(data);
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Gelişmiş İstatistik');
    XLSX.writeFile(wb, `gelismis-istatistik-${new Date().getTime()}.xlsx`);
  }

  exportEvaluationStatsToExcel(dataSource: any[], suffix?: string): void {
    const exportData = dataSource.map(this.evalStatsConfig.getRowExportData);
    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(exportData, { header: this.evalStatsConfig.headers });
    this.evalStatsConfig.headers.forEach((_, idx) => {
      const cell = ws[XLSX.utils.encode_cell({ r: 0, c: idx })];
      if (cell) { cell.s = { fill: { fgColor: { rgb: 'D9EAF7' } }, font: { bold: true } }; }
    });
    ws['!cols'] = this.evalStatsConfig.headers.map(header => {
      const maxContent = Math.max(header.length, ...exportData.map(row => (row[header] ? row[header].toString().length : 0)));
      return { wch: maxContent + 2 };
    });
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, this.evalStatsConfig.excelSheetName);
    const fileSuffix = suffix ? `-${suffix}` : '';
    XLSX.writeFile(wb, `degerlendirme-istatistik${fileSuffix}-${new Date().getTime()}.xlsx`, { cellStyles: true });
  }
  applyTableFilters(data: any[], filters: TableColumnFilter, config: TableConfig): any[] {
    return data.filter(row => {
      return config.columns.every(col => {
        const filterValue = filters[col] || '';
        if (!filterValue) { return true; }
        let cellValue = '';
        if (col === 'school_type_name') {
          cellValue = ReportsDetailComponent.getSchoolTypeDisplay(row.team?.school_type_name) || '';
        } else if (col === 'vehicle_category') {
          cellValue = ReportsDetailComponent.getCategoryDisplay(row.team?.vehicle_category) || '';
        } else if (col.startsWith('day')) {
          cellValue = row[col] || '';
        } else if (col === 'team_name') {
          cellValue = row.team?.team_name || '';
        } else if (col === 'vehicle_number') {
          cellValue = row.team?.vehicle_number?.toString() || '';
        } else if (col === 'team_id') {
          cellValue = row.team?.team_id?.toString() || '';
        } else if (col === 'eval_date') {
          cellValue = row.eval_date?.toString() || '';
        } else if (col === 'evaluation_status') {
          cellValue = row.evaluation_status?.toString() || '';
        } else {
          cellValue = row[col] || '';
        }
        return cellValue.toLowerCase().includes(filterValue.toLowerCase());
      });
    });
  }

  exportTeamsForStat(statType: string, dayIndex?: number) {
    const filteredTeams = this.getFilteredTeamsByStatsType(this.dynamicStatsType);

    let teams: any[] = [];
    if (statType === 'joined' && typeof dayIndex === 'number') {
      teams = filteredTeams.filter(item => {
        const appointments = item.appointments || [];
        const date = this.dynamicDays[dayIndex];
        return appointments.some(app => app.start_time.startsWith(date) && app.status === 'joined');
      });
    } else if (statType === 'notJoined' && typeof dayIndex === 'number') {
      teams = filteredTeams.filter(item => {
        const appointments = item.appointments || [];
        const date = this.dynamicDays[dayIndex];
        return appointments.some(app => app.start_time.startsWith(date) && app.status === 'not_joined');
      });
    }
    else if (statType === 'attendedTeams') {
      teams = filteredTeams.filter(item => {
        for (let i = 0; i < this.dynamicDays.length; i++) {
          const key = `day${i + 1}_dynamic`;
          if (item[key]) { return true; }
        }
        return false;
      });
    } else if (statType === 'notAttendedTeams') {
      teams = filteredTeams.filter(item => {
        for (let i = 0; i < this.dynamicDays.length; i++) {
          const key = `day${i + 1}_dynamic`;
          if (item[key]) { return false; }
        }
        return true;
      });
    } else if (statType === 'passedTeams') {
      teams = filteredTeams.filter(item => {
        for (let i = 0; i < this.dynamicDays.length; i++) {
          const key = `day${i + 1}_dynamic`;
          if (item[key] === 'Geçti') { return true; }
        }
        return false;
      });
    } else if (statType === 'failedTeams') {
      teams = filteredTeams.filter(item => {
        let attended = false;
        let passed = false;
        for (let i = 0; i < this.dynamicDays.length; i++) {
          const key = `day${i + 1}_dynamic`;
          if (item[key]) { attended = true; }
          if (item[key] === 'Geçti') { passed = true; }
        }
        return attended && !passed;
      });
    } else if (statType === 'techSuccessfulTeams') {
      teams = filteredTeams.filter(item => item.evaluation_status?.toLowerCase() === 'success');
    } else if (statType === 'techUnsuccessfulTeams') {
      teams = filteredTeams.filter(item =>
        item.evaluation_status?.toLowerCase() === 'closed' &&
        this.dynamicDays.some((_, index) => item[`day${index + 1}_dynamic`] === 'Geçti')
      );
    }

    const exportData = teams.map(item => ({
      Takım: item.team?.team_name || '',
      'Takım Lideri': item.team?.team_leader || '',
      'Lider E-posta': item.team?.team_leader_email || '',
      'Lider Telefon': item.team?.team_leader_phone || '',
      Üniversite: item.team?.university_name || '',
      Şehir: item.team?.city_name || '',
      Kategori: ReportsDetailComponent.getCategoryDisplay(item.team?.vehicle_category),
      Araç: item.team?.vehicle_name || ''
    }));

    if (exportData.length === 0) { return; }
    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(exportData);
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Takımlar');
    let fileName = 'takimlar';
    if (statType && typeof dayIndex === 'number') {
      fileName += `-${statType}-${dayIndex + 1}gun`;
    } else if (statType) {
      fileName += `-${statType}`;
    }
    XLSX.writeFile(wb, `${fileName}-${new Date().getTime()}.xlsx`);
  }

  applyGDataTableFilters(): void {
    let filtered = this.gDataTableRows.filter(row => {
      return this.gDataTableColumns.every(col => {
        const filterValue = (this.gDataTableFilters[col] || '').toString().toLowerCase();
        if (!filterValue) { return true; }
        const cellValue = (row[col] || '').toString().toLowerCase();
        return cellValue.includes(filterValue);
      });
    });
    if (this.gDataTableSortField && this.gDataTableSortOrder) {
      filtered = this.sortGDataTable(filtered, this.gDataTableSortField, this.gDataTableSortOrder);
    }
    this.gDataTableFilteredRows = filtered;
  }

  clearGDataTableFilters(): void {
    this.gDataTableColumns.forEach(col => this.gDataTableFilters[col] = '');
    this.gDataTableSortField = '';
    this.gDataTableSortOrder = '';
    this.gDataTableFilteredRows = [...this.gDataTableRows];
  }

  onGDataTableSort(field: string): void {
    if (this.gDataTableSortField === field) {
      this.gDataTableSortOrder = this.gDataTableSortOrder === 'asc' ? 'desc' : (this.gDataTableSortOrder === 'desc' ? '' : 'asc');
      if (!this.gDataTableSortOrder) { this.gDataTableSortField = ''; }
    } else {
      this.gDataTableSortField = field;
      this.gDataTableSortOrder = 'asc';
    }
    this.applyGDataTableFilters();
  }

  getGDataTableSortIcon(field: string): string {
    if (this.gDataTableSortField !== field) { return 'bx bx-sort'; }
    if (this.gDataTableSortOrder === 'asc') { return 'bx bx-sort-up'; }
    if (this.gDataTableSortOrder === 'desc') { return 'bx bx-sort-down'; }
    return 'bx bx-sort';
  }

  getGDataColWidth(col: string): string {
    if (this.gDataColWidths[col]) {
      return this.gDataColWidths[col] + 'px';
    }
    let maxLen = (col || '').length;
    for (const row of this.gDataTableRows) {
      const val = row[col];
      if (val && val.toString().length > maxLen) {
        maxLen = val.toString().length;
      }
    }
    const px = Math.max(100, Math.min(350, maxLen * 10));
    this.gDataColWidths[col] = px;
    return px + 'px';
  }

  private sortGDataTable(rows: any[], field: string, order: 'asc' | 'desc'): any[] {
    return [...rows].sort((a, b) => {
      const aValue = (a[field] || '').toString().toLowerCase();
      const bValue = (b[field] || '').toString().toLowerCase();
      if (aValue < bValue) { return order === 'asc' ? -1 : 1; }
      if (aValue > bValue) { return order === 'asc' ? 1 : -1; }
      return 0;
    });
  }

  exportGDataTableToExcel(): void {
    if (!this.gDataTableFilteredRows || this.gDataTableFilteredRows.length === 0) { return; }
    const headers = this.gDataTableColumns.map(col => {
      switch (col) {
        case 'team_id': return 'Takım ID';
        case 'team_name': return 'Takım Adı';
        case 'vehicle_category': return 'Araç Kategorisi';
        case 'school_type_name': return 'Kurum Tipi';
        case 'dynamic_drive_result': return 'Dinamik Sürüş';
        case 'eval_status': return 'Eval Status';
        default: return col;
      }
    });

    const exportData = this.gDataTableFilteredRows.map(row => {
      const obj: any = {};
      this.gDataTableColumns.forEach((col, idx) => {
        obj[headers[idx]] = row[col];
      });
      return obj;
    });
    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(exportData, { header: headers });
    headers.forEach((_, idx) => {
      const cell = ws[XLSX.utils.encode_cell({ r: 0, c: idx })];
      if (cell) { cell.s = { fill: { fgColor: { rgb: 'D9EAF7' } }, font: { bold: true } }; }
    });
    ws['!cols'] = headers.map((header, idx) => {
      const maxContent = Math.max(header.length, ...exportData.map(row => (row[header] ? row[header].toString().length : 0)));
      return { wch: maxContent + 2 };
    });
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Takım Gelişmiş Kontrol');
    XLSX.writeFile(wb, `takim-gelismis-kontrol-${new Date().getTime()}.xlsx`, { cellStyles: true });
  }

  exportOriginalDesignedStatsToExcel(): void {
    if (!this.originalDesignedStatsFilteredData || this.originalDesignedStatsFilteredData.length === 0) { return; }
    const headers = [
      ...this.originalDesignedStatsTeamColumns.map(col => col.label),
      ...this.originalDesignedStatsHeaders
    ];
    const exportData = this.originalDesignedStatsFilteredData.map(row => {
      const obj: any = {};
      this.originalDesignedStatsTeamColumns.forEach(col => {
        if (col.key === 'school_type') {
          obj[col.label] = this.getSchoolTypeDisplay(row[col.key]);
        } else if (col.key === 'vehicle_category') {
          obj[col.label] = this.getVehicleCategoryDisplay(row[col.key]);
        } else {
          obj[col.label] = row[col.key];
        }
      });
      this.originalDesignedStatsHeaders.forEach(h => { obj[h] = row[h]; });
      return obj;
    });
    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(exportData, { header: headers });
    headers.forEach((_, idx) => {
      const cell = ws[XLSX.utils.encode_cell({ r: 0, c: idx })];
      if (cell) { cell.s = { fill: { fgColor: { rgb: 'D9EAF7' } }, font: { bold: true } }; }
    });
    ws['!cols'] = headers.map((header, idx) => {
      const maxContent = Math.max(header.length, ...exportData.map(row => (row[header] ? row[header].toString().length : 0)));
      return { wch: maxContent + 2 };
    });
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Orijinal Tasarım');
    XLSX.writeFile(wb, `takim-orijinal-tasarim-${new Date().getTime()}.xlsx`, { cellStyles: true });
  }


  onOriginalStatsSort(field: string): void {
    if (this.originalDesignedStatsSortField === field) {
      // tslint:disable-next-line:max-line-length
      this.originalDesignedStatsSortOrder = this.originalDesignedStatsSortOrder === 'asc' ? 'desc' : (this.originalDesignedStatsSortOrder === 'desc' ? '' : 'asc');
      if (!this.originalDesignedStatsSortOrder) {
        this.originalDesignedStatsSortField = '';
      }
    } else {
      this.originalDesignedStatsSortField = field;
      this.originalDesignedStatsSortOrder = 'asc';
    }
    this.applyOriginalDesignedStatsFilters();
  }

  getOriginalStatsSortIcon(field: string): string {
    if (this.originalDesignedStatsSortField !== field) { return 'bx bx-sort'; }
    if (this.originalDesignedStatsSortOrder === 'asc') { return 'bx bx-sort-up'; }
    if (this.originalDesignedStatsSortOrder === 'desc') { return 'bx bx-sort-down'; }
    return 'bx bx-sort';
  }

  applyOriginalDesignedStatsFilters(): void {
    let filtered = this.originalDesignedStatsData.filter(row => {
      return [
        ...this.originalDesignedStatsTeamColumns.map(col => col.key),
        ...this.originalDesignedStatsHeaders
      ].every(col => {
        const filterValue = (this.originalDesignedStatsFilters[col] || '').toString().toLowerCase();
        let cellValue = (row[col] || '').toString().toLowerCase();
        if (col === 'school_type') {
          cellValue = this.getSchoolTypeDisplay(row.school_type).toLowerCase();
        }
        if (col === 'vehicle_category') {
          cellValue = this.getVehicleCategoryDisplay(row.vehicle_category).toLowerCase();
        }
        if (this.originalDesignedStatsHeaders.includes(col)) {
          cellValue = (row[col] || '').toString().toLowerCase();
          return !(filterValue && filterValue !== cellValue);
        }
        return cellValue.includes(filterValue);
      });
    });
    if (this.originalDesignedStatsSortField && this.originalDesignedStatsSortOrder) {
      filtered = [...filtered].sort((a, b) => {
        let aValue = (a[this.originalDesignedStatsSortField] || '').toString().toLowerCase();
        let bValue = (b[this.originalDesignedStatsSortField] || '').toString().toLowerCase();
        if (this.originalDesignedStatsSortField === 'school_type') {
          aValue = this.getSchoolTypeDisplay(a.school_type).toLowerCase();
          bValue = this.getSchoolTypeDisplay(b.school_type).toLowerCase();
        }
        if (this.originalDesignedStatsSortField === 'vehicle_category') {
          aValue = this.getVehicleCategoryDisplay(a.vehicle_category).toLowerCase();
          bValue = this.getVehicleCategoryDisplay(b.vehicle_category).toLowerCase();
        }
        return aValue < bValue ? (this.originalDesignedStatsSortOrder === 'asc' ? -1 : 1)
          : aValue > bValue ? (this.originalDesignedStatsSortOrder === 'asc' ? 1 : -1) : 0;
      });
    }
    this.originalDesignedStatsFilteredData = filtered;
  }

  clearOriginalDesignedStatsFilters(): void {
    this.originalDesignedStatsTeamColumns.forEach(col => this.originalDesignedStatsFilters[col.key] = '');
    this.originalDesignedStatsHeaders.forEach(h => this.originalDesignedStatsFilters[h] = '');
    this.originalDesignedStatsSortField = '';
    this.originalDesignedStatsSortOrder = '';
    this.originalDesignedStatsFilteredData = [...this.originalDesignedStatsData];
  }

  applyLocalDesignedStatsFilters(): void {
    let filtered = this.localDesignedStatsData.filter(row => {
      return [
        ...this.localDesignedStatsTeamColumns.map(col => col.key),
        ...this.localDesignedStatsHeaders
      ].every(col => {
        const filterValue = (this.localDesignedStatsFilters[col] || '').toString().toLowerCase();
        let cellValue = (row[col] || '').toString().toLowerCase();
        if (col === 'school_type') {
          cellValue = this.getSchoolTypeDisplay(row.school_type).toLowerCase();
        }
        if (col === 'vehicle_category') {
          cellValue = this.getVehicleCategoryDisplay(row.vehicle_category).toLowerCase();
        }
        if (this.localDesignedStatsHeaders.includes(col)) {
          cellValue = (row[col] || '').toString().toLowerCase();
          return !(filterValue && filterValue !== cellValue);
        }
        return cellValue.includes(filterValue);
      });
    });
    if (this.localDesignedStatsSortField && this.localDesignedStatsSortOrder) {
      filtered = [...filtered].sort((a, b) => {
        let aValue = (a[this.localDesignedStatsSortField] || '').toString().toLowerCase();
        let bValue = (b[this.localDesignedStatsSortField] || '').toString().toLowerCase();
        if (this.localDesignedStatsSortField === 'school_type') {
          aValue = this.getSchoolTypeDisplay(a.school_type).toLowerCase();
          bValue = this.getSchoolTypeDisplay(b.school_type).toLowerCase();
        }
        if (this.localDesignedStatsSortField === 'vehicle_category') {
          aValue = this.getVehicleCategoryDisplay(a.vehicle_category).toLowerCase();
          bValue = this.getVehicleCategoryDisplay(b.vehicle_category).toLowerCase();
        }
        return aValue < bValue ? (this.localDesignedStatsSortOrder === 'asc' ? -1 : 1)
          : aValue > bValue ? (this.localDesignedStatsSortOrder === 'asc' ? 1 : -1) : 0;
      });
    }
    this.localDesignedStatsFilteredData = filtered;
  }

  getOriginalStatsColWidth(col: string): string {
    let maxLen = (col || '').length;
    for (const row of this.originalDesignedStatsData) {
      const val = row[col];
      if (val && val.toString().length > maxLen) {
        maxLen = val.toString().length;
      }
    }
    const px = Math.max(120, Math.min(500, maxLen * 12 + 32));
    return px + 'px';
  }

  clearLocalDesignedStatsFilters(): void {
    this.localDesignedStatsTeamColumns.forEach(col => this.localDesignedStatsFilters[col.key] = '');
    this.localDesignedStatsHeaders.forEach(h => this.localDesignedStatsFilters[h] = '');
    this.localDesignedStatsSortField = '';
    this.localDesignedStatsSortOrder = '';
    this.localDesignedStatsFilteredData = [...this.localDesignedStatsData];
  }

  exportLocalDesignedStatsToExcel(): void {
    if (!this.localDesignedStatsFilteredData || this.localDesignedStatsFilteredData.length === 0) { return; }
    const headers = [
      ...this.localDesignedStatsTeamColumns.map(col => col.label),
      ...this.localDesignedStatsHeaders
    ];
    const exportData = this.localDesignedStatsFilteredData.map(row => {
      const obj: any = {};
      this.localDesignedStatsTeamColumns.forEach(col => {
        if (col.key === 'school_type') {
          obj[col.label] = this.getSchoolTypeDisplay(row[col.key]);
        } else if (col.key === 'vehicle_category') {
          obj[col.label] = this.getVehicleCategoryDisplay(row[col.key]);
        } else {
          obj[col.label] = row[col.key];
        }
      });
      this.localDesignedStatsHeaders.forEach(h => { obj[h] = row[h]; });
      return obj;
    });
    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(exportData, { header: headers });
    headers.forEach((_, idx) => {
      const cell = ws[XLSX.utils.encode_cell({ r: 0, c: idx })];
      if (cell) { cell.s = { fill: { fgColor: { rgb: 'D9EAF7' } }, font: { bold: true } }; }
    });
    ws['!cols'] = headers.map((header, idx) => {
      const maxContent = Math.max(header.length, ...exportData.map(row => (row[header] ? row[header].toString().length : 0)));
      return { wch: maxContent + 2 };
    });
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Yerli Tasarım');
    XLSX.writeFile(wb, `takim-yerli-tasarim-${new Date().getTime()}.xlsx`, { cellStyles: true });
  }

  onLocalStatsSort(field: string): void {
    if (this.localDesignedStatsSortField === field) {
      // tslint:disable-next-line:max-line-length
      this.localDesignedStatsSortOrder = this.localDesignedStatsSortOrder === 'asc' ? 'desc' : (this.localDesignedStatsSortOrder === 'desc' ? '' : 'asc');
      if (!this.localDesignedStatsSortOrder) {
        this.localDesignedStatsSortField = '';
      }
    } else {
      this.localDesignedStatsSortField = field;
      this.localDesignedStatsSortOrder = 'asc';
    }
    this.applyLocalDesignedStatsFilters();
  }

  getLocalStatsSortIcon(field: string): string {
    if (this.localDesignedStatsSortField !== field) { return 'bx bx-sort'; }
    if (this.localDesignedStatsSortOrder === 'asc') { return 'bx bx-sort-up'; }
    if (this.localDesignedStatsSortOrder === 'desc') { return 'bx bx-sort-down'; }
    return 'bx bx-sort';
  }

  getLocalStatsColWidth(col: string): string {
    let headerLabel = col;
    if (this.localDesignedStatsHeaders.includes(col)) {
      headerLabel = col;
    } else {
      const teamCol = this.localDesignedStatsTeamColumns.find(tc => tc.key === col);
      if (teamCol) { headerLabel = teamCol.label; }
    }
    let maxLen = (headerLabel || '').length;
    for (const row of this.localDesignedStatsData) {
      const val = row[col];
      if (val && val.toString().length > maxLen) {
        maxLen = val.toString().length;
      }
    }
    let minWidth = 200;
    let maxWidth = 800;
    if ((headerLabel || '').length > 40) {
      minWidth = 350;
      maxWidth = 1200;
    }
    const px = Math.max(minWidth, Math.min(maxWidth, maxLen * 14 + 32));
    return px + 'px';
  }
}
