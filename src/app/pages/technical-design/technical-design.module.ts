import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { TechnicalDesignRoutingModule } from './technical-design-routing.module';
import { TechnicalDesignComponent } from './technical-design/technical-design.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { UIModule } from 'src/app/shared/ui/ui.module';
import { NgbDropdownModule, NgbTooltipModule, NgbNavModule, NgbModalModule, NgbCarouselModule } from '@ng-bootstrap/ng-bootstrap';
import { WidgetModule } from 'src/app/shared/widget/widget.module';
import { NgApexchartsModule } from 'ng-apexcharts';
import { PerfectScrollbarModule } from 'ngx-perfect-scrollbar';
import { ArchwizardModule } from 'angular-archwizard';
import { NgxDatatableModule } from '@swimlane/ngx-datatable';


@NgModule({
  declarations: [TechnicalDesignComponent],
  imports: [
    CommonModule,
    TechnicalDesignRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    UIModule,
    NgbDropdownModule,
    NgbTooltipModule,
    NgbNavModule,
    WidgetModule,
    NgApexchartsModule,
    PerfectScrollbarModule,
    ArchwizardModule,
    NgxDatatableModule,
    NgbModalModule,
    NgbCarouselModule,
  ]
})
export class TechnicalDesignModule { }
