


//::ng-deep datatable-body-cell{width:100%;}
//
//::ng-deep .ngx-datatable.bootstrap .datatable-body .datatable-body-row{
//    height: 40px !important;
//}
//
::ng-deep .ngx-datatable input {
  max-width: 70px;
}



::ng-deep .diff-class-name{
    //color:#f5c6cb;
  height: 100%;
  background-color: yellow;
}
::ng-deep .ngx-datatable.fixed-header .datatable-header .datatable-header-inner .datatable-header-cell .datatable-header-cell-template-wrap {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  vertical-align: middle;
  font-size: 12px;
  font-weight: bold;
  margin: -5px -10px 0px -10px;
  line-height: 1.2em;
  height: 4em;
}

.yerlilik-select {
  color: #495057;
  border-color: #ced4da;
  background: #eff2f7;
  border-radius: 3px;
  text-align: center;
}
