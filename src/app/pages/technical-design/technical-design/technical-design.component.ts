import { Component, OnInit, ViewChild } from '@angular/core';
import { TechnicalDesignService } from '../../../core/services/technical-design.service';
import Swal from 'sweetalert2';
import { ColumnMode } from '@swimlane/ngx-datatable';
import { FormBuilder, FormGroup } from '@angular/forms';
import { TeamsService } from 'src/app/core/services/teams.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-technical-design',
  templateUrl: './technical-design.component.html',
  styleUrls: ['./technical-design.component.scss'],
})
export class TechnicalDesignComponent implements OnInit {

  title = [];
  @ViewChild('myTable') table: any;
  breadCrumbItems: Array<{}>;
  editing = {};
  rows = [];
  columns = [];
  fileToUpload: File = null;
  rowsData = [];
  loadingIndicator: boolean = true;
  reorderable: boolean = true;
  formData: FormGroup;
  filterForm: FormGroup;
  ColumnMode = ColumnMode;
  private columnsWithSearch = ['takim_no', 'team.team_name', 'team.vehicle_number'];

  groups = [];
  private temp: any[];

  constructor(private modalService: NgbModal, private technicalDesignService: TechnicalDesignService, private formBuilder: FormBuilder, private teamsService: TeamsService) { }

  ngOnInit(): void {
    this.getTechnicalDesign();
    this.filterForm = this.formBuilder.group({
      limit: [100],
    });
    this.formData = this.formBuilder.group({
      title: '',
      type: '',
    });
  }

  upload(files, event) {
    this.fileToUpload = files.item(0);
    this.uploadFileToActivity();
  }

  uploadFileToActivity() {
    const formData: FormData = new FormData();
    formData.append('file_name', this.fileToUpload);
    console.log(formData);
    this.technicalDesignService.importTechnicalDesign(formData).subscribe(data => {
      this.position('Imported Successfully', 'Report Imported', 'OK', 'success');
    }, error => {
      this.showValidationError(error);
    });
  }

  showValidationError(error) {
    let message = error.error.message;
    if (message === 'validation.error') {
      message = Object.values(error.error.errors).join('\n<br>');
    }

    this.position('Error', message, 'OK', 'error', false);
  }

  position(title, text, confirmText, icon, dismissAll = true) {
    return Swal.fire({
      title: title,
      html: text,
      icon: icon,
      confirmButtonColor: '#34c38f',
      cancelButtonColor: '#f46a6a',
      confirmButtonText: confirmText,
    }).then(result => {
      if (result.value && dismissAll) {
        this.modalService.dismissAll();
      }
    });
  }

  descriptionText;

  openDescription(value, modal) {
    this.descriptionText = value.split('_x000D_').join('>');
    this.modalService.open(modal, { centered: true, windowClass: 'modal-holder' });
  }

  orjData = {};

  getTechnicalDesign() {
    this.columns = [];
    this.rowsData = [];
    this.technicalDesignService.getTechnicalDesigns().subscribe(technicalDesigns => {

      this.columns = [
        // { prop: 'id',},
        { prop: 'team_name', name: 'Takim Adı', editable: false },
        { prop: 'takim_no', name: 'Takim ID', editable: false },
        { prop: 'toplam', name: 'toplam', editable: false },
        //{ prop: 'gr', name: 'GR', editable: false },
        //{ prop: 'gr_20', name: 'GR %20', editable: false },
        //{ prop: 'gr_20_eklenmis_puan', name: 'GR %20 EKLENMİŞ PUAN', editable: false },

        { prop: 'arac_ozellikleri_tablosu', name: 'ARAÇ ÖZELLİKLERİ TABLOSU', editable: false },
        { prop: 'motor', name: 'MOTOR' },
        { prop: 'motor_surucusu', name: 'MOTOR SÜRÜCÜSÜ' },
        { prop: 'batarya_yonetim_sistemi', name: 'BATARYA YÖNETİM SİSTEMİ' },
        { prop: 'yerlesik_sarj_birimi', name: 'YERLEŞİK ŞARJ BİRİMİ' },
        { prop: 'batarya_paketleme', name: 'BATARYA PAKETLEME' },
        { prop: 'elektronik_diferansiyel_uygulamasi', name: 'ELEKTRONİK DİFERANSİYEL UYGULAMASI' },
        { prop: 'arac_kontrol_sistemi', name: 'ARAÇ KONTROL SİSTEMİ' },
        { prop: 'izolasyon_izleme_cihazi', name: 'İZOLASYON İZLEME CİHAZI' },
        { prop: 'direksiyon_sistemi', name: 'DİREKSİYON SİSTEMİ' },
        { prop: 'kapi_mekanizmasi', name: 'KAPI MEKANİZMASI' },
        { prop: 'mekanik_detaylar', name: 'MEKANİK DETAYLAR' },
        { prop: 'arac_elektrik_semasi', name: 'ARAÇ ELEKTRİK ŞEMASI' },
        { prop: 'orijinal_tasarim', name: 'ORİJİNAL TASARIM' },
        { prop: 'yakit_pili', name: 'YAKIT PİLİ' },
        { prop: 'yakit_pili_kontrol_sistemi', name: 'YAKIT PİLİ KONTROL SİSTEMİ' },
        { prop: 'enerji_yonetim_sistemi', name: 'ENERJİ YÖNETİM SİSTEMİ' },
        { prop: 'dinamik_surus_testi', name: 'DİNAMİK SÜRÜŞ TESTİ' },
        { prop: 'telemetri', name: 'TELEMETRİ' },
        { prop: 'yerlilik_durumu', name: 'YERLİLİK DURUMU' },


        // { prop: 'orijinal_tasarim', name: 'Orjinal Tasarım' },
        // { prop: 'kriter_aciklama', name: 'Kriter Açıklama' },

      ];

      technicalDesigns['data'].map(item => {
        this.orjData[item.id] = item;
      });


      technicalDesigns['data'].forEach(tDesign => {
        this.rowsData.push({
          ...tDesign,
          team_name: tDesign.team?.vehicle_number + (tDesign.team ? ' - ' + tDesign.team.team_name : ''),
        });
      });
      this.rows = this.rowsData;
      this.temp = [...this.rows];
    });
  }


  updateValue(event, cell, rowIndex, row) {
    console.log('inline editing rowIndex', rowIndex);
    console.log('inline editing event', event);
    console.log('inline editing cell', cell);
    console.log(this.rows);
    console.log(event.target.value);
    this.editing[rowIndex + '-' + cell] = false;
    // console.log('rowIndex', rowIndex);
    // console.log('rowIndex-test', rowIndex[0]);
    // console.log('cell', cell);
    // console.log('event', event);
    // console.log('row', row);
    // this.rows[rowIndex[0]][cell] = event.target.value;
    this.rows = [...this.rows];
    // console.log('UPDATED!', this.rows[rowIndex][cell]);
    // console.log(this.rows[rowIndex]["team_id"]);
    // let obj = { [cell]: this.rows[rowIndex[0]][cell] };
    let obj = { [cell]: event.target.value };

    // const realIndex = rowIndex.split('-')[0];
    const realIndex = rowIndex;
    this.rows[realIndex][cell] = event.target.value;

    this.technicalDesignService.updateTechnicalDesigns(obj, row.id).subscribe((result: any) => {
      const tDesign = result.data;
      console.log('tDesign', tDesign);
      this.rows[realIndex] = {
        ...tDesign,
        team_name: tDesign.team?.vehicle_number + (tDesign.team ? ' - ' + tDesign.team.team_name : ''),
      };

      this.rows = [...this.rows]; // hack for table change detector
      this.temp = [...this.rows];
    }, error => {
      this.showValidationError(error);
    });
  }

  getRowHeight(row) {
    return row.height;
  }

  toggleExpandGroup(group) {
    console.log('Toggled Expand Group!', group);
    this.table.groupHeader.toggleExpandGroup(group);
  }

  onDetailToggle(event) {
    console.log('Detail Toggled', event);
  }

  toggleExpandRow(row) {
    console.log('Toggled Expand Row!', row);
    this.table.rowDetail.toggleExpandRow(row);
  }


  getGroupRowHeight(group, rowHeight) {
    let style = {};

    style = {
      height: (group.length * 40) + 'px',
      width: '100%',
    };

    return style;
  }

  checkGroup(event, row, rowIndex, group) {
  }

  pickRowColor = (obj) => {
    let id = obj.row['id'];
    // console.log(`id : ${id}`);
    const orjData = this.orjData[id];

    if (['id', 'takim_no', 'no', 'universite', 'kriter_aciklama', 'total', 'arac_no'].indexOf(obj.column.prop) === -1) {
      if (typeof orjData['orj_' + obj.column.prop] === 'undefined') {
        return '';
      } else {
        if (orjData['orj_' + obj.column.prop] === obj.row[obj.column.prop]) {
          return '';
        } else {
          return 'diff-class-name';
        }
      }
    }
  };

  fileType: string = 'design';

  /**
   * Open Event Modal
   * @param content modal content
   * @param event calendar event
   */
  openModal(content: any) {
    this.modalService.open(content);
  }

  saveEvent() {

  }

  closeModal() {
    this.modalService.dismissAll();
  }

  getTeams() {
    this.teamsService.getTeams(this.filterForm.value).subscribe(teams => {
      teams['data'].forEach(teamsData => {
        // console.log('teamsData');
        // console.log(teamsData);
        if (teamsData['completed'] === 1) {
          this.title.push({
            'id': teamsData['id'],
            'team_name': `${teamsData['university_name']} : ${teamsData['team_name']}`,
          });
        }
      });

    });
  }

  exportTechnicalDesign() {
    this.technicalDesignService.exportTechnicalDesigns().subscribe(data => {
      this.downLoadFile(data, 'application/ms-excel', 'technical_design.xlsx');
    });
  }


  downLoadFile(data: any, type: string, filename: string) {
    const blob = new Blob([data], { type: type });

    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.click();
  }

  updateFilter(event) {
    const orginalValue = event.target.value;
    const filterVal = orginalValue.toLocaleLowerCase();
    // filter our data
    // assign filtered matches to the active datatable
    this.rows = this.temp.filter(item => {
      //console.log('item', item);
      for (let i = 0; i < this.columnsWithSearch.length; i++) {
        // const colValue = item[this.columnsWithSearch[i]];
        const colValue = this.getValue(item, this.columnsWithSearch[i]);

        // if no filter OR colvalue is NOT null AND contains the given filter
        if ((!filterVal || (!!colValue && colValue.toString().toLocaleLowerCase().indexOf(filterVal) !== -1)) || (!orginalValue || (!!colValue && colValue.toString().indexOf(orginalValue) !== -1))) {
          // found match, return true to add to result set
          return true;
        }
      }
    });


    // Whenever the filter changes, always go back to the first page
    this.table.offset = 0;
  }

  getValue(item, key) {

    if (key.indexOf('.') === -1) {
      return item[key];
    } else {
      const pKey = key.split('.');
      if (pKey.length > 1) {
        return item[pKey[0]]?.[pKey[1]];
      }
      return item[pKey[0]];

    }

  }
}
