<div class="container-fluid">
  <!-- start page title -->
  <div class="row">
    <div class="col-12">
      <div class="text-center my-3">
        <a (click)="story.click()" class="btn btn-success inner mr-1">
          Import Technical Design
        </a>

        <a (click)="openModal(content)" class="btn btn-success inner mr-1">
          Import Files by Team
        </a>

        <a (click)="exportTechnicalDesign()" class="btn btn-success inner">
          Export Technical Design
        </a>

        <input
          type="file"
          w
          accept=".csv, .xlsm, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
          #story
          (change)="upload(story.files, $event)"
          style="display: none"
        />
      </div>
    </div>
    <!-- end col-->
  </div>
  <app-page-title
    title="Technical Design"
    [breadCrumbItems]="breadCrumbItems"
  ></app-page-title>
  <div class="row" *ngIf="rows">
    <div class="col-12">
      <input
        type="text"
        class="mx-2 mb-2 form-control"
        style="width: 30%"
        placeholder="Type to filter..."
        (keyup)="updateFilter($event)"
      />
      <ngx-datatable
        #myTable
        [rows]="rows"
        class="bootstrap"
        [loadingIndicator]="loadingIndicator"
        [columnMode]="'force'"
        [headerHeight]="50"
        [footerHeight]="50"
        [rowHeight]="'auto'"
        [limit]="100"
        [columns]="columns"
        [scrollbarH]="true"
        [reorderable]="reorderable"
      >
        <ngx-datatable-column
          *ngFor="let column of columns; let i = index"
          [name]="column.name"
          [prop]="column.prop"
          [minWidth]="120"
          [cellClass]="pickRowColor"
        >
          <ng-template
            ngx-datatable-cell-template
            let-rowIndex="rowIndex"
            let-value="value"
            let-row="row"
          >
            <div *ngIf="column.prop !== 'kriter_aciklama'">
              <!--              {{ column.name }} <br />-->
              <div
                (click)="editing[rowIndex + '-' + column.prop] = true"
                *ngIf="!editing[rowIndex + '-' + column.prop]"
              >
                <span *ngIf="column.prop !== 'yerlilik_durumu'">
                  {{ value }}
                </span>
                <span *ngIf="column.prop === 'yerlilik_durumu'">
                  {{ value ? "Var" : "Yok" }}
                </span>
              </div>
              <input
                (keydown.enter)="
                  updateValue($event, column.prop, rowIndex, row)
                "
                [disabled]="column.editable === false"
                *ngIf="
                  editing[rowIndex + '-' + column.prop] &&
                  column.prop !== 'yerlilik_durumu'
                "
                type="text"
                [value]="value"
              />
            </div>
            <div *ngIf="column.prop === 'kriter_aciklama'">
              <a (click)="openDescription(value, conditionModal)"
                >Descriptions</a
              >
            </div>
            <select
              class="yerlilik-select"
              (change)="updateValue($event, column.prop, rowIndex, row)"
              [ngModel]="value"
              *ngIf="editing[rowIndex + '-' + column.prop] && column.prop === 'yerlilik_durumu'"
            >
              <option value="1">Var</option>
              <option value="0">Yok</option>
            </select>
          </ng-template>
        </ngx-datatable-column>
      </ngx-datatable>
    </div>
  </div>
</div>

<ng-template #conditionModal let-modal>
  <div class="modal-header">
    <h5 class="modal-title mt-0">{{ descriptionText }}</h5>
    <button
      type="button"
      class="close"
      (click)="modal.dismiss('Cross click')"
      aria-hidden="true"
    >
      ×
    </button>
  </div>
</ng-template>

<ng-template #content let-modal>
  <div class="modal-header">
    <h5 class="modal-title">Add Files to team</h5>
    <button
      type="button"
      class="close"
      aria-hidden="true"
      (click)="closeModal()"
    >
      ×
    </button>
  </div>
  <div class="modal-body p-3">
    <form (ngSubmit)="saveEvent()" [formGroup]="formData">
      <div class="row">
        <div class="col-12">
          <div class="form-group">
            <label class="control-label">Team Name</label
            ><select class="form-control" name="title" formControlName="title">
              <option *ngFor="let option of title" [value]="option.id">
                {{ option.id }} -
                {{ option.team_name }}
              </option>
            </select>
          </div>

          <div class="form-group row">
            <div class="custom-file">
              <input
                type="file"
                #story
                class="custom-file-input"
                id="customFile"
              />
              <label class="custom-file-label" for="customFile"
                >Select file</label
              >
            </div>
          </div>
        </div>
      </div>

      <div class="text-right pt-4">
        <button type="button" class="btn btn-light" (click)="closeModal()">
          Close
        </button>
        <button type="submit" class="btn btn-success save-event ml-1">
          Send File
        </button>
      </div>
    </form>
  </div>
</ng-template>
