import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { TeamRoutingModule } from './team-routing.module';
import { TeamComponent } from './team/team.component';
import { TeamListComponent } from './team-list/team-list.component';
import { TeamDetailComponent } from './team-detail/team-detail.component';
import { UIModule } from 'src/app/shared/ui/ui.module';
import { CreateTeamComponent } from './create-team/create-team.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbDropdownModule, NgbTooltipModule, NgbNavModule, NgbModalModule, NgbCarouselModule } from '@ng-bootstrap/ng-bootstrap';
import { WidgetModule } from 'src/app/shared/widget/widget.module';
import { NgApexchartsModule } from 'ng-apexcharts';
import { PerfectScrollbarModule } from 'ngx-perfect-scrollbar';
import { ArchwizardModule } from 'angular-archwizard';
import { NgxDatatableModule } from '@swimlane/ngx-datatable';
import { TeamEditComponent } from './team-edit/team-edit.component';
import { TranslateModule } from '@ngx-translate/core';
import { NgxMaskModule } from 'ngx-mask';
import { NgxSpinnerModule } from 'ngx-spinner';
import { NgbPopoverModule } from '@ng-bootstrap/ng-bootstrap';


@NgModule({
  declarations: [TeamComponent, TeamListComponent, TeamDetailComponent, CreateTeamComponent, TeamEditComponent],
  imports: [
    CommonModule,
    TeamRoutingModule,
    FormsModule,
    NgbPopoverModule,
    ReactiveFormsModule,
    UIModule,
    NgbDropdownModule,
    NgbTooltipModule,
    NgbNavModule,
    WidgetModule,
    NgApexchartsModule,
    PerfectScrollbarModule,
    ArchwizardModule,
    NgxDatatableModule,
    NgbModalModule,
    NgbCarouselModule,
    TranslateModule,
    TranslateModule,
    NgxMaskModule.forRoot(),
    NgxSpinnerModule,
  ],
})
export class TeamModule { }
