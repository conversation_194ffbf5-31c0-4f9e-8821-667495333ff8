import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { TeamComponent } from './team/team.component';
import { TeamDetailComponent } from './team-detail/team-detail.component';
import { CreateTeamComponent } from './create-team/create-team.component';
import { TeamEditComponent } from './team-edit/team-edit.component';



const routes: Routes = [
  {
    path: 'team',
    component: TeamComponent
  },
  {
    path: 'team/create',
    component: CreateTeamComponent
  },
  {
    path: 'team/detail/:id',
    component: TeamDetailComponent
  },
  {
    path: 'team/edit/:id',
    component: TeamEditComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class TeamRoutingModule { }
