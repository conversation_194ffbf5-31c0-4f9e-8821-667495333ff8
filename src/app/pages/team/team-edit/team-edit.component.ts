import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { TeamsService } from 'src/app/core/services/teams.service';
import { ActivatedRoute, Router } from '@angular/router';
import Swal from 'sweetalert2';
import { isLise } from '../../../util';
import { environment } from 'src/environments/environment';
import { NgxSpinnerService } from 'ngx-spinner';

@Component({
  selector: 'app-team-edit',
  templateUrl: './team-edit.component.html',
  styleUrls: ['./team-edit.component.scss'],
})
export class TeamEditComponent implements OnInit {

  isLise = isLise;
  // bread crumb items
  breadCrumbItems: Array<{}>;
  openPreRegistration = environment.openPreRegistration;
  createTeamForm: FormGroup;
  teamId;
  teamDetail;

  constructor(private teamService: TeamsService, private route: ActivatedRoute, private fb: FormBuilder, private router: Router, private spinner: NgxSpinnerService) { }

  ngOnInit() {
    this.route.params.subscribe(params => {
      this.teamId = params['id'];
      this.getTeamDetail();
    });


    this.breadCrumbItems = [{ label: 'Edit Team' }, { label: 'Team Wizard', active: true }];
  }

  getTeamDetail() {
    this.teamService.getTeam(this.teamId).subscribe(team => {
      this.editTeamForm(team['data']);
    });
  }

  editTeamForm(obj: any) {
    this.createTeamForm = this.fb.group({
      team_name: [this.getValue(obj['team_name'])],
      university_name: [this.getValue(obj['university_name'])],
      vehicle_name: [this.getValue(obj['vehicle_name'])],
      city_name: [this.getValue(obj['city_name']).charAt(0).toUpperCase() + this.getValue(obj['city_name']).slice(1).toLowerCase()],
      team_leader: [this.getValue(obj['team_leader'])],
      team_leader_phone: [this.getValue(obj['team_leader_phone'])],
      team_leader_email: [this.getValue(obj['team_leader_email'])],
      consultant_name: [this.getValue(obj['consultant_name'])],
      consultant_phone: [this.getValue(obj['consultant_phone'])],
      consultant_email: [this.getValue(obj['consultant_email'])],
      curator_name: [this.getValue(obj['curator_name'])],
      curator_phone: [this.getValue(obj['curator_phone'])],
      curator_email: [this.getValue(obj['curator_email'])],
      driver_name: [this.getValue(obj['driver_name'])],
      driver_phone: [this.getValue(obj['driver_phone'])],
      driver_email: [this.getValue(obj['driver_email'])],
      second_driver_name: [this.getValue(obj['second_driver_name'])],
      second_driver_phone: [this.getValue(obj['second_driver_phone'])],
      second_driver_email: [this.getValue(obj['second_driver_email'])],
      vehicle_category: [this.getValue(obj['vehicle_category'])],
      vehicle_number: [this.getValue(obj['vehicle_number'])],
      team_member_count: [this.getValue(obj['team_member_count'])],
      school_type: [this.getValue(obj['school_type'])],
    });
    this.teamDetail = obj;
  }


  submitCreateTeam() {

    console.log('this.createTeamForm.value');
    console.log(this.createTeamForm.value);
    let finalValues = this.createTeamForm.value;
    Object.keys(finalValues).forEach((key) => (finalValues[key] == null || finalValues[key] == '') && delete finalValues[key]);

    console.log('this.createTeamForm.value');
    console.log(this.createTeamForm.value);

    console.log('finalValues');
    console.log(finalValues);
    if (finalValues.vehicle_number > 99 || finalValues.vehicle_number < 1) {
      this.position('Error', 'Vehicle mumber must be between 0-99', 'OK', 'error', false);
      return;
    }
    this.spinner.show();
    this.teamService.updateTeam(finalValues, this.teamId).subscribe(team => {
      this.spinner.hide();
      this.position('Updated Successfully', 'Updated', 'OK', 'success')
        .then(res => {
          this.redirectToList();
        });
    });
  }


  getValue(val) {
    return val ? val : '';
  }

  position(title, text, confirmText, icon, dismissAll = true) {
    return Swal.fire({
      title: title,
      html: text,
      icon: icon,
      confirmButtonColor: '#34c38f',
      cancelButtonColor: '#f46a6a',
      confirmButtonText: confirmText,
    }).then(result => {
      if (result.value && dismissAll) {
      }
    });
  }

  takeRandomVehicleNumber() {
    if (!this.checkIfCaptain) {
      this.takeRandomVehicleNumberByCaptain();
    } else {
      this.takeRandomVehicleNumberByAdmin(this.teamId);
    }
  }

  takeRandomVehicleNumberByCaptain() {
    this.teamService.myTeamAutoVehicleNumber().subscribe((team: any) => {
      this.createTeamForm.patchValue({
        vehicle_number: team?.data.vehicle_number
      });
    });
  }

  takeRandomVehicleNumberByAdmin(teamId) {
    this.teamService.autoVehicleNumberForAdmin(teamId).subscribe((team: any) => {
      this.createTeamForm.patchValue({
        vehicle_number: team?.data.vehicle_number
      });
    });
  }


  redirectToList() {
    history.back();
    // this.router.navigateByUrl(`/team`);
  }

  checkIfCaptain() {
    const user = JSON.parse(localStorage.getItem('currentUser'));
    if (user['user'] && user['user']['team'] && user['user']['team']['id']) {
      return false;
    }
    return true;

  }

}
