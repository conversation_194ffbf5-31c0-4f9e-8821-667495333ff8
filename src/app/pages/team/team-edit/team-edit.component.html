<div class="container-fluid">
  <app-page-title title="Edit Team" [breadCrumbItems]="breadCrumbItems"></app-page-title>

  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body" *ngIf="teamDetail">
          <form [formGroup]="createTeamForm">
            <h4 class="card-title mb-4">{{"team._team_details" | translate}}</h4>

            <aw-wizard [navBarLayout]="'large-empty-symbols'">
              <aw-wizard-step>
                <ng-template awWizardStepSymbol>
                  <i class="fas fa-user"></i>
                </ng-template>
                <div class="col-12">
                  <div class="form-group row mb-3">
                    <label class="col-md-3 col-form-label">{{'team.form._team_name' |translate}}</label>
                    <div class="col-md-9">
                      <input class="form-control" name="team_name" formControlName="team_name"
                             type="text" placeholder="Enter Team Name">
                    </div>
                  </div>
                  <div class="form-group row mb-3">
                    <label class="col-md-3 col-form-label">{{(isLise() ? 'team.form._high_school_name' : 'team.form._university_name') |translate}}</label>
                    <div class="col-md-9">
                      <input class="form-control" name="university_name"
                             formControlName="university_name" type="text"
                             [placeholder]="'Enter '+(isLise() ? 'High School Name' : 'University Name')">
                    </div>
                  </div>
                  <div class="form-group row mb-3">
                    <label class="col-md-3 col-form-label">{{'team.form._school_type' |translate}}</label>
                    <div class="col-md-9">
                      <select id="school_type" name="school_type"
                              formControlName="school_type" class="form-control">
                        <option value="">--Please Select--</option>
                        <option value="1">International</option>
                        <option value="2">High School</option>
                      </select>
                    </div>
                  </div>

                  <div class="form-group row mb-3">
                    <label class="col-md-3 col-form-label">{{'team.form._vehicle_name' |translate}}</label>
                    <div class="col-md-9">
                      <input class="form-control" name="vehicle_name"
                             formControlName="vehicle_name" type="text"
                             placeholder="Enter Vehicle Name">
                    </div>
                  </div>
                  <div class="form-group row mb-3">
                    <label class="col-md-3 col-form-label">{{'team.form._city_name' |translate}}</label>
                    <div class="col-md-9">
                      <select id="ddlCreditCardType" formControlName="city_name"
                              name="city_name" class="form-control">
                        <option value="------">------</option>
                        <option value="Adana">Adana</option>
                        <option value="Adıyaman">Adıyaman</option>
                        <option value="Afyonkarahisar">Afyonkarahisar</option>
                        <option value="Ağrı">Ağrı</option>
                        <option value="Amasya">Amasya</option>
                        <option value="Ankara">Ankara</option>
                        <option value="Antalya">Antalya</option>
                        <option value="Artvin">Artvin</option>
                        <option value="Aydın">Aydın</option>
                        <option value="Balıkesir">Balıkesir</option>
                        <option value="Bilecik">Bilecik</option>
                        <option value="Bingöl">Bingöl</option>
                        <option value="Bitlis">Bitlis</option>
                        <option value="Bolu">Bolu</option>
                        <option value="Burdur">Burdur</option>
                        <option value="Bursa">Bursa</option>
                        <option value="Çanakkale">Çanakkale</option>
                        <option value="Çankırı">Çankırı</option>
                        <option value="Çorum">Çorum</option>
                        <option value="Denizli">Denizli</option>
                        <option value="Diyarbakır">Diyarbakır</option>
                        <option value="Edirne">Edirne</option>
                        <option value="Elazığ">Elazığ</option>
                        <option value="Erzincan">Erzincan</option>
                        <option value="Erzurum">Erzurum</option>
                        <option value="Eskişehir">Eskişehir</option>
                        <option value="Gaziantep">Gaziantep</option>
                        <option value="Giresun">Giresun</option>
                        <option value="Gümüşhane">Gümüşhane</option>
                        <option value="Hakkâri">Hakkâri</option>
                        <option value="Hatay">Hatay</option>
                        <option value="Isparta">Isparta</option>
                        <option value="Mersin">Mersin</option>
                        <option value="İstanbul">İstanbul</option>
                        <option value="İzmir">İzmir</option>
                        <option value="Kars">Kars</option>
                        <option value="Kastamonu">Kastamonu</option>
                        <option value="Kayseri">Kayseri</option>
                        <option value="Kırklareli">Kırklareli</option>
                        <option value="Kırşehir">Kırşehir</option>
                        <option value="Kocaeli">Kocaeli</option>
                        <option value="Konya">Konya</option>
                        <option value="Kütahya">Kütahya</option>
                        <option value="Malatya">Malatya</option>
                        <option value="Manisa">Manisa</option>
                        <option value="Kahramanmaraş">Kahramanmaraş</option>
                        <option value="Mardin">Mardin</option>
                        <option value="Muğla">Muğla</option>
                        <option value="Muş">Muş</option>
                        <option value="Nevşehir">Nevşehir</option>
                        <option value="Niğde">Niğde</option>
                        <option value="Ordu">Ordu</option>
                        <option value="Rize">Rize</option>
                        <option value="Sakarya">Sakarya</option>
                        <option value="Samsun">Samsun</option>
                        <option value="Siirt">Siirt</option>
                        <option value="Sinop">Sinop</option>
                        <option value="Sivas">Sivas</option>
                        <option value="Tekirdağ">Tekirdağ</option>
                        <option value="Tokat">Tokat</option>
                        <option value="Trabzon">Trabzon</option>
                        <option value="Tunceli">Tunceli</option>
                        <option value="Şanlıurfa">Şanlıurfa</option>
                        <option value="Uşak">Uşak</option>
                        <option value="Van">Van</option>
                        <option value="Yozgat">Yozgat</option>
                        <option value="Zonguldak">Zonguldak</option>
                        <option value="Aksaray">Aksaray</option>
                        <option value="Bayburt">Bayburt</option>
                        <option value="Karaman">Karaman</option>
                        <option value="Kırıkkale">Kırıkkale</option>
                        <option value="Batman">Batman</option>
                        <option value="Şırnak">Şırnak</option>
                        <option value="Bartın">Bartın</option>
                        <option value="Ardahan">Ardahan</option>
                        <option value="Iğdır">Iğdır</option>
                        <option value="Yalova">Yalova</option>
                        <option value="Karabük">Karabük</option>
                        <option value="Kilis">Kilis</option>
                        <option value="Osmaniye">Osmaniye</option>
                        <option value="Düzce">Düzce</option>
                      </select>
                    </div>
                  </div>

                  <div class="form-group row mb-3">
                    <label class="col-md-3 col-form-label">{{'team.form._vehicle_category' |translate}}</label>
                    <div class="col-md-9">
                      <select id="vehicle_category" name="vehicle_category"
                              formControlName="vehicle_category" class="form-control">
                        <option value="">--Please Select--</option>
                        <option value="electromobile">electromobile</option>
                        <option value="hydromobile">hydromobile</option>
                      </select>
                    </div>
                  </div>
                  <div class="form-group row mb-3">
                    <label class="col-md-3 col-form-label">{{'team.form._vehicle_number' |translate}}</label>
                    <div class="col-md-9 input-group">
                      <input class="form-control" name="vehicle_number"
                             formControlName="vehicle_number" type="number"
                             placeholder="Enter Vehicle Number">
                             <button class="btn ml-1 btn-secondary"
                                     [disabled]="teamDetail.vehicle_number && teamDetail.vehicle_number <= 99"
                                     (click)="takeRandomVehicleNumber()">{{'team.form._random_vehicle_number' |translate}}
                             </button>
                    </div>
                  </div>
                  <div class="form-group row mb-3">
                    <label class="col-md-3 col-form-label">{{'team.form._number_of_team_members' |translate}}</label>
                    <div class="col-md-9">
                      <input class="form-control" name="team_member_count"
                             formControlName="team_member_count" type="number"
                             placeholder="Enter Team Member Count">
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-12">
                      <div class="text-center my-3">

                        <button [disabled]="!openPreRegistration && !checkIfCaptain()" (click)="submitCreateTeam()" class="btn btn-success inner">
                          {{'team._save_team' |translate}} </button>
                      </div>
                    </div> <!-- end col-->
                  </div>
                </div>
                <!--                <ul class="list-inline wizard mb-0">-->
                <!--                  <li class="previous list-inline-item">-->
                <!--                    <button class="btn btn-primary"-->
                <!--                            awPreviousStep>Previous-->
                <!--                    </button>-->
                <!--                  </li>-->
                <!--                </ul>-->
              </aw-wizard-step>
            </aw-wizard>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
<ngx-spinner type="ball-clip-rotate-multiple" size="medium"> </ngx-spinner>
