import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { TeamsService } from 'src/app/core/services/teams.service';
import { Router } from '@angular/router';

import Swal from 'sweetalert2';
import { NgxSpinnerService } from 'ngx-spinner';

@Component({
  selector: 'app-create-team',
  templateUrl: './create-team.component.html',
  styleUrls: ['./create-team.component.scss']
})
export class CreateTeamComponent implements OnInit {
  // bread crumb items
  breadCrumbItems: Array<{}>;

  createTeamForm: FormGroup;
  errorMsgs;

  constructor(private fb: FormBuilder, private teamsService: TeamsService, private router: Router, private spinner: NgxSpinnerService) { }

  ngOnInit() {
    this.createTeamForm = this.fb.group({
      team_name: [],
      university_name: [],
      vehicle_name: [],
      city_name: [''],
      team_leader: [],
      team_leader_phone: [],
      team_leader_email: [],
      consultant_name: [],
      consultant_phone: [],
      consultant_email: [],
      curator_name: [],
      curator_phone: [],
      curator_email: [],
      driver_name: [],
      driver_phone: [],
      driver_email: [],
      second_driver_name: [],
      second_driver_phone: [],
      second_driver_email: [],
      vehicle_category: [''],
      vehicle_number: [null],
      team_member_count: [null],
      school_type: [1],
    });
    this.breadCrumbItems = [{ label: 'Create Team' }, { label: 'Team Wizard', active: true }];
  }

  submitCreateTeam() {
    const formData = this.createTeamForm.value;
    if (formData.vehicle_number > 99 || formData.vehicle_number < 1) {
      this.position('Error', 'Vehicle mumber must be between 0-99', 'error', false, false);
      return;
    }
    this.spinner.show();
    this.teamsService.createTeam(formData).subscribe(team => {
      this.spinner.hide();
      this.position('Added Successfully');
    }, error => {
      this.spinner.hide();
      this.errorMsgs = error?.error?.errors ? Object.values(error.error.errors) : error;
    });
  }


  position(title, text = null, icon = null, dismissAll = true, goToList = true) {
    Swal.fire({
      position: 'center',
      icon: icon ? icon : 'success',
      title: title,
      html: text ? text : '',
      timer: 1500,
      allowOutsideClick: false,
      showConfirmButton: true,
      showCancelButton: false
    })
      .then((willDelete) => {
        if (goToList) {
          this.redirectToList();
        }
      });
  }

  takeRandomVehicleNumber() {
    this.createTeamForm.patchValue({
      vehicle_number: 34
    });
  }


  redirectToList() {
    this.router.navigateByUrl(`/team`);
  }

}
