<div class="container-fluid">

  <!-- start page title -->
  <div class="row">
    <div class="col-12">
      <div class="text-center my-3" style="margin-left: -30px; margin-right:-30px">

        <a [routerLink]="['/team/create']" class="btn btn-success inner mr-1 col-2">
          Add Team </a>
        <a (click)="story.click()" class="btn btn-success inner col-2">
          Import Teams </a>

        <input type="file"
               accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
               #story (change)="upload(story.files, $event)" style="display: none;"/>

        <a (click)="newTeamExport()" class="btn btn-success inner ml-1 col-2">
          Export Teams </a>

        <a (click)="exportPenalties()" class="btn btn-success inner ml-1 col-2">
          Export Penalties </a>

        <a (click)="exportMembers()" class="btn btn-success inner ml-1 col-2">
          Export Members </a>


      </div>
    </div> <!-- end col-->
  </div>
  <app-page-title title="Teams" [breadCrumbItems]="breadCrumbItems"></app-page-title>
  <!-- end page title -->
  <div class="row">
    <div class="col-12">
      <!--
  	   Embed the ngx-datatable component with following property bindings/values:
       1. sorttype - allow data to be sorted on multiple columns
       2. headerHeight - Set height of table header at 50 pixels
       3. footerHeight - Set height of table footer at 50 pixels
       4. rowHeight - Set height of table rows at 50 pixels (or 'auto')
       5. rows - Derives the data for the table rows from the component class
                 property of rows
       6. columns - Derives the names for the table columns from the component
                    class property of columns
       7. columnMode - Use of standard, flex or force - Force value makes columns
                       equidistant and span the width of the parent container
       8. limit - the number of records to display before paginating the results
-->
      <!--   <ngx-datatable #mydatatable class="bootstrap" [sortType]="'multi'" [headerHeight]="50" [footerHeight]="40"
        [rowHeight]="'auto'" [rows]="rows" [columnMode]="'force'" [limit]="5" [loadingIndicator]="loadingIndicator">

        <ngx-datatable-column name="team_name">
          <ng-template ngx-datatable-cell-template let-rowIndex="rowIndex" let-value="value" let-row="row">
            <span title="Double click to edit" (dblclick)="editing[rowIndex + '-name'] = true"
              *ngIf="!editing[rowIndex + '-name']">
              {{value}}
            </span>
            <input autofocus (blur)="this.updateValue($event, 'name', rowIndex)" *ngIf="editing[rowIndex+ '-name']"
              type="text" [value]="value" />
          </ng-template>
        </ngx-datatable-column>

        <ngx-datatable-column name="university_name">
          <ng-template ngx-datatable-cell-template let-rowIndex="rowIndex" let-row="row" let-value="value">
            <span title="Double click to edit" (dblclick)="editing[rowIndex + '-university_name'] = true"
              *ngIf="!editing[rowIndex + '-university_name']">
              {{value}}
            </span>
            <select *ngIf="editing[rowIndex + '-university_name']"
              (blur)="editing[rowIndex + '-university_name'] = false"
              (change)="this.updateValue($event, 'university_name', rowIndex)" [value]="value">
              <option value="male">Male</option>
              <option value="female">Female</option>
            </select>
          </ng-template>
        </ngx-datatable-column>


        <ngx-datatable-column name="vehicle_name">
          <ng-template ngx-datatable-cell-template let-value="value">
            {{value}}
          </ng-template>
        </ngx-datatable-column>

        <ngx-datatable-column name="city_name">
          <ng-template ngx-datatable-cell-template let-value="value">
            {{value}}
          </ng-template>
        </ngx-datatable-column>

        <ngx-datatable-column name="team_leader">
          <ng-template ngx-datatable-cell-template let-value="value">
            {{value}}
          </ng-template>
        </ngx-datatable-column>

        <ngx-datatable-column name="vehicle_category">
          <ng-template ngx-datatable-cell-template let-rowIndex="rowIndex" let-row="row" let-value="value">
            <span title="Double click to edit" (dblclick)="editing[rowIndex + '-vehicle_category'] = true"
              *ngIf="!editing[rowIndex + '-vehicle_category']">
              {{value}}
            </span>
            <select *ngIf="editing[rowIndex + '-vehicle_category']"
              (blur)="editing[rowIndex + '-vehicle_category'] = false"
              (change)="this.updateValue($event, 'vehicle_category', rowIndex)" [value]="value">
              <option value="male">Male</option>
              <option value="female">Female</option>
            </select>
          </ng-template>
        </ngx-datatable-column>

      </ngx-datatable> -->

      <input
        type="text"
        class="mx-2 mb-2 form-control"
        style="width:60%;"
        placeholder="Type to filter..."
        (keyup)="updateFilter($event)"
      />
      <ngx-datatable  #table [rows]="rows" class="bootstrap" [loadingIndicator]="loadingIndicator" [columnMode]="'force'"
                     [headerHeight]="50" [footerHeight]="50" [rowHeight]="'auto'" [limit]="10" [columns]="columns"
                     [reorderable]="reorderable" [scrollbarH]="true">

        <ngx-datatable-column *ngFor="let column of columns; let i = index;" [name]="column.name"
                              [prop]="column.prop"
                              [width]="column.width"
                              [cellClass]="pickCellColor">

          <ng-template let-value="value" let-row="row" ngx-datatable-cell-template>
            <a *ngIf="column.prop === 'team_name'" href="javascript: void(0);"
               [routerLink]="['/team/detail/', row.id]">{{value}}</a>

            <span *ngIf="checkValue(column.prop)">{{value}}</span>
            <a *ngIf="column.prop === 'edit'" href="javascript: void(0);"
               [routerLink]="['/team/edit/', row.id]">Edit</a>
            <a *ngIf="column.prop === 'completed' && value === 0" href="javascript: void(0);"
               (click)="approveTeam(row.id)">Approve Now</a>
            <span *ngIf="column.prop === 'completed' && value === 1">Ready</span>
          </ng-template>
        </ngx-datatable-column>


      </ngx-datatable>
    </div>
  </div>
  <!-- end row -->


</div> <!-- container-fluid -->
<ngx-spinner type="ball-clip-rotate-multiple" size="medium"> </ngx-spinner>
