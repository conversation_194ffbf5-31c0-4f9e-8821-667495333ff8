import { Component, OnInit, ViewChild } from '@angular/core';
import { TeamsService } from '../../../core/services/teams.service';
import { FormBuilder, FormGroup } from '@angular/forms';
import Swal from 'sweetalert2';
import { Router } from '@angular/router';
import { DatatableComponent } from '@swimlane/ngx-datatable';
import { isLise } from '../../../util';
import { NgxSpinnerService } from 'ngx-spinner';

@Component({
  selector: 'app-team-list',
  templateUrl: './team-list.component.html',
  styleUrls: ['./team-list.component.scss'],
})
export class TeamListComponent implements OnInit {

  editing = {};
  rows = [];
  columns = [];

  fileToUpload: File = null;
  loadingIndicator: boolean = true;
  reorderable: boolean = true;


  // applyStyles = true;

  // cell, single, multiClick, checkbox, multi
  // selectionType = SelectionType.checkbox;


  breadCrumbItems: Array<{}>;
  filterForm: FormGroup;
  currentUser;
  protected temp: any[];
  @ViewChild(DatatableComponent) table: DatatableComponent;
  private columnsWithSearch = ['id', 'team_id', 'vehicle_number', 'team_name', 'university_name', 'vehicle_name'];


  constructor(private teamService: TeamsService, private fb: FormBuilder, private router: Router, private spinner: NgxSpinnerService) {

    this.currentUser = JSON.parse(localStorage.getItem('currentUser'));
    if (this.currentUser['user'] && this.currentUser['user']['team'] && this.currentUser['user']['team']['id']) {
      let teamLoggedId = this.currentUser['user']['team']['id'];
      this.router.navigate(['/team/detail', teamLoggedId]);
    }

    this.filterForm = this.fb.group({
      limit: [200],
    });

    setTimeout(() => { this.loadingIndicator = false; }, 5000);
    // this.fetch((data) => {
    //   this.rows = data;
    // });
  }

  ngOnInit() {
    this.breadCrumbItems = [{ label: 'Teams' }, { label: 'Team List', active: true }];

    this.columns = [
      { prop: 'team_name', name: 'Team Name' },
      { prop: 'university_name', name: (isLise() ? 'High School Name' : 'University Name') },
      { prop: 'vehicle_number', name: 'Vehicle Number' },
      { prop: 'vehicle_name', name: 'Vehicle Name' },
      { prop: 'vehicle_category', name: 'Category' },
      { prop: 'school_type_name', name: 'Team Type' },
      { prop: 'team_leader', name: 'Team Captain' },
      { prop: 'team_id', name: 'Team Id' },
      { prop: 'edit' },
      { prop: 'completed' },
    ];


    this.getTeams();
  }

  approveTeam(teamId) {
    this.spinner.show();
    this.teamService.approveTeam(teamId).subscribe(data => {
      // alert('Successfully approved');
      this.spinner.hide();
      this.position('Approved');

      this.getTeams();
    }, error => {
      this.spinner.hide();
      let message = error.error.message;
      if (message === 'validation.error') {
        message = Object.values(error.error.errors).join('\n<br>');
      }

      this.messagePopup('Error', message, 'OK', 'error');
    });
  }

  checkValue(type) {
    let s = ['team_name', 'edit', 'completed'];
    return s.indexOf(type) === -1;
  }

  upload(files, event) {
    this.fileToUpload = files.item(0);
    this.uploadFileToActivity();
  }

  uploadFileToActivity() {
    const formData: FormData = new FormData();
    formData.append('file_name', this.fileToUpload);
    console.log(formData);
    this.spinner.show();
    this.teamService.importTeam(formData).subscribe(data => {
      this.spinner.hide();
      this.position('Imported Successfully');
    }, error => {
      this.spinner.hide();
      this.position(error);
    });
  }


  position(title) {
    Swal.fire({
      position: 'center',
      icon: 'success',
      title: title,
      timer: 1500,
      allowOutsideClick: false,
      showConfirmButton: true,
      showCancelButton: false,
    })
      .then((willDelete) => {

        this.redirectToList();
      });
  }

  redirectToList() {
    this.getTeams();
  }

  getTeams() {
    this.teamService.getTeams(this.filterForm.value).subscribe(teams => {
      this.rows = teams['data'];
      this.temp = [...teams['data']];
      console.log('tem', this.temp);
    });
  }

  exportPenalties() {
    this.spinner.show();
    this.teamService.exportAllPenalty().subscribe(data => {
      this.spinner.hide();
      console.log(data);
      // alert('Exported');
      this.downLoadFile(data, 'application/ms-excel', 'EC_teams_penalties.xlsx');
    }, error => {
      this.spinner.hide();
      this.position(error);
    });
  }

  newTeamExport() {
    this.spinner.show();
    this.teamService.teamExport().subscribe(data => {
      this.spinner.hide();
      console.log(data);
      // alert('Exported');
      this.downLoadFile(data, 'application/ms-excel', 'EC_teams.xlsx');
    }, error => {
      this.spinner.hide();
      this.position(error);
    });
  }

  downLoadFile(data: any, type: string, filename) {
    let blob = new Blob([data], { type: type });

    let url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.click();

    // let pwa = window.open(url);
    // if (!pwa || pwa.closed || typeof pwa.closed == 'undefined') {
    //   alert('Please disable your Pop-up blocker and try again.');
    // }
  }


  fetch(cb) {
    const req = new XMLHttpRequest();
    req.open('GET', 'https://unpkg.com/@swimlane/ngx-datatable@6.3.0/assets/data/company.json');

    req.onload = () => {
      cb(JSON.parse(req.response));
    };

    req.send();
  }


  updateValue(event, cell, rowIndex) {
    console.log('inline editing rowIndex', rowIndex);
    this.editing[rowIndex + '-' + cell] = false;
    this.rows[rowIndex][cell] = event.target.value;
    this.rows = [...this.rows];
    console.log('UPDATED!', this.rows[rowIndex][cell]);
  }


  messagePopup(title, text, confirmText, icon) {
    Swal.fire({
      title: title,
      html: text,
      icon: icon,
      confirmButtonColor: '#34c38f',
      cancelButtonColor: '#f46a6a',
      confirmButtonText: confirmText,
    }).then(result => {
      if (result.value) {
        // this.modalService.dismissAll();
      }
    });
  }

  pickCellColor(obj) {
    // console.log(obj);
    if (obj.column.prop !== 'vehicle_category') {
      return [];
    }
    return {
      'electromobile-color': obj.row.vehicle_category === 'electromobile',
      'hydromobile-color': obj.row.vehicle_category === 'hydromobile',
    };
  }

  updateFilter(event) {
    const filterValLower = event.target.value.toLocaleLowerCase();
    const filterValOrj = event.target.value;
    // filter our data
    // assign filtered matches to the active datatable
    this.rows = this.temp.filter(item => {
      //console.log('item',item);
      for (let i = 0; i < this.columnsWithSearch.length; i++) {
        const colValue = item[this.columnsWithSearch[i]];

        // if no filter OR colvalue is NOT null AND contains the given filter
        if ((!filterValOrj || (!!colValue && colValue.toString().indexOf(filterValOrj) !== -1)) || (!filterValLower || (!!colValue && colValue.toString().toLocaleLowerCase().indexOf(filterValLower) !== -1))) {
          // found match, return true to add to result set
          return true;
        }
      }
    });


    // Whenever the filter changes, always go back to the first page
    this.table.offset = 0;
  }


  exportMembers() {
    this.spinner.show();
    this.teamService.exportAllMembers().subscribe(data => {
      // alert('Exported');
      this.spinner.hide();
      this.downLoadFile(data, 'application/ms-excel', 'EC_members.xlsx');
    }, error => {
      this.spinner.hide();
      this.position(error);
    });
  }
}
