.hidden {
  display: none;
}

.spinner-input {
  width: 89% !important;
}

.btn-outline-primary {
  border-color: #34c38f !important;
  color: #fff !important;
  background-color: #34c38f !important;
}

.btn-secondary {
  border-color: #74788d !important;
  color: #fff !important;
  background-color: #74788d !important;
}
:host ::ng-deep .popover {
  border: 1px solid #34c38f !important;
}

:host ::ng-deep .swal2-popup {
  font-size: 0.9rem !important;
  font-family: Georgia, serif;
}
:host ::ng-deep .swal2-warning {
  height: 20px !important;
  width: 20px !important;
}

.modal-body-team-member {
  display: block;
  position: relative;
  height: 600px;
  overflow: auto;
}

.overlay {
  position: fixed; /* Sit on top of the page content */
  
  width: 100%; /* Full width (cover the whole page) */
  height: 100%; /* Full height (cover the whole page) */
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5); /* Black background with opacity */
  z-index: 9999; /* Specify a stack order in case you're using a different order for other elements */
  cursor: pointer; /* Add a pointer on hover */
}

.disabled-member-list {
  pointer-events: none;
  opacity: 0.6;
}

.enable-member-list-header {
  color: red;
  opacity: 1;
}

