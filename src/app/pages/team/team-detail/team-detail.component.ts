import { Component, OnInit } from '@angular/core';
import { Form<PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';

import { TeamsService } from '../../../core/services/teams.service';
import { AssetService } from '../../../core/services/asset.service';

import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { AppointmentService } from 'src/app/core/services/appointment.service';
import { environment } from 'src/environments/environment';
import Swal from 'sweetalert2';
import { TeamRoleEnum } from '../team-role.enum';
import { UniformSizesEnum } from '../uniform-sizes.enum';
import { LoaderService } from '../../../core/services/loader.service';
import { TranslateService } from '@ngx-translate/core';
import { isLise } from '../../../util';
import { NgxSpinnerService } from 'ngx-spinner';
import { RaceService } from 'src/app/core/services/race.service';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

@Component({
  selector: 'app-team-detail',
  templateUrl: './team-detail.component.html',
  styleUrls: ['./team-detail.component.scss'],
})
export class TeamDetailComponent implements OnInit {

  isLise = isLise;
  // bread crumb items
  breadCrumbItems: Array<{}>;
  teamId;

  window = window;

  penaltyForm: FormGroup;
  memberForm: FormGroup;
  teamNameForm: FormGroup;
  fileToUpload: File = null;
  teamDetailForm: FormGroup;
  promotionForm: FormGroup;
  memberCount = 0;
  memberCountInArea = 0;
  teamRoles = Object.values(TeamRoleEnum);
  uniformSizes = Object.values(UniformSizesEnum);

  env = environment;
  apiUrl = this.env.apiUrl;
  showNavigationIndicators: any;
  teamDetail;
  teamAppointments;
  currentUser;
  sizeImage: string;
  updateType: boolean = false;
  showParent = true;
  uploadedImage: any;

  loading: any;
  loadingExport: boolean;

  formsAttachmentForm: FormGroup;
  disableSaveButton = false;
  attachments = [];
  filesLoading = [];
  attachmentOption = [
    { id: 'contract', name: 'Contract' },
    { id: 'team_photo', name: 'Team Photo' },
    { id: 'ttr', name: 'Technical Design Report' },
    { id: 'Other', name: 'Other' },
  ];
  allRace$: Observable<any>;

  constructor(
    private raceService: RaceService, private assetService: AssetService,
    private modalService: NgbModal, private fb: FormBuilder,
    private route: ActivatedRoute, private teamService: TeamsService,
    private appointmentService: AppointmentService,
    private loader: LoaderService,
    private translateService: TranslateService,
    private spinner: NgxSpinnerService,
  ) { }

  ngOnInit() {
    this.allRace$ = this.raceService.getAllRaceSession().pipe(map(race => {
        return race['data'].map(session => {
          return {
            id: session['id'],
            name: `${session['race']?.['name']} -- ${session['name']}`,
          };
        });
      },
    ));
    this.route.params.subscribe(params => {
      this.teamId = params['id'];
      this.getTeamDetail();
      // this.getAppointments();
      this.penaltyForm = this.fb.group({
        team_id: [],
        subject: [],
        violation: [],
        penalty: [],
        session_id: [],
        type: [],
        conclusion: [],
      });
    });

    this.teamNameForm = this.fb.group({
      team_name: ['', Validators.required],
    });
    this.memberForm = this.fb.group({
      first_name: [null, Validators.required],
      last_name: [null, Validators.required],
      email: [null, Validators.required],
      team_id: [],
      role_in_team: [null, Validators.required],
      identity_number: [null, Validators.required],
      not_tc_citizen: [false, Validators.required],
      phone_number: [null, Validators.required],
      birthday: [null, Validators.required],
      gender: [null, Validators.required],
      in_area: [null, Validators.required],
      parent_name: [],
      parent_phone: [],
      uniform_size: [null, Validators.required],
      picture_id: [null],
      picture_url: [null],
    });

    this.formsAttachmentForm = this.fb.group({
      id: [0],
      team_id: [this.teamId],
      name: ['', Validators.required],
      surname: ['', Validators.required],
      description: ['', Validators.required],
      pdf: [''],
      excel: [''],
      word: [''],
      photo: [''],
      video: [''],
    });


    this.translateService.get(['_teams', '_team_overview']).subscribe(i => {
      this.breadCrumbItems = [{ label: i['_teams'] },
        { label: i['_team_overview'], active: true }];
    });

  }


  checkIfCaptain() {
    this.currentUser = JSON.parse(localStorage.getItem('currentUser'));
    if (this.currentUser['user'] && this.currentUser['user']['team'] && this.currentUser['user']['team']['id']) {
      return false;
    }
    return true;

  }

  deleteTeamMember(memberId) {
    this.spinner.show();
    this.teamService.deleteTeamMember(memberId).subscribe(data => {
      //alert('Deleted');
      this.spinner.hide();
      this.position('Team Member Successfully Deleted', 'Team Member Deleted', 'OK', 'success');
      this.getTeamDetail();
    }, error => {
      this.spinner.hide();
      this.position('Error', error['message'], 'OK', 'success', false);
    });
  }


  getTeamDetail() {
    let teamObs;
    if (!this.checkIfCaptain()) {
      teamObs = this.teamService.getMyTeam();
    } else {
      teamObs = this.teamService.getTeam(this.teamId);
    }
    teamObs.subscribe(team => {
      this.teamId = team.data.id;
      this.teamDetail = team.data;
      this.memberCount = team.data.members.length;
      this.memberCountInArea = team.data.members.filter(member => member.in_area).length;
      this.teamAppointments = team.data.appointments || [];
      this.promotionForm = this.fb.group({
        name: [],
        quantity: [],
        team_id: [this.teamId],
      });
    });

  }

  promotions = [];

  checkValue(event) {
    if (event.target.checked) {
      this.promotions.push(event.target.value);
    } else {
      var index = this.promotions.indexOf(event.target.value);
      this.promotions.splice(index, 1);
    }
  }

  listPromotion() {
    this.spinner.show();
    this.teamService.listPromotion().subscribe(data => {
      //alert('Promotion Successfully added');
      this.spinner.hide();
      this.position('Promotion Successfully added', 'Promotion Removed', 'OK', 'success');
      this.getTeamDetail();
      this.modalService.dismissAll();
    }, error => {
      this.spinner.hide();
      this.position('Error', error['message'], 'OK', 'success', false);
    });
  }

  deletePromotion(id) {
    this.spinner.show();
    this.teamService.deletePromotion(id).subscribe(data => {
      // alert('Successfully deleted');
      this.spinner.hide();
      this.position('Removed Successfully', 'Promotion Removed', 'OK', 'success');

      this.getTeamDetail();
    });
  }

  deletePenalty(id) {
    this.spinner.show();
    this.teamService.deletePenalty(id).subscribe(data => {
      //alert('Successfully deleted');
      this.spinner.hide();
      this.position('Deleted Successfully', 'Penalty Deleted', 'OK', 'success');
      this.getTeamDetail();
    });
  }

  addPromotion() {

    this.spinner.show();
    const promotionNumbers = this.promotions.map((promotion) => {
      const element = document.getElementById(promotion + '-number') as HTMLInputElement;
       return element.value;
    });

    this.promotionForm.patchValue({ name: this.promotions, quantity: promotionNumbers });
    console.log(this.promotionForm.value);
    this.teamService.addPromotion(this.promotionForm.value).subscribe(res => {
      this.spinner.hide();

      // alert('Promotion Successfully added');
      this.position('Added Successfully', 'Promotion Added', 'OK', 'success');

      this.getTeamDetail();
      this.modalService.dismissAll();
    }, error => {
      this.spinner.hide();
      //alert(error.error.message);
      this.position('Error', error['message'], 'OK', 'success', false);
    });
  }

  exportPromotions() {
    this.spinner.show();
    this.teamService.exportPromotions(this.teamId).subscribe(data => {
      console.log(data);
      this.spinner.hide();
      this.downLoadFile(data, 'application/ms-excel', this.teamDetail.team_name + '_team_promotions.xlsx');
    }, error => {
      this.spinner.hide();
      this.position('Error', error['message'], 'OK', 'success', false);
    });
  }

  downLoadFile(data: any, type: string, filename: string) {
    let blob = new Blob([data], { type: type });

    let url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.click();

    // let pwa = window.open(url);
    // if (!pwa || pwa.closed || typeof pwa.closed == 'undefined') {
    //   alert('Please disable your Pop-up blocker and try again.');
    // }
  }


  getTelHref(phoneNumber: string): string {
    // return `tel:+9${phoneNumber}`;
    return 'javascript:void(0)';
  }

  getEmailHref(email: string): string {
    // return `mailto: :${email}`;
    return 'javascript:void(0)';
  }

  openPromotion(promotionModal: any) {
    this.modalService.open(promotionModal, { centered: true, windowClass: 'modal-holder' });
  }

  openAttachments(attachmentModal: any) {
    this.modalService.open(attachmentModal, { centered: true, windowClass: 'modal-holder' });
  }

  openFormsAttachments(formAttachmentModal: any, formsElement = null) {
    if (formsElement) {
      this.formsAttachmentForm.patchValue({
        id: formsElement.id,
        name: formsElement.name,
        surname: formsElement.surname,
        description: formsElement.description,
      });
      formsElement.attachments.forEach(element => {
        this.formsAttachmentForm.patchValue({
          [element.type.toLowerCase()]: element.asset.path.split('/').reverse()[0],
        });
      });
    } else {
      this.formsAttachmentForm.reset();
    }
    this.formsAttachmentForm.patchValue({ team_id: this.teamId });
    this.modalService.open(formAttachmentModal, { centered: true, windowClass: 'modal-holder' });
  }

  addPenalty() {
    this.spinner.show();
    this.penaltyForm.patchValue({ team_id: this.teamId });
    console.log(this.penaltyForm.value);
    // this.penaltyForm.patchValue({ session_id: this.sessionId });
    this.teamService.addPenalty(this.penaltyForm.value).subscribe(data => {
      this.spinner.hide();
      this.position('Added Successfully', 'Penalty Added', 'OK', 'success');
      this.getTeamDetail();
    });
  }

  exportPenalties() {
    this.spinner.show();
    this.teamService.exportPenalties(this.teamId).subscribe(data => {
      console.log(data);
      this.spinner.hide();
      this.downLoadFile(data, 'application/ms-excel', this.teamDetail.team_name + '_team_penalties.xlsx');
    }, error => {
      this.spinner.hide();
      this.position('Error', error['message'], 'OK', 'error', false);
    });
  }

  getAttachmentName(name: string) {
    const attachment = this.attachmentOption.find(data => data.id === name);
    return attachment?.name;
  }

  updateMemberFromTable(member: any) {
    this.updateType = true;
    this.addMember(member);
  }

  addMember(member = null) {
    this.memberForm.patchValue({ team_id: this.teamId });
    if (this.updateType) {
      this.spinner.show();

      this.teamService.updateMember(member || this.memberForm.value, member?.id || this.memberId).subscribe(data => {
        this.position('Updated Successfully', 'Member Updated', 'OK', 'success');
        this.spinner.hide();

        this.getTeamDetail();
      });
    } else {
      this.spinner.show();

      this.teamService.addMember(this.memberForm.value).subscribe(data => {
        this.position('Added Successfully', 'Member Added', 'OK', 'success');
        this.spinner.hide();

        this.getTeamDetail();
      });
    }
  }

  checkTeam() {
    this.loader.isLoading.next(true);
    this.spinner.show();
    if (!this.checkIfCaptain())
      this.checkTeamByCaptain();
    else
      this.checkTeamByAdmin(this.teamId);
  }

  checkTeamByCaptain() {
    this.teamService.checkMyTeam().subscribe((response: any) => {
      this.loader.isLoading.next(false);
      this.spinner.hide();

      this.prepareCheckTeamContent(response.data);

    }, () => {
      this.loader.isLoading.next(false);
      this.spinner.hide();
    });
  }

  checkTeamByAdmin(teamId) {
    this.teamService.checkTeamByAdmin(teamId).subscribe((response: any) => {
      this.loader.isLoading.next(false);
      this.spinner.hide();
      this.prepareCheckTeamContent(response.data);

    }, () => {
      this.loader.isLoading.next(false);
      this.spinner.hide();
    });
  }

  prepareCheckTeamContent(data: any) {
    const teamValidateError = data.teamValidateError?.validateErrors;
    let text = '<table class="table table-bordered table-stripe"><thead><tr><th scope="col">Field</th><th scope="col">Error</th></tr></thead><tbody>';
    if (teamValidateError) {
      text += '<tr><td scope="row">Takım</td><td scope="row"<p>';
      Object.keys(teamValidateError).forEach(element => {
        text += teamValidateError[element] + '<br>';
      });
    }
    const memberValidateErrors = data.memberValidateErrors;
    if (memberValidateErrors) {
      memberValidateErrors?.forEach(element => {
        text += '<tr><td scope="row">' + element.memberName + '</td><td scope="row"<p>';
        const validateErrors = element['validateErrors '];
        Object.keys(validateErrors).forEach(error => {
          text += validateErrors[error] + '<br>';
        });
      });
    }
    text += '</p></td></tbody></table>';

    if (!Object.values(teamValidateError || {})?.length && !memberValidateErrors?.length) {
      Swal.fire({
        position: 'center',
        icon: 'success',
        title: this.translateService.instant('_team_validation.valid'),
      })
        .then((willDelete) => {
        });
      return;
    }
    Swal.fire({
      title: this.translateService.instant('_team_validation.member_error'),
      html: text,
      icon: 'warning',
      width: '800px',
    });
  }

  uploadMemberPicture(files, $event) {
    const formData: FormData = new FormData();
    formData.append('file', files.item(0));
    this.uploadedImage = files.item(0);
    console.log('upload', this.uploadedImage);
    this.loader.isLoading.next(true);

    if (['image/jpeg', 'image/jpg', 'image/png'].indexOf(this.uploadedImage.type) === -1) {
      return this.position('Error', 'File not supported', 'OK', 'error', false);
    }
    this.spinner.show();
    this.assetService.upload(formData)
      .subscribe((data: any) => {
        console.log(data);
        this.memberForm.patchValue({
          picture_id: data.id,
        });
        this.loader.isLoading.next(false);
        this.spinner.hide();
      });
  }

  position(title, text, confirmText, icon, dismissAll = true) {
    Swal.fire({
      title: title,
      html: text,
      icon: icon,
      confirmButtonColor: '#34c38f',
      cancelButtonColor: '#f46a6a',
      confirmButtonText: confirmText,
    }).then(result => {
      if (result.value && dismissAll) {
        this.modalService.dismissAll();
      }
    });
  }

  penalty(penaltyModal: any) {
    this.modalService.open(penaltyModal, { centered: true, windowClass: 'modal-holder' });
  }

  memberId: number;

  openModal(penaltyModal: any, modalType?, memberData?) {

    this.teamNameForm.patchValue({ team_name: this.teamDetail.team_name });
    if (modalType) {
      this.updateType = true;
      this.memberId = memberData['id'];
      this.memberForm.patchValue({
        first_name: memberData.first_name,
        last_name: memberData.last_name,
        email: memberData.email,
        role_in_team: memberData.role_in_team,
        identity_number: memberData.identity_number,
        not_tc_citizen: memberData.not_tc_citizen,
        phone_number: memberData.phone_number,
        birthday: memberData.birthday,
        gender: memberData.gender,
        in_area: memberData.in_area,
        parent_name: memberData.parent_name,
        parent_phone: memberData.parent_phone,
        uniform_size: memberData.uniform_size,
        picture_url: memberData.picture_url,
        picture_id: null,
      });
    } else {
      this.updateType = false;

      this.memberForm.patchValue({
        first_name: null,
        last_name: null,
        email: null,
        role_in_team: null,
        identity_number: null,
        not_tc_citizen: false,
        phone_number: null,
        birthday: null,
        gender: null,
        in_area: null,
        parent_name: null,
        parent_phone: null,
        uniform_size: null,
        hes_code: null,
        picture_id: null,
      });
    }
    this.modalService.open(penaltyModal, { centered: true, windowClass: 'modal-holder', backdrop: 'static' });
  }

  upload(files, event) {
    this.spinner.show();
    console.log('test');
    this.fileToUpload = files.item(0);
    this.uploadFileToActivity(files.item(0));
  }

  uploadFileToActivity(fileToUpload) {
    const formData: FormData = new FormData();
    formData.append('file', fileToUpload);
    this.assetService.upload(formData).subscribe(data => {
      this.spinner.hide();
      console.log(data);
      this.attachFile(data['id']);
    });
  }

  saveFormsAttachmentInfo(files: any, type: string, index: number) {
    //this.spinner.show();
    this.filesLoading[index] = true;
    this.disableSaveButton = true;
    this.uploadFormsAttachmentToActivity(files.item(0), type, index);
  }

  uploadFormsAttachmentToActivity(file, type, index) {
    const formData: FormData = new FormData();
    formData.append('file', file);
    this.assetService.upload(formData).subscribe(data => {
      console.log(data);
      this.formsAttachmentForm.patchValue({ [type.toLowerCase()]: file.name });
      this.attachments.push({ asset_id: data['id'], type: type });
      this.filesLoading[index] = false;
      this.disableSaveButton = false;
      //this.spinner.hide();
    });
  }

  attachForms() {
    let attachObj = Object.assign({}, this.formsAttachmentForm.value);
    attachObj.attachments = this.attachments;
    attachObj.team_id = this.teamId.toString();
    this.spinner.show();
    if (attachObj.id > 0) {
      this.updateAttachFormss(attachObj);
    } else {
      this.addAttachForms(attachObj);
    }
  }

  addAttachForms(addModel) {
    this.teamService.attachFormsAdd(addModel).subscribe(data => {
      this.position('Added Successfully', 'Forms Added', 'OK', 'success');
      this.spinner.hide();
      this.getTeamDetail();
      this.modalService.dismissAll();
    });
  }

  updateAttachFormss(updateModel) {
    this.teamService.attachFormsUpdate(updateModel).subscribe(data => {
      this.position('Updated Successfully', 'Forms Updated', 'OK', 'success');
      this.spinner.hide();
      this.getTeamDetail();
      this.modalService.dismissAll();
    });
  }

  attachFile(asset_id) {
    let attachObj = { 'asset_id': asset_id, 'type': this.fileType };
    this.teamService.attachFile(attachObj, this.teamId).subscribe(data => {
      //alert('File uploaded successfully');
      this.position('File uploaded successfully', 'Added', 'OK', 'success');
      this.getTeamDetail();
      this.modalService.dismissAll();
    });
  }

  fileType;

  onChange(event) {
    this.fileType = event.target.value;
  }

  getAppointments() {
    this.appointmentService.getTeamAppointments(this.teamId).subscribe(data => {
      this.teamAppointments = data['data'];
    });
  }

  approveErrors;

  approveTeam() {
    this.spinner.show();

    this.teamService.approveTeam(this.teamId).subscribe(data => {
      // alert('Successfully approved');
      this.spinner.hide();

      this.getTeamDetail();
      this.position('Approved', 'Team Approved', 'OK', 'success');
    });
  }


  getPath(path: string) {
    //console.log(`${this.apiUrl}storage/${path}`);
    return `${this.apiUrl}storage/${path}`;
  }

  exportMembers() {
    this.spinner.show();
    this.teamService.exportMembers(this.teamId).subscribe(data => {
      this.spinner.hide();
      console.log(data);
      this.downLoadFile(data, 'application/ms-excel', this.teamDetail.team_name + '_team_members.xlsx');
    }, error => {
      this.spinner.hide();
      this.position('Error', error['message'], 'OK', 'error', false);
    });
  }

  updateTeamName() {
    console.log(this.teamNameForm.value);
    this.spinner.show();
    let finalValues = this.teamNameForm.value;
    Object.keys(finalValues).forEach((key) => (finalValues[key] == null || finalValues[key] == '') && delete finalValues[key]);

    if (!this.checkIfCaptain()) {
      this.teamService.updateMyTeam(finalValues).subscribe((team: any) => {
        this.spinner.hide();
        this.position('Added Successfully', 'Updated', 'OK', 'success');
        this.teamDetail = team.data;

      }, error => {
        this.spinner.hide();
        this.position('Error', error['message'], 'OK', 'error', false);
      });
    } else {
      this.teamService.updateTeam(finalValues, this.teamId).subscribe((team: any) => {
        this.spinner.hide();
        this.teamDetail = team.data;
        this.position('Added Successfully', 'Updated', 'OK', 'success');

      }, error => {
        this.spinner.hide();
        this.position('Error', error['message'], 'OK', 'error', false);
      });
    }

  }

  showUniformSizes() {

    if (this.sizeImage) {
      this.sizeImage = null;
      return;
    }
    this.uniformSizeChange();
  }

  uniformSizeChange() {
    if (this.memberForm.get('gender').value === 'male') {
      this.sizeImage = 'assets/images/tshirt-size.png';
    } else {
      this.sizeImage = 'assets/images/tshirt-size.png';
    }
  }

  birtDayChange() {

    var birtday = new Date(this.memberForm.get('birthday').value);
    var _birtday = new Date(
      birtday.getFullYear(),
      birtday.getMonth(),
      birtday.getDate(),
    ).getTime();

    var today = new Date();
    var _today = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate(),
    ).getTime();
    const minute = 1000 * 60;
    const hour = minute * 60;
    const day = hour * 24;
    const year = day * 365;
    if ((_today - _birtday) / year < 18) {
      this.showParent = true;
    } else {
      this.showParent = false;
    }
    //this.showParent = this.memberForm.get('birthday').value > '2003-08-29';
  }

  exportQRPdf() {
    this.spinner.show();
    this.teamService.exportQrCodes(this.teamId)
      .subscribe(data => {
        this.spinner.hide();
        console.log(data);
        this.downLoadFile(data, 'application/pdf', this.teamDetail.team_name + '_qr_code.pdf');
      }, error => {
        this.spinner.hide();
      });
  }

  checkNumValue(event: Event) {
    const input = event.target as HTMLInputElement;
    if (!input.value || Number(input.value) < 1) {
      input.value = '1';
    }
  }
}
