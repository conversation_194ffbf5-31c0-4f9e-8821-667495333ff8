<div class="container-fluid">
  <!-- start page title -->
  <app-page-title
    [title]="'member.form.team_member_page' | translate"
    [breadCrumbItems]="breadCrumbItems"
  >
  </app-page-title>
  <!-- end page title -->

  <div class="row" *ngIf="teamDetail">
    <div class="col-lg-8">
      <div class="card">
        <div class="card-body">
          <div class="media">
            <img
              [src]="teamDetail.logo_url || 'assets/images/companies/img-1.png'"
              alt=""
              class="avatar-sm mr-4"
            />
            <div class="media-body">
              <div class="d-flex justify-content-between">
                <div class="d-flex flex-row flex-nowrap">
                <h5 class="text-truncate font-size-15 text-wrap">
                  {{ teamDetail.team_name }}
                </h5>
                </div>
                <div ngbDropdown placement="left-auto" class="d-sm-none">
                  <button type="button" class="btn btn-outline-primary" id="mobile-actions" ngbDropdownToggle>
                    <i class="fa fa-angle-down" aria-hidden="true"></i></button>
                  <div ngbDropdownMenu aria-labelledby="mobile-actions" class="dropdown-menu">
                    <!-- <button ngbDropdownItem
                    (click)="openModal(changeTeamName)"
                    class="btn btn-outline-primary mb-1"
                  >
                    {{'team._change_team_name' | translate}}
                  </button> -->
                  <button ngbDropdownItem
                    [disabled]="!env.openPreRegistration && !checkIfCaptain()"
                    href="javascript: void(0);"
                    class="btn btn-secondary mb-1"
                    (click)="checkTeam()"
                    >{{(!checkIfCaptain()? 'team._check_my_team': 'team._check_team') |translate}}</button
                  >

                  <button ngbDropdownItem
                    [disabled]="!env.openPreRegistration && !checkIfCaptain()"
                    href="javascript: void(0);"
                    class="btn btn-secondary"
                    [routerLink]="['/team/edit/', teamDetail.id]"
                    >{{'team._edit' |translate}}</button
                  >
                  <button ngbDropdownItem
                    class="btn btn-secondary mt-1"
                    *ngIf="!teamDetail.completed && checkIfCaptain()"
                    href="javascript: void(0);"
                    (click)="approveTeam()"
                    >{{'team._approve_now' |translate}}</button
                  >
                  </div>
                </div>

                <div class="d-none d-sm-flex">
<!--                  <button-->
<!--                    (click)="openModal(changeTeamName)"-->
<!--                    class="btn btn-success mr-1"-->
<!--                  >-->
<!--                  {{'team._change_team_name' | translate}}-->
<!--                  </button>-->
                  <button
                    [disabled]="!env.openPreRegistration && !checkIfCaptain()"
                    href="javascript: void(0);"
                    class="btn btn-success mr-1"
                    (click)="checkTeam()"
                    >{{(!checkIfCaptain()? 'team._check_my_team': 'team._check_team') |translate}}</button
                  >
                  <button
                    [disabled]="!env.openPreRegistration && !checkIfCaptain()"
                    href="javascript: void(0);"
                    class="btn btn-secondary"
                    [routerLink]="['/team/edit/', teamDetail.id]"
                    >{{'team._edit' |translate}}</button
                  >

                  <button
                    class="ml-1 btn btn-secondary"
                    *ngIf="!teamDetail.completed && checkIfCaptain()"
                    href="javascript: void(0);"
                    (click)="approveTeam()"
                    >{{'team._approve_now' |translate}}</button
                  >
                </div>
              </div>

              <div class="d-flex flex-row flex-nowrap">
              <p class="text-muted">
                {{ teamDetail.university_name }} - {{ teamDetail.city_name }}
              </p>
              </div>
            </div>
          </div>
          <h5 class="font-size-15 mt-4">
            {{ "member.form.vehicle_name" | translate }}:
          </h5>
          <p class="text-muted">
            ({{ teamDetail.vehicle_number }}) {{ teamDetail.vehicle_name }} -
            {{ teamDetail.vehicle_category }}
          </p>
          <div class="row task-dates">
            <div class="col-sm-4 col-6">
              <div class="mt-4">
                <h5 class="font-size-14">
                  <i class="bx bx-calendar mr-1 text-primary"></i>
                  {{ "_created_at" | translate }}
                </h5>
                <p class="text-muted mb-0">
                  {{ teamDetail.created_at | date: "short" }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- end col -->

    <div class="col-lg-4" *ngIf="checkIfCaptain()">
      <div class="card">
        <div class="card-body">
          <h4 class="card-title mb-4">
            Penalties
            <div class="float-right">
              <a
                (click)="penalty(penaltyModal)"
                title="Add Penalty"
                class="btn btn-success mr-1"
                style="color: white"
                ><i class="bx bxs-message-square-add"></i
              ></a>

              <a
                (click)="exportPenalties()"
                title="Export Penalties"
                class="btn btn-success"
                style="color: white"
                ><i class="bx bx-table"></i
              ></a>
            </div>
          </h4>
          <div class="table-responsive">
            <table class="table table-nowrap table-hover mb-0">
              <thead>
                <tr>
                  <th scope="col">Subject</th>
                  <th scope="col">Violation</th>
                  <th scope="col">Penalty</th>
                  <th scope="col">Session</th>
                  <th scope="col">Type</th>
                  <th scope="col">Conclusion</th>
                  <th scope="col"></th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let penalty of teamDetail?.penalties">
                  <td>{{ penalty.subject }}</td>
                  <td>{{ penalty.violation }}</td>
                  <td>{{ penalty.penalty }}</td>
                  <td>{{penalty.session_id }}</td>
                  <td>{{ penalty.type }}</td>
                  <td>{{ penalty.conclusion }}</td>
                  <td>
                    <a (click)="deletePenalty(penalty.id)">
                      <i class="bx bxs-trash"></i
                    ></a>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- end col -->
  </div>
  <!-- end row -->
  <div class="row" *ngIf="teamDetail">
    <div class="col-lg-8" [ngClass]="{'disabled-member-list': !env.openPreRegistration && !checkIfCaptain()}">

      <div class="card">
        <div class="card-body">
          <div class="d-flex">
          <h4 class="card-title mb-4">
            {{ "member.form.team_member_title" | translate }}
            <i *ngIf="!checkIfCaptain() && !isLise()" class="fa fa-info-circle ml-2 d-md-none"
            ngbPopover="Takımlar, takım kaptanı ve varsa danışman dahil en az beş (5), en fazla on beş (15) kişiden oluşmalıdır."
            popoverTitle="Üye Sayısı" aria-hidden="true"
            placement="bottom">
            </i>
            <p *ngIf="!checkIfCaptain() && !isLise()" class="d-none d-md-flex" style="font-size: 10px;">
              Takımlar, takım kaptanı ve varsa danışman dahil en az beş (5), en fazla on beş (15) kişiden oluşmalıdır.
            </p>

              <i *ngIf="!checkIfCaptain() && isLise()" class="fa fa-info-circle ml-2 d-md-none"
            ngbPopover="Takımlar, danışman öğretmen(ler) dahil en az beş (5), en fazla on beş (15) kişiden oluşmalıdır."
            popoverTitle="Üye Sayısı" aria-hidden="true"
            placement="bottom">
            </i>
            <p *ngIf="!checkIfCaptain() && isLise()" class="d-none d-md-flex" style="font-size: 10px;">
              Takımlar, danışman öğretmen(ler) dahil en az beş (5), en fazla on beş (15) kişiden oluşmalıdır.
            </p>
            <p style="font-size: 12px;">
              {{memberCount}} members ({{memberCountInArea}} members in area)
            </p>
          </h4>
           <div class="float-right" style="margin-left: auto; margin-right: 0px">
              <!--
              <button
                *ngIf="checkIfCaptain()"
                (click)="checkHesCode()"
                class="btn btn-secondary mr-1"
              >
                {{ "member._check_hes_code" | translate }}
                {{ hesCodeLoading ? "..." : "" }}
              </button>-->
              <button
                *ngIf="checkIfCaptain()"
                (click)="exportQRPdf()"
                class="btn btn-info mr-1"
              >
                {{ "member._export_qr_code" | translate }}
              </button>

              <button
                [disabled]="!env.openFormsAndTeamFiles && !checkIfCaptain()"
                (click)="openModal(memberModal)"
                title="add Member"
                class="btn btn-success mr-1"
                style="color: white"
                ><i class="bx bxs-message-square-add"></i
              ></button>

              <a
                *ngIf="checkIfCaptain()"
                (click)="exportMembers()"
                title="Export Members"
                class="btn btn-success"
                style="color: white"
                ><i class="bx bx-table"></i
              ></a>
            </div>
          </div>
          <h2 class="enable-member-list-header" *ngIf="!env.openPreRegistration && !checkIfCaptain()"> {{ 'team._pre_registration_period_expired'| translate}} </h2>
          <div class="table-responsive">
            <table class="table table-centered table-nowrap">
              <tbody>
                <tr *ngFor="let member of teamDetail.members; index as i">
                  <td style="width: 30px">{{i + 1}}</td>
                  <td style="width: 50px">
                    <img
                      [src]="
                        member.picture_url
                          ? apiUrl + 'storage' + member.picture_url
                          : 'assets/images/users/avatar-2.jpg'
                      "
                      class="rounded-circle avatar-xs"
                      alt=""
                    />
                  </td>
                  <td>
                    <h5 class="font-size-14 m-0">
                      <a
                        [href]="getTelHref(member.phone_number)"
                        class="text-dark"
                        >{{ member.first_name }} {{ member.last_name }} - </a
                      >
                      <span _ngcontent-xpi-c252="" class="text-muted">
                        {{
                          (member.role_in_team == "academicAdvisor" && isLise()
                            ? "member.team_roles.lise_" + member.role_in_team
                            : "member.team_roles." + member.role_in_team
                          ) | translate
                        }}</span
                      >
                    </h5>
                  </td>
                  <td>
                    <input type="checkbox" [(ngModel)]="member.in_area" [checked]="member.in_area" (change)="updateMemberFromTable(member)">
                    {{ "_in_area" | translate }}
                    <!-- <div *ngIf="member.in_area" class="badge badge-secondary">
                      {{ "_in_area" | translate }}
                    </div> -->
                  </td>
                  <td style="width: 150px">
                    <button
                      [disabled]="!env.openFormsAndTeamFiles && !checkIfCaptain()"
                      (click)="openModal(memberModal, true, member)"
                      class="btn btn-success ml-1"
                    >
                      {{ "_update" | translate }}
                    </button>
                    <button
                      [disabled]="!env.openFormsAndTeamFiles && !checkIfCaptain()"
                      (click)="deleteTeamMember(member.id)"
                      class="btn btn-danger ml-1"
                    >
                      <i class="bx bxs-trash"></i>
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-4" *ngIf="checkIfCaptain()">
      <div class="card">
        <div class="card-body">
          <h4 class="card-title mb-4">Team Gallery</h4>
          <ngb-carousel [showNavigationIndicators]="showNavigationIndicators">
            <ng-container *ngFor="let item of teamDetail?.files">
              <ng-template ngbSlide *ngIf="item.type === 'team_photo'">
                <img
                  [src]="getPath(item?.asset?.path)"
                  [alt]="item.type"
                  class="d-block img-fluid"
                  width="100%"
                  height="150"
                />
              </ng-template>
            </ng-container>
          </ngb-carousel>
        </div>
      </div>
    </div>
  </div>

  <div class="row" *ngIf="teamAppointments && checkIfCaptain()">
    <div class="col-lg-4">
      <div class="card">
        <div class="card-body">
          <h4 class="card-title mb-4">Appointments</h4>
          <div class="table-responsive">
            <table class="table table-nowrap table-hover mb-0">
              <thead>
                <tr>
                  <th scope="col">#id</th>
                  <th scope="col">Date</th>
                  <th scope="col">Status</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let item of teamAppointments">
                  <th scope="row">{{ item.team_id }}</th>
                  <td>{{ item.start_time }}</td>
                  <td>{{ item.status }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    <!-- end col -->
    <div class="col-lg-4">
      <div class="card">
        <div class="card-body">
          <h4 class="card-title mb-4">
            Team Attachments
            <div class="float-right">
              <a
                (click)="openAttachments(attachmentModal)"
                title="add Attachments"
                class="btn btn-success"
                style="color: white"
                ><i class="bx bxs-message-square-add"></i
              ></a>
            </div>
          </h4>
          <div class="table-responsive">
            <table class="table table-nowrap table-centered table-hover mb-0">
              <tbody>
                <tr *ngFor="let item of teamDetail?.files">
                  <td style="width: 45px">
                    <div class="avatar-sm">
                      <span
                        class="avatar-title rounded-circle bg-soft-primary text-primary font-size-24"
                      >
                        <i class="bx bxs-file-doc"></i>
                      </span>
                    </div>
                  </td>
                  <td>
                    <h5 class="font-size-14 mb-1">
                      <a href="javascript: void(0);" class="text-dark">{{
                        getAttachmentName(item.type)
                      }}</a>
                    </h5>
                    <small>{{ item.created_at | date: "medium" }}</small>
                  </td>
                  <td>
                    <div class="text-center">
                      <a
                        [href]="getPath(item?.asset?.path)"
                        target="_blank"
                        class="text-dark"
                        ><i class="bx bx-download h3 m-0"></i
                      ></a>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <!-- end col -->
    </div>

    <div class="col-lg-4">
      <div class="card">
        <div class="card-body">
          <h4 class="card-title mb-4">
            Promotions
            <div class="float-right">
              <a
                (click)="openPromotion(promotionModal)"
                title="add Promotions"
                class="btn btn-success mr-1"
                style="color: white"
                ><i class="bx bxs-message-square-add"></i
              ></a>

              <a
                (click)="exportPromotions()"
                title="Export Promotions"
                class="btn btn-success"
                style="color: white"
                ><i class="bx bxs-message-square-minus"></i
              ></a>
            </div>
          </h4>
          <div class="table-responsive">
            <table class="table table-nowrap table-hover mb-0">
              <thead>
                <tr>
                  <th scope="col">Name</th>
                  <th scope="col">Quantity</th>
                  <th scope="col">Rm</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let promotion of teamDetail?.promotions">
                  <td>{{ promotion.name }}</td>
                  <td>{{ promotion.quantity }}</td>
                  <td>
                    <a (click)="deletePromotion(promotion.id)">
                      <i class="bx bxs-trash"></i
                    ></a>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    <!-- end col -->
  </div>
  <!-- end row -->

  <div class="row"  *ngIf="teamDetail">
    <div class="col-lg-6 col-md-12 col-sm-12" [ngClass]="{'disabled-member-list': !env.openFormsAndTeamFiles && !checkIfCaptain()}">
      <div class="card">
        <div class="card-body">
          <h4 class="card-title mb-4">
            {{ 'team._forms_team_files' |translate}}
            <div class="float-right">
              <a
                (click)="openFormsAttachments(formsAttachmentModal)"
                title="add Attachments"
                class="btn btn-success"
                style="color: white"
                ><i class="bx bxs-message-square-add"></i
              ></a>
            </div>
          </h4>
          <h2 class="enable-member-list-header" *ngIf="!env.openFormsAndTeamFiles && !checkIfCaptain()"> {{ 'team._pre_registration_period_expired'| translate}} </h2>
          <div class="table-responsive">
            <table class="table table-nowrap table-centered table-hover mb-0">
              <tbody>
                <tr *ngFor="let item of teamDetail?.forms">
                  <td
                    (click)="openFormsAttachments(formsAttachmentModal, item)"
                  >
                    <h5 class="font-size-14 mb-1">
                      {{ item.name }}-{{ item.surname }}
                    </h5>
                    <br />
                    <p style="white-space: pre-wrap">{{ item?.description }}</p>
                    <small>{{ item.created_at | date: "medium" }}</small>
                  </td>
                  <td>
                    <tr *ngFor="let attac of item.attachments">
                      <td style="width: 45px">
                        <div class="avatar-sm">
                          <span
                            class="avatar-title rounded-circle bg-soft-primary text-primary font-size-24"
                          >
                            <i class="bx bxs-file-doc"></i>
                          </span>
                        </div>
                      </td>
                      <td>
                        <h5 class="font-size-14 mb-1">
                          <a href="javascript: void(0);" class="text-dark">{{
                            attac?.type
                          }}</a>
                        </h5>
                        <small>{{ attac?.created_at | date: "medium" }}</small>
                      </td>
                      <td>
                        <div class="text-center">
                          <a
                            [href]="getPath(attac?.asset?.path)"
                            target="_blank"
                            class="text-dark"
                            ><i class="bx bx-download h3 m-0"></i
                          ></a>
                        </div>
                      </td>
                    </tr>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<ng-template #promotionModal let-modal>
  <div class="modal-header">
    <h5 class="modal-title mt-0">Add Promotion</h5>
    <button
      type="button"
      class="close"
      (click)="modal.dismiss('Cross click')"
      aria-hidden="true"
    >
      ×
    </button>
  </div>
  <div class="modal-body">
    <form [formGroup]="promotionForm">
      <div class="form-group row">
        <div class="col-md-12">
          <table style="width: 100%;">
            <tr>
              <td>
                <label style="width: 100%;"
                ><input
                  type="checkbox"
                  (change)="checkValue($event)"
                  formControlName="name"
                  name="name"
                  value="bileklik"
                />
                  Bileklik</label>
              </td>
              <td>
                <label style="width: 100%;"
                ><input
                  type="number" name="bileklik-number" id="bileklik-number" value="1" (change)="checkNumValue($event)"/>
                  Adeti</label>
              </td>
            </tr>
            <tr>
              <td>
                <label style="width: 100%"
                ><input
                  type="checkbox"
                  (change)="checkValue($event)"
                  formControlName="name"
                  name="name"
                  value="organizasyonklavuzu"
                />
                  Organizasyon Klavuzu
                </label>
              </td>
              <td>
                <label style="width: 100%;"
                ><input
                  type="number" name="organizasyonklavuzu-number" id="organizasyonklavuzu-number" value="1" (change)="checkNumValue($event)"/>
                  Adeti</label>
              </td>
            </tr>
            <tr>
              <td>
                <label style="width: 100%"
                ><input
                  type="checkbox"
                  (change)="checkValue($event)"
                  formControlName="name"
                  name="name"
                  value="sertifika"
                />
                  Sertifika</label
                >
              </td>
              <td>
                <label style="width: 100%;"
                ><input
                  type="number" name="sertifika-number" id="sertifika-number" value="1" (change)="checkNumValue($event)"/>
                  Adeti</label>
              </td>
            </tr>
            <tr>
              <td>
                <label style="width: 100%"
                ><input
                  type="checkbox"
                  (change)="checkValue($event)"
                  formControlName="name"
                  name="name"
                  value="tshirt"
                />
                  T-shirt</label
                >
              </td>
              <td>
                <label style="width: 100%;"
                ><input
                  type="number" name="tshirt-number" id="tshirt-number" value="1" (change)="checkNumValue($event)"/>
                  Adeti</label>
              </td>
            </tr>
            <tr>
              <td>
                <label style="width: 100%"
                ><input
                  type="checkbox"
                  (change)="checkValue($event)"
                  formControlName="name"
                  name="name"
                  value="canta"
                />
                  Çanta
                </label>
              </td>
              <td>
                <label style="width: 100%;"
                ><input
                  type="number" name="canta-number" id="canta-number" value="1" (change)="checkNumValue($event)"/>
                  Adeti</label>
              </td>
            </tr>
            <tr>
              <td>
                <label style="width: 100%"
                ><input
                  type="checkbox"
                  (change)="checkValue($event)"
                  formControlName="name"
                  name="name"
                  value="etiketler"
                />
                  Etiketler
                </label>
              </td>
              <td>
                <label style="width: 100%;"
                ><input
                  type="number" name="etiketler-number" id="etiketler-number" value="1" (change)="checkNumValue($event)"/>
                  Adeti</label>
              </td>
            </tr>
          </table>

        </div>
      </div>
      <div class="text-center mt-4">
        <button type="button" (click)="addPromotion()" class="btn btn-primary">
          Add Promotion
        </button>
      </div>
    </form>
  </div>
</ng-template>

<ng-template #attachmentModal let-modal>
  <div class="modal-header">
    <h5 class="modal-title mt-0">Attachment</h5>
    <button
      type="button"
      class="close"
      (click)="modal.dismiss('Cross click')"
      aria-hidden="true"
    >
      ×
    </button>
  </div>
  <div class="modal-body">
    <div class="custom-file">
      <input type="file" #story class="custom-file-input" id="customFile" />
      <label class="custom-file-label" for="customFile">Select file</label>
    </div>
    <div class="text-center mt-4">
      <select
        class="form-control"
        [(ngModel)]="fileType"
        (change)="onChange($event)"
      >
        <option *ngFor="let item of attachmentOption" [value]="item?.id">{{item?.name}}</option>

      </select>
    </div>
    <div class="text-center mt-4">
      <button
        type="button"
        (click)="upload(story.files, $event)"
        class="btn btn-primary"
      >
        Send File
      </button>
    </div>
  </div>
</ng-template>

<ng-template #formsAttachmentModal let-modal>
  <div class="modal-header">
    <h5 class="modal-title mt-0">Forms</h5>
    <button
      type="button"
      class="close"
      (click)="modal.dismiss('Cross click')"
      aria-hidden="true"
    >
      ×
    </button>
  </div>
  <div class="modal-body">
    <form [formGroup]="formsAttachmentForm">
      <!-- Adi-->
      <div class="form-group row">
        <label class="col-md-3 col-form-label">Adı/Name</label>
        <div class="col-md-9">
          <input
            class="form-control"
            placeholder="Adi/Name"
            type="text"
            formControlName="name"
          />
        </div>
      </div>

      <!-- Soyadi-->
      <div class="form-group row">
        <label class="col-md-3 col-form-label">Soyadı/Surname</label>
        <div class="col-md-9">
          <input
            class="form-control"
            placeholder="Soyadı/Surname"
            type="text"
            formControlName="surname"
          />
        </div>
      </div>

      <!--Photo-->
      <div class="form-group row">
        <label class="col-md-3 col-form-label">Photo</label>
        <div class="col-md-9">
          <div class="input-group mb-3">
            <div class="input-group-prepend">
              <button class="btn btn-secondary" (click)="photo.click()">
                <i class="fa fa-paperclip" aria-hidden="true"></i>
              </button>
            </div>
            <span class="p-input-icon-right spinner-input">
              <i class="pi pi-spin pi-spinner" [hidden]="!filesLoading[0]"></i>
              <input
                class="form-control"
                type="text"
                readonly
                placeholder="Choose a photo"
                formControlName="photo"
            /></span>
          </div>
        </div>
      </div>

      <!--Word-->
      <div class="form-group row">
        <label class="col-md-3 col-form-label">Word</label>
        <div class="col-md-9">
          <div class="input-group mb-3">
            <div class="input-group-prepend">
              <button class="btn btn-secondary" (click)="word.click()">
                <i class="fa fa-paperclip" aria-hidden="true"></i>
              </button>
            </div>
            <span class="p-input-icon-right spinner-input">
              <i class="pi pi-spin pi-spinner" [hidden]="!filesLoading[1]"></i>
              <input
                class="form-control"
                type="text"
                readonly
                placeholder="Choose a word"
                formControlName="word"
            /></span>
          </div>
        </div>
      </div>

      <!--Excel-->
      <div class="form-group row">
        <label class="col-md-3 col-form-label">Excel</label>
        <div class="col-md-9">
          <div class="input-group mb-3">
            <div class="input-group-prepend">
              <button class="btn btn-secondary" (click)="excel.click()">
                <i class="fa fa-paperclip" aria-hidden="true"></i>
              </button>
            </div>

            <span class="p-input-icon-right spinner-input">
              <i class="pi pi-spin pi-spinner" [hidden]="!filesLoading[2]"></i>
              <input
                class="form-control"
                type="text"
                readonly
                placeholder="Choose an excel"
                formControlName="excel"
            /></span>
          </div>
        </div>
      </div>

      <!--pdf-->
      <div class="form-group row">
        <label class="col-md-3 col-form-label">PDF</label>
        <div class="col-md-9">
          <div class="input-group mb-3">
            <div class="input-group-prepend">
              <button class="btn btn-secondary" (click)="pdf.click()">
                <i class="fa fa-paperclip" aria-hidden="true"></i>
              </button>
            </div>
            <span class="p-input-icon-right spinner-input">
              <i class="pi pi-spin pi-spinner" [hidden]="!filesLoading[3]"></i>
              <input
                class="form-control"
                type="text"
                readonly
                placeholder="Choose a Pdf"
                formControlName="pdf"
            /></span>
          </div>
        </div>
      </div>

      <!--video-->
      <div class="form-group row">
        <label class="col-md-3 col-form-label">Video</label>
        <div class="col-md-9">
          <div class="input-group mb-3">
            <div class="input-group-prepend">
              <button class="btn btn-secondary" (click)="video.click()">
                <i class="fa fa-paperclip" aria-hidden="true"></i>
              </button>
            </div>

            <span class="p-input-icon-right spinner-input">
              <i class="pi pi-spin pi-spinner" [hidden]="!filesLoading[4]"></i>
              <input
                class="form-control"
                type="text"
                readonly
                pInputtext
                placeholder="Choose a video"
                formControlName="video"
              />
            </span>
          </div>
        </div>
      </div>

      <!--Aciklama-->
      <div class="form-group row mt-3">
        <label class="col-md-3 col-form-label">Metin/Text</label>
        <div class="col-md-9">
          <textarea
            class="form-control"
            placeholder="Metin/Text"
            formControlName="description"
            rows="5"
          ></textarea>
        </div>
      </div>

      <div class="text-center mt-4">
        <button type="button" [disabled]="disableSaveButton" (click)="attachForms()" class="btn btn-primary">
          Send
        </button>
      </div>
    </form>
  </div>
</ng-template>

<ng-template #penaltyModal let-modal>
  <div class="modal-header">
    <h5 class="modal-title mt-0">Penalty</h5>
    <button
      type="button"
      class="close"
      (click)="modal.dismiss('Cross click')"
      aria-hidden="true"
    >
      ×
    </button>
  </div>
  <div class="modal-body">
    <form [formGroup]="penaltyForm">
      <div class="form-group row">
        <label class="col-md-2 col-form-label">Type</label>
        <div class="col-md-10">
          <select
            id="type"
            name="type"
            formControlName="type"
            class="form-control"
          >
            <option value="before_race">before_race</option>
            <option value="after_race">after_race</option>
          </select>
        </div>
      </div>
      <div class="form-group row">
        <label class="col-md-2 col-form-label">Subject</label>
        <div class="col-md-10">
          <input
            class="form-control"
            name="subject"
            type="text"
            formControlName="subject"
            placeholder="subject"
          />
        </div>
      </div>
      <div class="form-group row">
        <label class="col-md-2 col-form-label">Violation</label>
        <div class="col-md-10">
          <input
            class="form-control"
            name="violation"
            type="text"
            formControlName="violation"
            placeholder="violation"
          />
        </div>
      </div>
      <div class="form-group row">
        <label class="col-md-2 col-form-label">Penalty</label>
        <div class="col-md-10">
          <input
            class="form-control"
            name="penalty"
            type="number"
            formControlName="penalty"
            placeholder="penalty"
          />
        </div>
      </div>
      <div class="form-group row"
        *ngIf="penaltyForm.get('type').value == 'after_race'">
        <label for="session" class="col-md-2 col-form-label">Session</label>
        <div class="col-md-10">
          <select
            name="session"
            id="session"
            class="form-control"
            formControlName="session_id"
            >
            <option
              [value]="race.id"
              *ngFor="let race of (allRace$ | async )"
            >
              {{ race.name }}
            </option>
          </select>
        </div>
      </div>
      <div class="form-group row">
        <label class="col-md-2 col-form-label">Conclusion</label>
        <div class="col-md-10">
          <input
            class="form-control"
            name="conclusion"
            type="text"
            formControlName="conclusion"
            placeholder="conclusion"
          />
        </div>
      </div>
      <div class="text-center mt-4">
        <button type="button" (click)="addPenalty()" class="btn btn-primary">
          Add Penalty
        </button>
      </div>
    </form>
  </div>
</ng-template>

<ng-template #memberModal let-modal>
  <div class="modal-header">
    <h5 class="modal-title mt-0">
      {{
        (updateType ? "member._update_member" : "member._add_member")
          | translate
      }}
    </h5>
    <button
      type="button"
      class="close"
      (click)="modal.dismiss('Cross click')"
      aria-hidden="true"
    >
      ×
    </button>
  </div>
  <div class="modal-body modal-body-team-member">
    <form [formGroup]="memberForm">
      <div class="form-group row">
        <label class="col-md-3 col-form-label">{{
          "member.form.first_name" | translate
        }}</label>
        <div class="col-md-9">
          <input
            class="form-control"
            name="first_name"
            type="text"
            formControlName="first_name"
            [placeholder]="'member.form.first_name' | translate"
          />
        </div>
      </div>

      <div class="form-group row">
        <label class="col-md-3 col-form-label">{{
          "member.form.last_name" | translate
        }}</label>
        <div class="col-md-9">
          <input
            class="form-control"
            name="last_name"
            type="text"
            formControlName="last_name"
            [placeholder]="'member.form.last_name' | translate"
          />
        </div>
      </div>

      <div class="form-group row">
        <label class="col-md-3 col-form-label">{{
          "member.form.email" | translate
        }}</label>
        <div class="col-md-9">
          <input
            class="form-control"
            name="email"
            type="text"
            formControlName="email"
            [placeholder]="'member.form.email' | translate"
          />
        </div>
      </div>

      <div class="form-group row">
        <label class="col-md-3 col-form-label">{{
          "member.form.role_in_team" | translate
        }}</label>
        <div class="col-md-9">
          <select
            id="role_in_team"
            name="role_in_team"
            formControlName="role_in_team"
            class="form-control"
          >
            <option *ngFor="let role of teamRoles" [value]="role">
              {{
                (role == "academicAdvisor" && isLise()
                  ? "member.team_roles.lise_" + role
                  : "member.team_roles." + role
                ) | translate
              }}
            </option>
          </select>
        </div>
      </div>

      <div class="form-group row">
        <label class="col-md-3 col-form-label">
          {{
            (memberForm.value.not_tc_citizen
              ? "member.form.passport_number"
              : "member.form.identity_number"
            ) | translate
          }}</label
        >
        <div class="col-md-9">
          <input
            class="form-control"
            name="identity_number"
            type="text"
            formControlName="identity_number"
            [placeholder]="
              (memberForm.value.not_tc_citizen
                ? 'member.form.passport_number'
                : 'member.form.identity_number'
              ) | translate
            "
            [mask]="'00000000000'"
          />
          <input
            class="form-check-input ml-1"
            name="not_tc_citizen"
            type="checkbox"
            formControlName="not_tc_citizen"
          />
          <label class="ml-4 text-secondary">
            {{ "member._not_tc_citizen" | translate }}</label
          >
        </div>
      </div>

      <div class="form-group row">
        <label class="col-md-3 col-form-label">{{
          "member.form.phone_number" | translate
        }}</label>
        <div class="col-md-9">
          <input
            class="form-control"
            name="phone_number"
            type="text"
            formControlName="phone_number"
            [placeholder]="'member.form.phone_number' | translate"
            [mask]="'(000) 000 00 00'"
          />
        </div>
      </div>
      <div class="form-group row">
        <label class="col-md-3 col-form-label">{{
          "member.form.gender" | translate
        }}</label>
        <div class="col-md-9">
          <select
            id="gender"
            name="gender"
            formControlName="gender"
            class="form-control"
            (change)="uniformSizeChange()"
          >
            <option value="male">{{ "member.form.male" | translate }}</option>
            <option value="female">
              {{ "member.form.female" | translate }}
            </option>
          </select>
        </div>
      </div>

      <div class="form-group row">
        <label class="col-md-3 col-form-label">{{
          "member.form.birthday" | translate
        }}</label>
        <div class="col-md-9">
          <input
            class="form-control"
            name="birthday"
            type="date"
            formControlName="birthday"
            [placeholder]="'member.form.birthday' | translate"
            (change)="birtDayChange()"
          />
        </div>
      </div>

      <div class="form-group row" *ngIf="showParent">
        <label class="col-md-3 col-form-label">{{
          "member.form.parent_name" | translate
        }}</label>
        <div class="col-md-9">
          <input
            class="form-control"
            name="parent_name"
            type="text"
            formControlName="parent_name"
            [placeholder]="'member.form.parent_name' | translate"
          />
          <small class="form-text text-muted">
            {{ "member.form.required_adult" | translate }}</small
          >
        </div>
      </div>

      <div class="form-group row" *ngIf="showParent">
        <label class="col-md-3 col-form-label">{{
          "member.form.parent_phone" | translate
        }}</label>
        <div class="col-md-9">
          <input
            class="form-control"
            name="parent_phone"
            type="text"
            formControlName="parent_phone"
            [placeholder]="'member.form.parent_phone' | translate"
          />
          <small class="form-text text-muted">{{
            "member.form.required_adult" | translate
          }}</small>
        </div>
      </div>

      <div class="form-group row">
        <label class="col-md-3 col-form-label">{{
          "member.form.uniform_size" | translate
        }}</label>
        <div class="col-md-9">
          <select
            id="uniform_size"
            name="uniform_size"
            formControlName="uniform_size"
            class="form-control"
          >
            <option *ngFor="let role of uniformSizes" [value]="role">
              {{ role }}
            </option>
          </select>
          <a (click)="showUniformSizes()">{{
            (this.sizeImage
              ? "member._hide_uniform_table"
              : "member._show_uniform_table"
            ) | translate
          }}</a>
          <img *ngIf="sizeImage" style="width: 100%" [src]="sizeImage" />
        </div>
      </div>
      <div class="form-group row">
        <label class="col-md-3 col-form-label">{{
          "member.form.in_area" | translate
        }}</label>
        <div class="col-md-9">
          <input
            class="form-check-input ml-1"
            name="in_area"
            type="checkbox"
            formControlName="in_area"
          />
        </div>
      </div>

      <div class="form-group row">
        <label class="col-md-3 col-form-label">{{
          "member.form.profile_picture" | translate
        }}</label>
        <div class="col-md-9">
          <div class="">
            <!--            <input #memberPicture (change)="uploadMemberPicture(memberPicture.files)" type="file" name="picture_id"-->
            <!--              class="custom-file-input" id="memberPicture"-->
            <!--            accept="image/*"-->
            <!--            >-->
            <!--            <label class="custom-file-label" for="customFile">{{ | 'Select file'}}</label>-->
            <div class="form-group">
              <div class="mb-2" *ngIf="memberForm.value.picture_url">
                <img
                  [src]="apiUrl + 'storage' + memberForm.value.picture_url"
                  class="rounded-circle avatar-md"
                  alt=""
                />
                <!--                <button class="btn btn-sm btn-secondary ml-3 ">{{'_change'| translate}}</button>-->
              </div>

              <input
                #memberPicture
                (change)="uploadMemberPicture(memberPicture.files, $event)"
                type="file"
                class="form-control-file"
                id="exampleFormControlFile1"
                accept="image/png,image/jpg"
              />
            </div>
          </div>
        </div>
      </div>

      <div class="text-center mt-4">
        <button type="button" (click)="addMember()" class="btn btn-primary">
          {{
            (updateType ? "member._update_member" : "member._add_member")
              | translate
          }}
        </button>
      </div>
    </form>
  </div>
</ng-template>

<ng-template #changeTeamName let-modal>
  <div class="modal-header">
    <h5 class="modal-title mt-0">
      {{ "member.form.change_team" | translate }}
    </h5>
    <button
      type="button"
      class="close"
      (click)="modal.dismiss('Cross click')"
      aria-hidden="true"
    >
      ×
    </button>
  </div>
  <div class="modal-body">
    <form [formGroup]="teamNameForm">
      <div class="form-group row">
        <label class="col-md-3 col-form-label">{{
          "member.form.team_name" | translate
        }}</label>
        <div class="col-md-9">
          <input
            #teamname
            (input)="
              teamNameForm.patchValue({
                team_name: $event.target.value.toUpperCase()
              })
            "
            class="form-control"
            name="team_name"
            type="text"
            formControlName="team_name"
            [placeholder]="'member.form.team_name' | translate"
          />
        </div>
      </div>

      <div class="text-center mt-4">
        <button
          type="button"
          (click)="updateTeamName()"
          class="btn btn-primary"
        >
          {{ "member.form.change_team" | translate }}
        </button>
      </div>
    </form>
  </div>
</ng-template>

<ngx-spinner type="ball-clip-rotate-multiple" size="medium"> </ngx-spinner>

<!--Pdf File Input-->
<input
  type="file"
  hidden
  #pdf
  accept=".pdf"
  class="custom-file-input hidden"
  id="pdfFile"
  (change)="saveFormsAttachmentInfo(pdf.files, 'Pdf', 3)"
/>

<!--Video File Input-->
<input
  type="file"
  #video
  accept="video/*"
  class="custom-file-input hidden"
  id="videoFile"
  (change)="saveFormsAttachmentInfo(video.files, 'Video', 4)"
/>

<!--Ecxel File Input-->
<input
  type="file"
  #excel
  accept=".xls,.xlsx"
  class="custom-file-input hidden"
  id="excelFile"
  (change)="saveFormsAttachmentInfo(excel.files, 'Excel', 2)"
/>

<!--Word File Input-->
<input
  type="file"
  #word
  accept=".doc,.docx"
  class="custom-file-input hidden"
  id="wordFile"
  (change)="saveFormsAttachmentInfo(word.files, 'Word', 1)"
/>

<!--Photo File Input-->
<input
  type="file"
  #photo
  accept="image/*"
  class="custom-file-input hidden"
  id="photoFile"
  (change)="saveFormsAttachmentInfo(photo.files, 'Photo', 0)"
/>

