import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { RaceService } from 'src/app/core/services/race.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { FormBuilder, FormGroup } from '@angular/forms';
import { TeamsService } from 'src/app/core/services/teams.service';
import Swal from 'sweetalert2';
import { NgxSpinnerService } from 'ngx-spinner';

@Component({
  selector: 'app-race-detail',
  templateUrl: './race-detail.component.html',
  styleUrls: ['./race-detail.component.scss'],
})
export class RaceDetailComponent implements OnInit {

  raceId;
  raceDetail;
  // bread crumb items
  breadCrumbItems: Array<{}>;
  scores;
  sessionForm: FormGroup;


  sessions = [{
    'name': 'first session',
    'race_id': 1,
    'type': 1,
    'description': 'first race',
    'start_time': '2020-07-23 12:00',
    'status': 'In Progress',
  }];

  statData = [
    {
      icon: 'bx bx-package',
      title: 'Total Team',
      value: '55',
    },
  ];

  constructor(private teamsService: TeamsService, private fb: FormBuilder, private raceService: RaceService,
    private route: ActivatedRoute, private modalService: NgbModal, private spinner: NgxSpinnerService) { }

  ngOnInit(): void {
    this.breadCrumbItems = [{ label: 'Race Detail' }, { label: 'Race Overview', active: true }];
    this.route.params.subscribe(params => {
      this.raceId = params['id'];
      this.getRaceDetail();
      //this.getTeams();
      this.sessionForm = this.fb.group({
        name: [],
        type: [],
        race_id: [this.raceId],
        description: [],
        start_time: [],
      });
    });
  }

  getRaceDetail() {
    this.raceService.getRace(this.raceId).subscribe(data => {
      this.raceDetail = data['data'];
      this.sessions = data['data']['sessions'];
      this.scores = data['data']['scores'];
      this.modalService.dismissAll();
    });
  }

  teamsObj = { 'teams': [] };

  getTeams() {
    this.teamsService.getTeams({ limit: 100 }).subscribe(data => {
      this.statData[0].value = data['meta']['pagination']['total'];
      data['data'].forEach(item => {
        this.teamsObj['teams'].push(item['id']);
      });
    });

  }

  openCreateSession(sessionModal: any) {
    this.modalService.open(sessionModal, { centered: true, windowClass: 'modal-holder' });
  }

  onChange(event) {
    console.log(this.teamsObj);
    if (event.target.value === 'hydromobile') {

    } else if (event.target.value === 'electromobile') {

    }
  }

  createSession() {
    this.spinner.show();
    this.raceService.createSession(this.sessionForm.value).subscribe(data => {
      //alert('Session created successfully');
      this.spinner.hide();
      this.position('Session created successfully', 'success');
      this.sessionForm = this.fb.group({
        name: [],
        type: [],
        race_id: [this.raceId],
        description: [],
        start_time: [],
      });
      this.getRaceDetail();
    }, error => {
      this.spinner.hide();
      this.position(error['error']['message'], 'warning');
    });
  }

  addTeamsToSession() {
    console.log(this.sessionForm.get('type').value);
    this.raceService.addTeams(this.teamsObj, this.raceId).subscribe(data => {
      console.log('All teams successfully added');
    });
  }

  endRace(raceId) {
    this.spinner.show();
    this.raceService.endRace(raceId).subscribe(data => {
      this.spinner.hide();
      this.position('Successfully ended', 'success');
      this.getRaceDetail();
    }, error => {
      this.spinner.hide();
      this.position('Error', 'warning');
    });
  }

  calculateBestScores(raceId) {
    this.spinner.show();
    this.raceService.calculateBestScore(raceId)
      .subscribe(data => {
        this.spinner.hide();
        this.position('Successfully Calculated', 'success');
        this.getRaceDetail();
      }, error => {
        this.spinner.hide();
        this.position('Error', 'warning');
      });
  }

  position(title, icon) {
    Swal.fire({
      position: 'center',
      icon: icon,
      title: title,
      showConfirmButton: false,
      timer: 1500,
    });
  }


  exportScores() {
    this.spinner.show();
    this.raceService.exportBestScores(this.raceId)
      .subscribe(data => {
        // alert('Exported');
        this.spinner.hide();
        this.downLoadFile(data, 'application/ms-excel', 'EC_race_scores.xlsx');
      }, error => {
        this.spinner.hide();
        this.position('Error', 'warning');
      });
  }

  exportAllScores() {
    this.spinner.show();
    this.raceService.exportAllScores(this.raceId)
      .subscribe(data => {
        // alert('Exported');
        this.spinner.hide();
        this.downLoadFile(data, 'application/ms-excel', 'EC_race_scores_all.xlsx');
      }, error => {
        this.spinner.hide();
        this.position('Error', 'warning');
      });
  }

  downLoadFile(data: any, type: string, filename) {
    let blob = new Blob([data], { type: type });

    let url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.click();

    // let pwa = window.open(url);
    // if (!pwa || pwa.closed || typeof pwa.closed == 'undefined') {
    //   alert('Please disable your Pop-up blocker and try again.');
    // }
  }
}
