<div class="container-fluid" *ngIf="raceDetail">
  <app-page-title [title]="raceDetail.name" [breadCrumbItems]="breadCrumbItems"></app-page-title>
  <div class="row">
    <div class="col-xl-4">
      <div class="card">
        <div class="card-body">
          <p class="text-muted mb-4">{{ raceDetail.description }}</p>
          <div class="table-responsive">
            <table class="table table-nowrap mb-0">
              <tbody>
              <tr>
                <th scope="row">Start Date:</th>
                <td>{{ raceDetail.start_date }}</td>
              </tr>
              <tr>
                <th scope="row">Type :</th>
                <td>{{ raceDetail.type }}</td>
              </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div class="col-xl-8">
      <div class="row">
        <a (click)="endRace(raceId)" [hidden]="raceDetail.ended ===1" class="btn btn-success inner mr-3">
          End Race
        </a>
        <a (click)="calculateBestScores(raceId)" class="btn btn-success inner mr-3">
          Calculate Best Scores
        </a>
        <a [routerLink]="['/race-leaderboard/', raceId]" *ngIf="scores" class="btn btn-success inner mr-3">
          Leaderboard
        </a>
      </div>
    </div>

  </div>

  <div class="row">
    <div class="col-xl-12">


      <div class="card">
        <div class="card-body">
          <h4 class="card-title mb-4">Sessions
            <div class="float-right">
              <a (click)="openCreateSession(sessionModal)" title="add Session" class="btn btn-success"
                 style="color: white;"><i class="bx bxs-message-square-add"></i></a>

            </div>
          </h4>
          <div class="table-responsive">
            <table class="table table-nowrap table-hover mb-0">
              <thead>
              <tr>
                <th scope="col">#id</th>
                <th scope="col">Description</th>
                <th scope="col">Type</th>
                <th scope="col">Start Time</th>
                <th scope="col">Status</th>
              </tr>
              </thead>
              <tbody>
              <tr *ngFor="let session of sessions">
                <th scope="row">{{ session.id }}</th>
                <td><a [routerLink]="['/race/session/', session.id]">{{ session.name }}</a>
                </td>
                <td>{{ session.type || 'both' }}</td>
                <td>{{ session.start_time }}</td>
                <td>{{ session.status }}</td>
              </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row" *ngIf="scores">
    <div class="col-xl-12">
      <div class="card">
        <div class="card-body">
          <h4 class="card-title mb-4">Scores

            <div class="float-right">
              <a
                (click)="exportScores()"
                title="Export Scores"
                class="btn btn-success ml-2"
                style="color: white"
              ><i class="bx bx-table"></i>
                Export Best
              </a>
            </div>

            <div class="float-right">
              <a
                (click)="exportAllScores()"
                title="Export Scores"
                class="btn btn-success ml-2"
                style="color: white"
              ><i class="bx bx-table"> </i>
                Export All Session Scores</a>
            </div>


          </h4>
          <div class="table-responsive">
            <table class="table table-nowrap table-hover mb-0">
              <thead>
              <tr>
                <th scope="col">#Vehicle No</th>
                <th scope="col">Vehicle Name</th>
                <th scope="col">Team Name</th>
                <th scope="col">Last Energy Const</th>
                <th scope="col">Last Hydrogen Cons</th>
                <th scope="col">Penalty</th>
                <th scope="col">Score</th>
              </tr>
              </thead>
              <tbody>
              <tr *ngFor="let score of scores">
                <th scope="row">{{ score.team?.vehicle_number }}</th>
                <td>{{ score.team?.vehicle_name }}</td>
                <td>{{ score.team?.team_name }}</td>
                <td>{{ score.last_energy_cons }}</td>
                <td>{{ score.last_hydrogen_cons }}</td>
                <td>{{ score.penalty }}</td>
                <td>{{ score.score }}</td>
              </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<ngx-spinner type="ball-clip-rotate-multiple" size="medium"></ngx-spinner>

<ng-template #sessionModal let-modal>
  <div class="modal-header">
    <h5 class="modal-title mt-0">Add Session</h5>
    <button type="button" class="close" (click)="modal.dismiss('Cross click')" aria-hidden="true">×</button>
  </div>
  <div class="modal-body">
    <form [formGroup]="sessionForm">

      <div class="form-group row">
        <label for="example-search-input" class="col-md-2 col-form-label">Name</label>
        <div class="col-md-10">
          <input class="form-control" type="search" value="" id="example-search-input" formControlName="name">
        </div>
      </div>
      <div class="form-group row">
        <label for="example-search-input" class="col-md-2 col-form-label">Description</label>
        <div class="col-md-10">
          <input class="form-control" type="search" value="" id="example-search-input"
                 formControlName="description">
        </div>
      </div>
      <div class="form-group row">
        <label class="col-md-2 col-form-label">Type</label>
        <div class="col-md-10">
          <select class="form-control" formControlName="type" (change)="onChange($event)">
            <option value="notSelected"> Not Selected</option>
            <option value="electromobile">Electromobile</option>
            <option value="hydromobile">Hydromobile</option>
          </select>
        </div>
      </div>
      <div class="form-group row">
        <label for="example-datetime-local-input" class="col-md-2 col-form-label">Start Time</label>
        <div class="col-md-10">
          <input class="form-control" type="datetime-local" formControlName="start_time"
                 id="example-datetime-local-input">
        </div>
      </div>
      <div class="row">
        <div class="col-12">
          <div class="text-center my-3">

            <a (click)="createSession()" class="btn btn-success inner">
              Create Session </a>
          </div>
        </div> <!-- end col-->
      </div>
    </form>
  </div>
</ng-template>
