import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { RaceService } from '../../../core/services/race.service';
import Swal from 'sweetalert2';
import { Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
@Component({
  selector: 'app-create-race',
  templateUrl: './create-race.component.html',
  styleUrls: ['./create-race.component.scss']
})
export class CreateRaceComponent implements OnInit {

  breadCrumbItems: Array<{}>;
  raceForm: FormGroup;

  constructor(private fb: FormBuilder, private raceService: RaceService, private router: Router, private spinner: NgxSpinnerService) { }

  ngOnInit(): void {
    this.raceForm = this.fb.group({
      name: [],
      description: [],
      start_date: [],
    });
    this.breadCrumbItems = [{ label: 'Create Team' }, { label: 'Team Wizard', active: true }];
  }

  createRace() {
    this.spinner.show();
    this.raceService.createRace(this.raceForm.value).subscribe(
      data => {
        this.spinner.hide();
        console.log(data);
        this.position("Added Successfully");
      }
    )
  }

  position(title, icon=null) {
    Swal.fire({
      position: 'center',
      icon: icon? icon: 'success',
      title: title,
      timer: 1500,
      allowOutsideClick: false,
      showConfirmButton: true,
      showCancelButton: false
    })
      .then((willDelete) => {

        this.redirectToList();
      });
  }


  redirectToList() {
    this.router.navigateByUrl(`/race`);
  }

}
