import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { RaceComponent } from './race/race.component';
import { CreateRaceComponent } from './create-race/create-race.component';
import { RaceDetailComponent } from './race-detail/race-detail.component';
import { RaceSessionComponent } from './race-session/race-session.component';

const routes: Routes = [
  {
    path: 'race',
    component: RaceComponent
  },
  {
    path: 'race/create',
    component: CreateRaceComponent
  },
  {
    path: 'race/detail/:id',
    component: RaceDetailComponent
  },
  {
    path: 'race/session/:id',
    component: RaceSessionComponent
  }
];
@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class RaceRoutingModule { }
