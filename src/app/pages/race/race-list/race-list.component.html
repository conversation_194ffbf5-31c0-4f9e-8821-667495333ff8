<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="text-center my-3">
        <a [routerLink]="['/race/create']" class="btn btn-success inner mr-3">
          Add Race </a>
      </div>
    </div>
  </div>
  <app-page-title title="Race" [breadCrumbItems]="breadCrumbItems"></app-page-title>
  <div class="row">
    <div class="col-12">
      <ngx-datatable [rows]="rows" class="bootstrap" [loadingIndicator]="loadingIndicator" [columnMode]="'force'"
                     [headerHeight]="50" [footerHeight]="50" [rowHeight]="'auto'" limit="10" [columns]="columns"
                     [reorderable]="reorderable">
        <ngx-datatable-column *ngFor="let column of columns; let i = index;" [name]="column.name" [prop]="column.prop">

          <ng-template let-value="value" let-row="row" ngx-datatable-cell-template>

            <a *ngIf="column.prop === 'id'" [routerLink]="['/race/detail/', row.id]"> {{value}} </a>
            <a *ngIf="column.prop === 'name'" href="javascript: void(0);"
               [routerLink]="['/race/detail/', row.id]">{{value}}</a>

            <span *ngIf="column.prop === 'description'">{{value}}</span>

            <span *ngIf="column.prop === 'start_date'">{{value | date :'yyyy-MM-dd HH:mm'}}</span>
            <span *ngIf="column.prop === 'ended'">{{value == 1 ? 'ended' : 'in progress'}}</span>
            <!--            <span *ngIf="column.prop === 'updated_at'">{{value | date :'yyyy-MM-dd HH:mm'}}</span>-->
            <span *ngIf="column.prop === 'created_at'">{{value | date :'yyyy-MM-dd HH:mm'}}</span>

            <a *ngIf="column.prop === 'delete'" href="javascript: void(0);" (click)="removeRace(row.id)">
              <i class="bx bxs-trash"></i> delete </a>

          </ng-template>
        </ngx-datatable-column>
      </ngx-datatable>
    </div>
  </div>
</div>

<ngx-spinner type="ball-clip-rotate-multiple" size="medium"> </ngx-spinner>
