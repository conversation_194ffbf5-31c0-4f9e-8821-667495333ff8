import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder } from '@angular/forms';
import { NgxSpinnerService } from 'ngx-spinner';
import { RaceService } from 'src/app/core/services/race.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-race-list',
  templateUrl: './race-list.component.html',
  styleUrls: ['./race-list.component.scss']
})
export class RaceListComponent implements OnInit {

  columns = [];

  breadCrumbItems: Array<{}>;
  rows = [];
  loadingIndicator: boolean = true;
  reorderable: boolean = true;
  filterForm: FormGroup;

  constructor(private fb: FormBuilder, private raceService: RaceService, private spinner: NgxSpinnerService) {
    this.filterForm = this.fb.group({
      limit: [100]
    });

    setTimeout(() => { this.loadingIndicator = false; }, 5000);
  }

  ngOnInit(): void {
    this.breadCrumbItems = [{ label: 'Race' }, { label: 'Race List', active: true }];

    this.columns = [
      { prop: 'id' },
      { prop: 'name' },
      { prop: 'description' },
      // { prop: 'type' },
      { prop: 'start_date', name: "Start Date" },
      { prop: 'ended', name: "Status" },
      // { prop: 'updated_at', name: "Last Update" },
      { prop: 'created_at', name: "Created" },
      { prop: 'delete', name: "Delete" },
    ];
    this.getRaces();
  }


  getRaces() {
    this.raceService.getRaces(this.filterForm.value).subscribe(races => {
      this.rows = races["data"];
    })
  }

  removeRace(raceId) {
    Swal.fire({
      title: 'Are you sure to delete this Race?',
      text: 'It will delete this race.',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#34c38f',
      cancelButtonColor: '#f46a6a',
      confirmButtonText: 'Yes, delete.',
    }).then((result) => {
      if (result.value) {
        this.deleteRace(raceId);
      }
    });
  }

  deleteRace(raceId) {
    this.spinner.show();
    this.raceService.deleteRace(raceId).subscribe(data => {
      //alert("Removed Successfully");
      this.spinner.hide();
      this.getRaces();
    }, error => {
      this.spinner.hide();
      this.position(error["error"]["message"], "error");
    })
  }

  position(title, icon=null) {
    Swal.fire({
      position: 'center',
      icon: icon? icon: 'success',
      title: title,
      timer: 1500,
      allowOutsideClick: false,
      showConfirmButton: true,
      showCancelButton: false
    })
  }

}
