::ng-deep datatable-body-cell input[type='text']{
    width:50px;
}
::ng-deep .datatable-body-cell-label{
    cursor:pointer;    font-size: 18px;
}
::ng-deep .datatable-body-cell-label{
  cursor:pointer;    font-size: 18px;
}

::ng-deep .ngx-datatable{
  height: calc(100vh - 395px);
}

::ng-deep .scroll {
  width: 100%;
  max-height: 400px;
  overflow-y: auto;
}

::ng-deep .modal-content {
  width: 100% !important;
}

.table-subject-column {
  max-width: 150px;
}

.table-violation-column {
  max-width: 150px;
}

.table-penalty-column {
  max-width: 50px;
}

td, p{
  overflow: hidden;
  white-space: normal;
}
