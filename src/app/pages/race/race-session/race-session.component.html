<div class="container-fluid" *ngIf="sessionDetail">
  <app-page-title [title]="sessionDetail.name" [breadCrumbItems]="breadCrumbItems"></app-page-title>
  <div class="row">
    <div class="col-xl-4">
      <div class="card">
        <div class="card-body">
          <p class="text-muted mb-4">{{sessionDetail.description}}</p>
          <div class="table-responsive">
            <table class="table table-nowrap mb-0">
              <tbody>
                <tr>
                  <th scope="row">Start Date:</th>
                  <td>{{sessionDetail.start_time}}</td>
                </tr>
                <tr>
                  <th scope="row">Type :</th>
                  <td>{{sessionDetail.type}}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div class="col-xl-8">
      <div class="row">
        <div *ngFor="let stat of statData" class="col-md-4">
          <app-stat [title]="stat.title" [value]="stat.value" [icon]="stat.icon"></app-stat>
        </div>
      </div>
      <div class="row ml-2">
        <div class="btn-group btn-group-lg" role="group" aria-label="Basic example">
          <button type="button" (click)="reloadScore()" class="btn btn-success  mr-1">Reload</button>
          <button type="button" [routerLink]="['/reports', sessionId]" class="btn btn-success mr-1">Leaders
          </button>
          <button type="button" (click)="openAddTeamModal(addTeamModal)" class="btn btn-success mr-1">Add Teams</button>
          <button type="button" (click)="exportScores()" class="btn btn-success">Export Scores</button>
        </div>
      </div>
    </div>
  </div>

  <div class="row" *ngIf="rows">
    <div class="col-12">
      <!--
       Embed the ngx-datatable component with following property bindings/values:
     1. sorttype - allow data to be sorted on multiple columns
     2. headerHeight - Set height of table header at 50 pixels
     3. footerHeight - Set height of table footer at 50 pixels
     4. rowHeight - Set height of table rows at 50 pixels (or 'auto')
     5. rows - Derives the data for the table rows from the component class
               property of rows
     6. columns - Derives the names for the table columns from the component
                  class property of columns
     7. columnMode - Use of standard, flex or force - Force value makes columns
                     equidistant and span the width of the parent container
     8. limit - the number of records to display before paginating the results
-->
      <ngx-datatable class="bootstrap "
                  [scrollbarV]="true"
                     [headerHeight]="50"
                     [footerHeight]="50"
                     [rowHeight]="60"
                     [rows]="rows"
                     [scrollbarH]="true"
        [limit]="100" [loadingIndicator]="loadingIndicator" [columnMode]="'force'">


        <ngx-datatable-column prop="vehicle_number" name="V. No">
          <ng-template ngx-datatable-cell-template let-value="value" let-row="row">
            <a target="_blank" href="javascript: void(0);"
            [routerLink]="['/team/detail/', row.team_id]">{{value}}</a>
          </ng-template>
        </ngx-datatable-column>

        <ngx-datatable-column prop="team_name" name="Name">
          <ng-template ngx-datatable-cell-template let-value="value" let-row="row">
            <span style="font-size:16px;" [style.color]="row.vehicle_category === 'hydromobile'? 'green': 'blue'">{{value}}</span>
          </ng-template>
        </ngx-datatable-column>

        <ngx-datatable-column prop="start_point" name="Start Point">
          <ng-template ngx-datatable-cell-template let-rowIndex="rowIndex" let-value="value" let-row="row">
            <div style="min-height: 30px" (click)="setEdit(rowIndex, 'start_point')"
              *ngIf="!editing[rowIndex + '-start_point']">
              <span>
                {{value}}
              </span>
            </div>
            <input
              #editableInput
              [attr.data-key]="rowIndex + '-start_point'"
              (keydown.enter)="updateValue($event, 'start_point', rowIndex)"
              (blur)="updateValue($event, 'start_point', rowIndex, true)"
              (input)="updateValueDebounced($event, 'start_point', rowIndex)"
              *ngIf="editing[rowIndex+ '-start_point']" type="text" [value]="value" />
          </ng-template>
        </ngx-datatable-column>

        <ngx-datatable-column prop="laps" name="Laps">
          <ng-template ngx-datatable-cell-template let-rowIndex="rowIndex" let-value="value" let-row="row">
            <div (click)="setEdit(rowIndex, 'laps')" *ngIf="!editing[rowIndex + '-laps']">
              <span>
                {{value}}
              </span>
            </div>
            <input
              #editableInput
              [attr.data-key]="rowIndex + '-laps'"
              (keydown.enter)="updateValue($event, 'laps', rowIndex)"
              (blur)="updateValue($event, 'laps', rowIndex, true)"
              (input)="updateValueDebounced($event, 'laps', rowIndex)"
              *ngIf="editing[rowIndex+ '-laps']"
              type="text" [value]="value" />
          </ng-template>
        </ngx-datatable-column>

        <ngx-datatable-column prop="valid_laps" name="Valid Laps">
          <ng-template ngx-datatable-cell-template let-rowIndex="rowIndex" let-value="value" let-row="row">
            <div (click)="setEdit(rowIndex, 'valid_laps')" *ngIf="!editing[rowIndex + '-valid_laps']">
              <span>
                {{value}}
              </span>
            </div>
            <input
              #editableInput
              [attr.data-key]="rowIndex + '-valid_laps'"
              (keydown.enter)="updateValue($event, 'valid_laps', rowIndex)"
              (blur)="updateValue($event, 'valid_laps', rowIndex, true)"
              (input)="updateValueDebounced($event, 'valid_laps', rowIndex)"
              *ngIf="editing[rowIndex+ '-valid_laps']" type="text" [value]="value" />
          </ng-template>
        </ngx-datatable-column>


        <ngx-datatable-column prop="initial_energy_cons" name="Initial Energy Cons">
          <ng-template ngx-datatable-cell-template let-rowIndex="rowIndex" let-value="value" let-row="row">
            <span title="Double click to edit" (click)="setEdit(rowIndex, 'initial_energy_cons')"
              *ngIf="!editing[rowIndex + '-initial_energy_cons']">
              {{value}}
            </span>
            <input
              #editableInput
              [attr.data-key]="rowIndex + '-initial_energy_cons'"
              (keydown.enter)="updateValue($event, 'initial_energy_cons', rowIndex)"
              (blur)="updateValue($event, 'initial_energy_cons', rowIndex, true)"
              (input)="updateValueDebounced($event, 'initial_energy_cons', rowIndex)"
              *ngIf="editing[rowIndex+ '-initial_energy_cons']" type="text" [value]="value" />
          </ng-template>
        </ngx-datatable-column>


        <ngx-datatable-column prop="last_energy_cons" name="Last Energy Cons">
          <ng-template ngx-datatable-cell-template let-rowIndex="rowIndex" let-value="value" let-row="row">
            <span title="Double click to edit" (click)="setEdit(rowIndex, 'last_energy_cons')"
              *ngIf="!editing[rowIndex + '-last_energy_cons']">
              {{value}}
            </span>
            <input
              #editableInput
              [attr.data-key]="rowIndex + '-last_energy_cons'"
              (keydown.enter)="updateValue($event, 'last_energy_cons', rowIndex)"
              (blur)="updateValue($event, 'last_energy_cons', rowIndex, true)"
              (input)="updateValueDebounced($event, 'last_energy_cons', rowIndex)"
              *ngIf="editing[rowIndex+ '-last_energy_cons']" type="text" [value]="value" />
          </ng-template>
        </ngx-datatable-column>


        <ngx-datatable-column *ngIf="sessionDetail.type === 'hydromobile' || !sessionDetail.type" prop="initial_hydrogen_cons" name="Initial Hydrogen Cons">
          <ng-template ngx-datatable-cell-template let-rowIndex="rowIndex" let-value="value" let-row="row">
            <span title="Double click to edit" (click)="setEdit(rowIndex, 'initial_hydrogen_cons')"
              *ngIf="!editing[rowIndex + '-initial_hydrogen_cons']">
              {{value}}
            </span>
            <input
              #editableInput
              [attr.data-key]="rowIndex + '-initial_hydrogen_cons'"
              (keydown.enter)="updateValue($event, 'initial_hydrogen_cons', rowIndex)"
              (blur)="updateValue($event, 'initial_hydrogen_cons', rowIndex, true)"
              (input)="updateValueDebounced($event, 'initial_hydrogen_cons', rowIndex)"
              *ngIf="editing[rowIndex+ '-initial_hydrogen_cons']" type="text" [value]="value" />
          </ng-template>
        </ngx-datatable-column>


        <ngx-datatable-column *ngIf="sessionDetail.type === 'hydromobile' || !sessionDetail.type" prop="last_hydrogen_cons" name="Last Hydrogen Cons">
          <ng-template ngx-datatable-cell-template let-rowIndex="rowIndex" let-value="value" let-row="row">
            <span title="Double click to edit" (click)="setEdit(rowIndex, 'last_hydrogen_cons')"
              *ngIf="!editing[rowIndex + '-last_hydrogen_cons']">
              {{value}}
            </span>
            <input
              #editableInput
              [attr.data-key]="rowIndex + '-last_hydrogen_cons'"
              (keydown.enter)="updateValue($event, 'last_hydrogen_cons', rowIndex)"
              (blur)="updateValue($event, 'last_hydrogen_cons', rowIndex, true)"
              (input)="updateValueDebounced($event, 'last_hydrogen_cons', rowIndex)"
              *ngIf="editing[rowIndex+ '-last_hydrogen_cons']" type="text" [value]="value" />
          </ng-template>
        </ngx-datatable-column>


        <ngx-datatable-column prop="race_time" name="Race Time">
          <ng-template ngx-datatable-cell-template let-rowIndex="rowIndex" let-value="value" let-row="row">
            <span title="Double click to edit" (click)="setEdit(rowIndex, 'race_time')"
              *ngIf="!editing[rowIndex + '-race_time']">
              {{value}}
            </span>
            <input
              #editableInput
              [attr.data-key]="rowIndex + '-race_time'"
              (keydown.enter)="updateValue($event, 'race_time', rowIndex)"
              (blur)="updateValue($event, 'race_time', rowIndex, true)"
              (input)="updateValueDebounced($event, 'race_time', rowIndex)"
              *ngIf="editing[rowIndex+ '-race_time']" type="text" [value]="value" />
          </ng-template>
        </ngx-datatable-column>

        <ngx-datatable-column prop="penalty" name="Penalty">
          <ng-template ngx-datatable-cell-template let-rowIndex="rowIndex" let-value="value" let-row="row">
            <span container="body" [ngbTooltip]='mergePenalties(row.penalties)' placement="top"
              (click)="penalty(penaltyModal, row)">
              {{value}}
            </span>
          </ng-template>
        </ngx-datatable-column>


        <ngx-datatable-column prop="total_cons" name="Total Cons">
          <ng-template ngx-datatable-cell-template let-value="value">
            {{value}}
          </ng-template>
        </ngx-datatable-column>

        <ngx-datatable-column *ngIf="sessionDetail.type === 'hydromobile'" prop="total_hydrogen_cons" name="Total Hydrogen Cons">
          <ng-template ngx-datatable-cell-template let-value="value">
            {{value}}
          </ng-template>
        </ngx-datatable-column>

        <!--                <ngx-datatable-column prop="final" name="Final">-->
        <!--                    <ng-template ngx-datatable-cell-template let-value="value">-->
        <!--                        {{value}}-->
        <!--                    </ng-template>-->
        <!--                </ngx-datatable-column>-->

        <ngx-datatable-column prop="score" name="Score">
          <ng-template ngx-datatable-cell-template let-value="value">
            {{value}}
          </ng-template>
        </ngx-datatable-column>


        <ngx-datatable-column prop="status" name="status">
          <ng-template ngx-datatable-cell-template let-rowIndex="rowIndex" let-value="value" let-row="row">
            <span title="Double click to edit" (click)="editing[rowIndex + '-status'] = true"
              *ngIf="!editing[rowIndex + '-status']">
              {{value}}
            </span>
            <select *ngIf="editing[rowIndex + '-status']" (blur)="editing[rowIndex + '-status'] = false"
              (change)="updateValue($event, 'status', rowIndex)" [value]="value">
              <option value="FI">FI</option>
              <option value="DSQ">DSQ</option>
              <option value="DSQ">DSQ</option>
              <option value="DNF">DNF</option>
              <option value="DNS">DNS</option>
            </select>
          </ng-template>
        </ngx-datatable-column>


      </ngx-datatable>


    </div>
  </div>
  <!-- end row -->
</div>


<ng-template #penaltyModal let-modal>
  <div class="modal-header">
    <h5 class="modal-title mt-0">Team Penalties</h5>
    <button type="button" class="close" (click)="modal.dismiss('Cross click')" aria-hidden="true">×</button>
  </div>
  <div class="scroll">
    <div class="table-responsive">
      <table class="table table-nowrap table-hover mb-0">
        <thead>
          <tr>
            <!--        <th scope="col">Type</th>-->
            <th class="table-subject-column" scope="col">Subject</th>
            <th class="table-violation-column" scope="col">Violation</th>
            <th class="table-penalty-column" scope="col">Penalty</th>
            <!--        <th scope="col">Conclusion</th>-->
            <!--        <th scope="col"></th>-->

          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let penalty of modalPenalties">
            <!--        <td>{{penalty.type}}</td>-->
            <td class="table-subject-column"><p>{{penalty.subject}}</p></td>
            <td class="table-violation-column"><p>{{penalty.violation}}</p></td>
            <td class="table-penalty-column">{{penalty.penalty}}</td>
            <!--        <td>{{penalty.conclusion}}</td>-->
            <!--        <td> <a (click)="deletePenalty(penalty.id)"> <i class="bx bxs-trash"></i></a>        </td>-->
          </tr>
        </tbody>
        <tfoot>
          <td></td>
          <td></td>
          <td></td>
        </tfoot>
      </table>
    </div>
  </div>
    <div class="modal-header">
      <h5 class="modal-title mt-0">Add Penalty</h5>
    </div>
    <div class="modal-body">
      <form [formGroup]="penaltyForm">

        <div class="form-group row">
          <label class="col-md-2 col-form-label">Penalty</label>
          <div class="col-md-10">
            <input class="form-control" name="penalty" type="number" formControlName="penalty" placeholder="penalty">
          </div>
        </div>

        <!--      <div class="form-group row">-->
        <!--        <label class="col-md-2 col-form-label">Type</label>-->
        <!--        <div class="col-md-10">-->
        <!--          <select id="type" name="type" formControlName="type" class="form-control">-->
        <!--            <option value="before_race">before_race</option>-->
        <!--            <option value="after_race">after_race</option>-->
        <!--          </select>-->
        <!--        </div>-->
        <!--      </div>-->
        <!--      -->
        <div class="form-group row">
          <label class="col-md-2 col-form-label">Subject</label>
          <div class="col-md-10">
            <input class="form-control" name="subject" type="text" formControlName="subject" placeholder="subject">
          </div>
        </div>
        <div class="form-group row">
          <label class="col-md-2 col-form-label">Violation</label>
          <div class="col-md-10">
            <input class="form-control" name="violation" type="text" formControlName="violation" placeholder="violation">
          </div>
        </div>

        <div class="form-group row">
          <label class="col-md-2 col-form-label">Conclusion</label>
          <div class="col-md-10">
            <input class="form-control" name="conclusion" type="text" formControlName="conclusion"
              placeholder="conclusion">
          </div>
        </div>
        <div class="text-center mt-4">
          <button type="button" (click)="addPenalty()" class="btn btn-primary">Add Penalty</button>
        </div>
      </form>

      <!--
      <div class="table-responsive">
          <table class="table table-nowrap table-hover mb-0">
              <thead>
                  <tr>
                      <th scope="col">Name</th>
                      <th scope="col">Date</th>
                      <th scope="col">Rm</th>
                  </tr>
              </thead>
              <tbody>
                  <tr *ngFor="let promotion of teamDetail?.penalty">
                      <td>{{promotion.name}}</td>
                      <td>{{promotion.created_at | date : "yyyy-MM-dd HH:mm:ss"}}</td>
                      <td> <a (click)="deletePromotion(promotion.id)"> <i class="bx bxs-trash"></i></a>
                      </td>
                  </tr>


              </tbody>
          </table>
      </div>-->
    </div>
</ng-template>


<ng-template #addTeamModal let-modal>

  <div class="modal-header">
    <h5 class="modal-title mt-0">Add Teams</h5>
    <button type="button" (click)="addAllpossibleTeams()" class="btn btn-success">Add All Teams</button>

  </div>
  <div class="modal-body">

    <form>
      <ng-multiselect-dropdown [formControl]="teamDropdown" [settings]="dropdownSettings" [data]="teams">
      </ng-multiselect-dropdown>
      <div class="modal-header">
        <h5 class="modal-title mt-0">Selected Teams</h5>
      </div>
      <div class="inboxContainer">
        <ul *ngFor="let item of teamDropdown?.value">
          <li>{{item.team_name}}</li>
        </ul>
      </div>
      <div class="text-center mt-4">
        <button type="button" (click)="addTeams()" class="btn btn-primary">Add Teams</button>
      </div>
    </form>

  </div>
</ng-template>
<ngx-spinner type="ball-clip-rotate-multiple" size="medium"> </ngx-spinner>
