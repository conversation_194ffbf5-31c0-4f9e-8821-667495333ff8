import { Compo<PERSON>, On<PERSON>nit, <PERSON><PERSON><PERSON><PERSON>n, <PERSON>ement<PERSON><PERSON>, QueryList, AfterViewChecked } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { RaceService } from 'src/app/core/services/race.service';
import { ActivatedRoute } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { TeamsService } from 'src/app/core/services/teams.service';
import { LoaderService } from '../../../core/services/loader.service';
import Swal from 'sweetalert2';
import { EvaluationStatusEnum } from '../../evaluation/evaluation-status.enum';
import { NgxSpinnerService } from 'ngx-spinner';

@Component({
  selector: 'app-race-session',
  templateUrl: './race-session.component.html',
  styleUrls: ['./race-session.component.scss'],
})
export class RaceSessionComponent implements OnInit, AfterViewChecked {

  teamId;
  breadCrumbItems: Array<{}>;
  sessionId;
  sessionDetail;

  statData = [
    {
      icon: 'bx bx-package',
      title: 'Available Team',
      value: '0',
    }, {
      icon: 'bx bx-package',
      title: 'Total Team In This Session',
      value: '0',
    },
  ];
  editing = {};
  rows = [];
  columns = [];

  loadingIndicator: boolean = false;
  reorderable: boolean = true;
  penaltyForm: FormGroup;
  modalPenalties: any;

  teams: any[];

  teamDropdown: FormControl;
  dropdownSettings: any = {};

  debounceTimers: { [key: string]: any } = {};

  @ViewChildren('editableInput') editableInputs: QueryList<ElementRef>;
  focusedInputKey: string = null;

  constructor(
    public loader: LoaderService,
    private teamsService: TeamsService,
    private fb: FormBuilder,
    private raceService: RaceService,
    private route: ActivatedRoute,
    private modalService: NgbModal,
    private spinner: NgxSpinnerService
  ) { }

  ngOnInit(): void {
    this.breadCrumbItems = [{ label: 'Session Detail' }, { label: 'Session Overview', active: true }];
    this.route.params.subscribe(params => {
      this.sessionId = params['id'];
      this.getSessionDetail();
    });

    this.dropdownOptions();
  }

  exportScores(){
    this.spinner.show();
    this.raceService.exportScores(this.sessionId).subscribe(data => {
      this.spinner.hide();
      this.downLoadFile(data, 'application/ms-excel', `EC_${this.sessionDetail.name}_scores.xlsx`)
    })
  }

  getTeams() {
    this.teamsService.getTeams({ limit: 100, 'includes[]': 'currentEvaluation' })
      .subscribe((data: any) => {
        this.statData[0].value = data.meta.pagination.total;
        this.teams = data.data;
        this.teams = this.teams.filter(team => team.current_evaluation?.status && !this.rowsData.some(row => row.team_id === team.id));
        this.teams.map(team => {
          if (team.current_evaluation?.status === EvaluationStatusEnum.SUCCESS) {
            team.team_name += ' - ' + team.vehicle_category;
          }

        });
      });
  }

  rowsData = [];

  getSessionDetail() {
    this.columns = [];
    this.rowsData = [];
    this.raceService.getSession(this.sessionId).subscribe(data => {
      this.columns = [
        { prop: 'final' },
        { prop: 'initial_energy_cons' },
        { prop: 'initial_hydrogen_cons' },
        { prop: 'laps' },
        { prop: 'last_energy_cons' },
        { prop: 'last_hydrogen_cons' },
        { prop: 'race_time' },
        { prop: 'penalty' },
        { prop: 'score' },
        { prop: 'status' },
        { prop: 'start_point' },
        { prop: 'total_cons' },
        { prop: 'total_hydrogen_cons' },
        { prop: 'valid_laps' },
        // { prop: 'team_id' },
        { prop: 'team_name' },
        { prop: 'vehicle_number' },
      ];

      data['data']['race_teams'].forEach(sessionTeam => {
        this.rowsData.push({
          id: sessionTeam['id'],
          final: sessionTeam['final'],
          initial_energy_cons: sessionTeam['initial_energy_cons'],
          initial_hydrogen_cons: sessionTeam['initial_hydrogen_cons'],
          laps: sessionTeam['laps'],
          last_energy_cons: sessionTeam['last_energy_cons'],
          last_hydrogen_cons: sessionTeam['last_hydrogen_cons'],
          race_time: sessionTeam['race_time'] ?  sessionTeam['race_time'] : 0,
          penalty: sessionTeam['penalty'],
          // penalties: team['team']?.['penalties'].map(item => item.subject.substr(0, 22) + '... : ' + item.penalty).join('\n'),
          penalties: sessionTeam['team']['penalties'],
          score: sessionTeam['score'],
          status: sessionTeam['status']  ?  sessionTeam['status'] : 'ST' ,
          session_id: sessionTeam['session_id'],
          start_point: sessionTeam['start_point'],
          total_cons: sessionTeam['total_cons'],
          total_hydrogen_cons: sessionTeam['total_hydrogen_cons'],
          valid_laps: sessionTeam['valid_laps'],
          team_id: sessionTeam['team_id'],
          team_name: sessionTeam['team']['team_name'],
          vehicle_number: sessionTeam['team']['vehicle_number'],
          vehicle_category: sessionTeam['team']['vehicle_category']
        });
      });


      this.getTeams();

      this.sessionDetail = data['data'];
      this.rows = this.rowsData;

      this.statData[1].value = data['data']['race_teams'].length;

      this.penaltyForm = this.fb.group({
        team_id: [],
        session_id: [],
        subject: [],
        violation: [],
        penalty: [],
        type: [],
        conclusion: [],
      });
      // console.log("this.rows");
      // console.log(this.rows);
      // this.raceService.getSessionTeam(this.sessionId).subscribe(data => {
      //   // console.log("getSessionTeam");
      //   // console.log(data);
      // });


    });
  }

  updateValue(event, cell, rowIndex, isBlur = false) {
    // Enter ve blur aynı anda tetiklenmesin diye
    if (this.editing[rowIndex + '-' + cell] === false) return;

    // Debounce timer varsa temizle
    const timerKey = rowIndex + '-' + cell;
    if (this.debounceTimers[timerKey]) {
      clearTimeout(this.debounceTimers[timerKey]);
      delete this.debounceTimers[timerKey];
    }

    this.editing[rowIndex + '-' + cell] = false;
    this.rows[rowIndex][cell] = event.target.value;
    this.rows = [...this.rows];
    let obj = { [cell]: this.rows[rowIndex][cell] };
    this.raceService.updateTeamScore(obj, this.rows[rowIndex]['id']).subscribe(data => {
      const sessionTeam = (data as any).data;
      this.rows[rowIndex] = {
        id: sessionTeam['id'],
        final: sessionTeam['final'],
        initial_energy_cons: sessionTeam['initial_energy_cons'],
        initial_hydrogen_cons: sessionTeam['initial_hydrogen_cons'],
        laps: sessionTeam['laps'],
        last_energy_cons: sessionTeam['last_energy_cons'],
        last_hydrogen_cons: sessionTeam['last_hydrogen_cons'],
        race_time: sessionTeam['race_time'] ?  sessionTeam['race_time'] : 0,
        penalties: sessionTeam['team']['penalties'],
        penalty: sessionTeam['penalty'],
        score: sessionTeam['score'],
        status: sessionTeam['status']  ?  sessionTeam['status'] : 'ST' ,
        session_id: sessionTeam['session_id'],
        start_point: sessionTeam['start_point'],
        total_cons: sessionTeam['total_cons'],
        total_hydrogen_cons: sessionTeam['total_hydrogen_cons'],
        valid_laps: sessionTeam['valid_laps'],
        team_id: sessionTeam['team_id'],
        team_name: sessionTeam['team']['team_name'],
        vehicle_number: sessionTeam['team']['vehicle_number'],
      };
      this.rows = [...this.rows];
    }, error => {
      this.showValidationError(error);
    });
  }

  updateValueDebounced(event: any, cell: string, rowIndex: string) {
    const timerKey = rowIndex + '-' + cell;
    if (this.debounceTimers[timerKey]) {
      clearTimeout(this.debounceTimers[timerKey]);
    }
    this.debounceTimers[timerKey] = setTimeout(() => {
      if (this.editing[timerKey]) {
        this.updateValue(event, cell, rowIndex);
      }
      delete this.debounceTimers[timerKey];
    }, 1000);
  }

  reloadScore() {
    location.reload();
  }

  addAllpossibleTeams() {
    this.raceService.addAllPossibleTeams(this.sessionId).subscribe(data => {
      this.reloadScore();
    });
  }

  downLoadFile(data: any, type: string, filename) {
    let blob = new Blob([data], { type: type });

    let url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.click();
  }

  addPenalty() {
    this.penaltyForm.patchValue({ team_id: this.teamId });
    this.penaltyForm.patchValue({ session_id: this.sessionId });
    this.penaltyForm.patchValue({ type: 'after_race' });
    this.teamsService.addPenalty(this.penaltyForm.value).subscribe(data => {
      this.position('Added Successfully', 'Penalty Added', 'OK', 'success');
      this.reloadScore();
    }, error => {
      this.showValidationError(error);
    });
  }
  position(title, text, confirmText, icon, dismissAll = true) {
    Swal.fire({
      title: title,
      html: text,
      icon: icon,
      confirmButtonColor: '#34c38f',
      cancelButtonColor: '#f46a6a',
      confirmButtonText: confirmText,
    }).then(result => {
      if (result.value && dismissAll) {
        this.modalService.dismissAll();
      }
    });
  }

  penalty(penaltyModal: any, row) {
    this.teamId = row.team_id;
    this.modalPenalties = row.penalties;

    this.modalService.open(penaltyModal, { centered: true, windowClass: 'modal-holder' });
  }


  public mergePenalties(penalties) {
    return penalties?.map(item => item.subject.substr(0, 22) + '... : ' + item.penalty)
      .join('\n');

  }

  openAddTeamModal(modal: any) {
   this.modalService.open(modal, { centered: true, windowClass: 'modal-holder' });
  }

  dropdownOptions() {
    this.dropdownSettings = {
      singleSelection: false,
      idField: 'id',
      textField: 'team_name',
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
      itemsShowLimit: 3,
      allowSearchFilter: true,
    };
    this.teamDropdown = new FormControl();
  }


  addTeams() {
    const teamIds = this.teamDropdown.value.map((item: any) => item.id);

    this.raceService.addTeams(teamIds, this.sessionId).subscribe( data => {
      this.position('Added Successfully', 'Teams Added', 'OK', 'success');
      this.reloadScore();
    }, error => {
      this.showValidationError(error);
    });
  }


  showValidationError(error) {
    let message = error.error.message;
    if (message === 'validation.error') {
      message = Object.values(error.error.errors).join('\n<br>');
    }

    this.position('Error', message, 'OK', 'error', false);
  }

  ngAfterViewChecked() {
    if (this.focusedInputKey && this.editableInputs) {
      const inputToFocus = this.editableInputs.find((input: ElementRef) => {
        return input.nativeElement.getAttribute('data-key') === this.focusedInputKey;
      });
      if (inputToFocus) {
        inputToFocus.nativeElement.focus();
        this.focusedInputKey = null;
      }
    }
  }

  setEdit(rowIndex: number, field: string) {
    this.editing[rowIndex + '-' + field] = true;
    this.focusedInputKey = rowIndex + '-' + field;
  }
}
