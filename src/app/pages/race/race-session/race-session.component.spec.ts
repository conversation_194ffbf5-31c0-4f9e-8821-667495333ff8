import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { RaceSessionComponent } from './race-session.component';

describe('RaceSessionComponent', () => {
  let component: RaceSessionComponent;
  let fixture: ComponentFixture<RaceSessionComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ RaceSessionComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(RaceSessionComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
