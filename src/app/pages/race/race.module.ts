import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { RaceRoutingModule } from './race-routing.module';
import { RaceComponent } from './race/race.component';
import { UIModule } from 'src/app/shared/ui/ui.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbDropdownModule, NgbTooltipModule, NgbNavModule, NgbModalModule, NgbToastModule } from '@ng-bootstrap/ng-bootstrap';
import { WidgetModule } from 'src/app/shared/widget/widget.module';
import { NgApexchartsModule } from 'ng-apexcharts';
import { PerfectScrollbarModule } from 'ngx-perfect-scrollbar';
import { RaceListComponent } from './race-list/race-list.component';
import { CreateRaceComponent } from './create-race/create-race.component';
import { NgxDatatableModule } from '@swimlane/ngx-datatable';
import {  RaceDetailComponent } from './race-detail/race-detail.component';
import { RaceSessionComponent } from './race-session/race-session.component';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { NgxSpinnerModule } from 'ngx-spinner';

@NgModule({
  declarations: [RaceComponent, RaceListComponent, CreateRaceComponent, RaceDetailComponent, RaceSessionComponent],
  imports: [
    CommonModule,
    RaceRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    UIModule,
    NgbDropdownModule,
    NgbTooltipModule,
    NgbNavModule,
    WidgetModule,
    NgApexchartsModule,
    PerfectScrollbarModule,
    NgxDatatableModule,
    NgbModalModule,
    NgMultiSelectDropDownModule,
    NgbToastModule,
    NgxSpinnerModule
  ],
})
export class RaceModule { }
