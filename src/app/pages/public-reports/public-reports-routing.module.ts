import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PublicReportsComponent } from './public-reports/public-reports.component';
import { PublicLayoutComponent } from 'src/app/layouts/public-layout/public-layout.component';

const routes: Routes = [
  { path: '', component: PublicLayoutComponent, children: [
    { path: '', component: PublicReportsComponent }
  ]}
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class PublicReportsRoutingModule { }
