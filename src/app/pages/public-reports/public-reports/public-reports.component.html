<div class="container-fluid">
    <div class="wrapper">
            <div class="list">
                    <div class="list__header">
                            <h5>Efficiency Challange</h5>
                            <h1></h1>
                            <div style="margin-top: 10px;">
                                <select [ngModel]="selectedRaceId" (change)="onRaceSelect($event)">
                                    <option value="" disabled selected>Yarış Seçiniz</option>
                                    <option *ngFor="let race of raceList" [value]="race.id">{{race.name}}</option>
                                </select>
                            </div>
                    </div>
                    <div class="list__body" *ngIf="leaderBoard">
                            <table class="list__table">
                                    <tr class="list__row" *ngFor="let team of leaderBoard; let i = index;">
                                            <td class="list__cell"><span class="list__value">{{team.team_id}}</span><small class="list__label">ID</small></td>
                                            <td class="list__cell"><span class="list__value"><img class="avatar-custom" [src]="team.team.logo_url"></span><small class="list__label">LOGO</small></td>
                                            <td class="list__cell"><span
                                                            class="uni list__value" style="font-weight:bold;">{{team.team.team_name}}</span><small
                                                            class="list__label">Team Name</small>

                                            <td class="list__cell"><span
                                                            class="uni list__value">{{team.team.university_name}}</span><small
                                                            class="list__label">{{team.team.driver_name}}</small>
                                            </td>
                                            <td  class="list__cell"><span
                                                            class="list__value">{{team.total_cons}}</span><small
                                                            class="list__label">{{ 'Elektrik Tüketim (Wh)' }}</small></td>
                                            <td class="list__cell" *ngIf = "sessionType == 'hydromobile'"><span
                                                            class="list__value">{{team.total_hydrogen_cons || 0}}</span><small
                                                            class="list__label">Hidrojen Tüketim (Lt)</small></td>
                                            <td class="list__cell"><span
                                                            class="list__value">{{team.status}}</span><small
                                                            class="list__label">DURUM</small></td>
                                            <td class="list__cell"><span
                                                            class="list__value">{{team.race_time || '--'}}</span><small
                                                            class="list__label">Toplam süre</small></td>
                                            <td class="list__cell"><span
                                                            class="list__value">{{team.brake_check || '--'}}</span><small
                                                            class="list__label">Fren Rampası Kontrolü</small></td>
                                            <td class="list__cell"><span
                                                            class="list__value">{{team.note || '--'}}</span><small
                                                            class="list__label">Açıklama</small></td>
                                    </tr>

                            </table>

                      <div class="status-legend">
                        <h6 class="status-legend__title">Durum Açıklamaları</h6>
                        <div class="status-legend__items">
                          <div class="status-legend__item">
                            <span class="status-legend__code">FI</span>
                            <span class="status-legend__description">Finished</span>
                          </div>
                          <div class="status-legend__item">
                            <span class="status-legend__code">DNS</span>
                            <span class="status-legend__description">Did not start</span>
                          </div>
                          <div class="status-legend__item">
                            <span class="status-legend__code">DNF</span>
                            <span class="status-legend__description">Did not finish</span>
                          </div>
                          <div class="status-legend__item">
                            <span class="status-legend__code">DSQ</span>
                            <span class="status-legend__description">Disqualified</span>
                          </div>
                        </div>
                      </div>

                    </div>
            </div>
    </div>
</div>
