import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { AdminService } from 'src/app/core/services/admin.service';
import { ActivatedRoute, Router } from '@angular/router';
import Swal from 'sweetalert2';
import { RolesEnum } from '../roles.enum';
import { NgxSpinnerService } from 'ngx-spinner';

@Component({
  selector: 'app-user-edit',
  templateUrl: './user-edit.component.html',
  styleUrls: ['./user-edit.component.scss'],
})
export class UserEditComponent implements OnInit {
  allRoles
  createUserObj = {
    'first_name': 'Super',
    'last_name': 'Admin',
    'email': 'test2@localhost',
    'picture_id': null,
    'role': 'superadmin',
    'password': 'secret',
    'password_confirmation': 'secret',
    'details': {
      'gender': 'male',
      'birthday': '1991-12-30',
      'country': null,
      'timezone': 'UTC',
      'description': null,
      'language': null,
    },
  };
  breadCrumbItems: Array<{}>;
  userForm: FormGroup;
  userId;

  constructor(private fb: FormBuilder,private spinner: NgxSpinnerService, private adminService: AdminService, private router: Router, private route: ActivatedRoute) { }

  ngOnInit(): void {
    this.breadCrumbItems = [{ label: 'Edit User' }, { label: 'User Wizard', active: true }];
    this.route.params.subscribe(params => {
      this.userId = params['id'];
      // this.getEvaluation();
      this.getUserDetail();
    });


  }


  getUserDetail() {
    this.adminService.getUser(this.userId).subscribe(data => {

      this.arrayToFGroup (data['data']);
    });
  }

  arrayToFGroup(trans) {
  
    let key;
    const temp: any = {};
    this.adminService.getAllRoles().subscribe(roles => {
      this.allRoles = roles
      roles.map(item => {
        temp[item.name] = [false];
      });
      for (key in trans.role) {
        if (trans.role.hasOwnProperty(key)) {
          temp[trans.role[key]] = true;
        }
      }
      this.createUserForm(trans, temp)
    })
  }

  createUserForm(obj, temp) {

    this.userForm = this.fb.group({
      first_name: [obj.first_name],
      last_name: [obj.last_name],
      email: [obj.email],
      role: this.fb.group(temp),
      password: [],
      password_confirmation: [],
    });
  }

  updateUser() {
    this.spinner.show();
    this.userForm.patchValue({ role: this.roles });

    this.userForm.value.role = Object.entries(this.userForm.value.role)
      .filter(item => item[1])
      .map(item => item[0]);

    if (!this.userForm.value.password) {
      delete this.userForm.value.password;
      delete this.userForm.value.password_confirmation;
    }

    this.adminService.updateUser(this.userForm.value, this.userId).subscribe(
      data => {
        this.spinner.hide();
        this.position('Updated Successfully');
      }, error => {
        this.spinner.hide();
        this.position('Error');
      }
    );
  }

  position(title) {
    Swal.fire({
      position: 'center',
      icon: 'success',
      title: title,
      timer: 1500,
      allowOutsideClick: false,
      showConfirmButton: true,
      showCancelButton: false,
    })
      .then((willDelete) => {

        this.redirectToList();
      });
  }

  roles = [];

  checkValue(event) {
    if (event.target.checked) {
      this.roles.push(event.target.value);
    } else {
      var index = this.roles.indexOf(event.target.value);
      this.roles.splice(index, 1);
    }
  }


  redirectToList() {
    this.router.navigateByUrl(`/user`);
  }


}
