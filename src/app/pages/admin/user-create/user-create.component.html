<div class="container-fluid">
  <app-page-title title="Create User" [breadCrumbItems]="breadCrumbItems"></app-page-title>

  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          <form [formGroup]="userForm">

            <div class="form-group row">
              <label for="first-name" class="col-md-2 col-form-label">First Name</label>
              <div class="col-md-10">
                <input class="form-control" type="text" value="" id="first-name"
                       formControlName="first_name">
              </div>
            </div>

            <div class="form-group row">
              <label for="last-name" class="col-md-2 col-form-label">Last Name</label>
              <div class="col-md-10">
                <input class="form-control" type="text" value="" id="last-name"
                       formControlName="last_name">
              </div>
            </div>

            <div class="form-group row">
              <label for="mail" class="col-md-2 col-form-label">Email</label>
              <div class="col-md-10">
                <input class="form-control" type="email" value="" id="mail"
                       formControlName="email">
              </div>
            </div>

            <div class="form-group row">
              <label class="col-md-2 col-form-label">Role</label>
              <div class="col-md-10">

                <fieldset>
                  <ng-container *ngFor="let role of (roles$ | async)">
                    <label class="form-check form-check-label" [for]="role">
                      <input
                        [id]='role.id'
                        type="checkbox" formControlName="role" (change)="checkValue($event)"
                        name="role" [value]="role.name">
                      {{ role.name }}</label>
                  </ng-container>
                </fieldset>
              </div>
            </div>


            <div class="form-group row">
              <label for="pass" class="col-md-2 col-form-label">Password</label>
              <div class="col-md-10">
                <input class="form-control" type="password" value="" id="pass"
                       formControlName="password">
              </div>
            </div>


            <div class="form-group row">
              <label for="pass-again" class="col-md-2 col-form-label">Password
                Confirmation</label>
              <div class="col-md-10">
                <input class="form-control" type="password" value="" id="pass-again"
                       formControlName="password_confirmation">
              </div>
            </div>
            <div class="row">
              <div class="col-12">
                <div class="text-center my-3">

                  <a (click)="createUser()" class="btn btn-success inner">
                    Create User </a>
                </div>
              </div> <!-- end col-->
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
<ngx-spinner type="ball-clip-rotate-multiple" size="medium"> </ngx-spinner>