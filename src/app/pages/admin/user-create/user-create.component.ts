import { Component, OnInit } from '@angular/core';
import Swal from 'sweetalert2';
import { AdminService } from 'src/app/core/services/admin.service';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { RolesEnum } from '../roles.enum';
import { Observable } from 'rxjs';
import { NgxSpinnerService } from 'ngx-spinner';

@Component({
  selector: 'app-user-create',
  templateUrl: './user-create.component.html',
  styleUrls: ['./user-create.component.scss'],
})
export class UserCreateComponent implements OnInit {

  roles$: Observable<any[]>
  createUserObj = {
    'first_name': 'Super',
    'last_name': 'Admin',
    'email': 'test2@localhost',
    'picture_id': null,
    'role': 'superadmin',
    'password': 'secret',
    'password_confirmation': 'secret',
    'details': {
      'gender': 'male',
      'birthday': '1991-12-30',
      'country': null,
      'timezone': 'UTC',
      'description': null,
      'language': null,
    },
  };
  breadCrumbItems: Array<{}>;
  userForm: FormGroup;

  constructor(private fb: FormBuilder, private adminService: AdminService, private router: Router, private spinner: NgxSpinnerService) { }

  ngOnInit(): void {
    this.breadCrumbItems = [{ label: 'Create User' }, { label: 'User Wizard', active: true }];
    this.userForm = this.fb.group({
      first_name: [],
      last_name: [],
      email: [],
      role: [],
      password: [],
      password_confirmation: [],
    });
    this.roles$ = this.adminService.getAllRoles();
  }

  createUser() {
    this.spinner.show();
    console.log('this.userForm.value', this.userForm.value);
    // this.userForm.patchValue({ role: this.roles });
    this.adminService.createUser({
      ...this.userForm.value,
      role: this.roles,
    }).subscribe(
      data => {
        this.spinner.hide();
        this.position('Added Successfully', 'Added', 'OK', 'success')
          .then(i => {
            this.redirectToList();
          });
      }
    );
  }

  position(title, text, confirmText, icon, dismissAll = true) {
    return Swal.fire({
      title: title,
      html: text,
      icon: icon,
      confirmButtonColor: '#34c38f',
      cancelButtonColor: '#f46a6a',
      confirmButtonText: confirmText,
    }).then(result => {
      if (result.value && dismissAll) {

      }
    });
  }

  roles = [];

  checkValue(event) {
    if (event.target.checked) {
      this.roles.push(event.target.value);
    } else {
      var index = this.roles.indexOf(event.target.value);
      this.roles.splice(index, 1);
    }
    console.log('roles', this.roles);
  }


  redirectToList() {
    this.router.navigateByUrl(`/user`);
  }

}
