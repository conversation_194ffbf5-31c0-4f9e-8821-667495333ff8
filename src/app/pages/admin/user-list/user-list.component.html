<div class="container-fluid">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="text-center my-3">
                <a [routerLink]="['/user/create']" class="btn btn-success inner mr-3">
                    Add User </a>
            </div>
        </div> <!-- end col-->
    </div>
    <app-page-title title="System Users" [breadCrumbItems]="breadCrumbItems"></app-page-title>
    <!-- end page title -->
    <div class="row">
        <div class="col-12">
            <input
            type="text"
            class="mx-2 mb-2 form-control"
            style="width:60%;"
            placeholder="Type to filter..."
            (keyup)="updateFilter($event)"
          />
            <ngx-datatable [rows]="rows" class="bootstrap" [loadingIndicator]="loadingIndicator" [columnMode]="'force'"
                [headerHeight]="50" [footerHeight]="50" [rowHeight]="'auto'" limit="10" [columns]="columns"
                [reorderable]="reorderable">
                <ngx-datatable-column *ngFor="let column of columns; let i = index;" [name]="column.prop"
                    prop="{{column.prop}}">
                    <ng-template let-value="value" let-row="row" ngx-datatable-cell-template>

                        <a *ngIf="column.prop === 'id'" href="javascript: void(0);"
                        [routerLink]="['/user/edit/', row.id]">{{value}}</a>


                        <span *ngIf="column.prop !== 'id'">{{value}}</span> 
                    </ng-template>
                </ngx-datatable-column>
            </ngx-datatable>
        </div>
    </div>
    <!-- end row -->



</div> <!-- container-fluid -->