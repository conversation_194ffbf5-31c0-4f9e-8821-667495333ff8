import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { AdminService } from 'src/app/core/services/admin.service';

@Component({
  selector: 'app-user-list',
  templateUrl: './user-list.component.html',
  styleUrls: ['./user-list.component.scss']
})
export class UserListComponent implements OnInit {

  editing = {};
  rows = [];
  columns = [];
  loadingIndicator: boolean = true;
  reorderable: boolean = true;
  breadCrumbItems: Array<{}>;
  filterForm: FormGroup;
  protected temp: any[];

  private columnsWithSearch = ['id', 'email', 'first_name', 'last_name', 'role', 'username'];

  constructor(private adminService: AdminService, private fb: FormBuilder) {
    this.filterForm = this.fb.group({
      limit: [100]
    })
  }

  ngOnInit(): void {
    this.breadCrumbItems = [{ label: 'System Users' }, { label: 'System User List', active: true }];
    
    
    this.columns = [
      { prop: 'id' },
      { prop: 'email' },
      { prop: 'first_name' },
      { prop: 'last_name' },
      { prop: 'role' },
      { prop: 'username' },
      { prop: 'created_at' }
    ];
    this.getUsers();
  }


  getUsers() {
    this.adminService.getUsers(this.filterForm.value).subscribe(data => {
      this.temp = data["data"];
      this.rows = data["data"];
    })
  }

  updateFilter(event) {
    const orginalValue = event.target.value;
    const filteredValue = orginalValue.toLocaleLowerCase();

    // filter our data
    const temp = this.temp.filter(item => {
      for (let i = 0; i < this.columnsWithSearch.length; i++) {
        const columnValue = item[this.columnsWithSearch[i]];
        if ((( !!columnValue && columnValue.toString().toLocaleLowerCase().indexOf(filteredValue) !== -1) || !filteredValue) || (( !!columnValue && columnValue.toString().indexOf(orginalValue) !== -1) || !orginalValue)) {
          return true;
        }
      }
    });

    // update the rows
    this.rows = temp;
    // Whenever the filter changes, always go back to the first page
    // this.table.offset = 0;
  }

}
