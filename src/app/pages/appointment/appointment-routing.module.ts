import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AppointmentComponent } from './appointment/appointment.component';
import { AppointmentScreenComponent } from './appointment-screen/appointment-screen.component';


const routes: Routes = [
  {
    path: 'appointment',
    component: AppointmentComponent
  },
  {
    path: 'appointment-screen',
    component: AppointmentScreenComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AppointmentRoutingModule { }
