import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

import listPlugin from '@fullcalendar/list';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import bootstrapPlugin from '@fullcalendar/bootstrap';

import { EventInput } from '@fullcalendar/core';
import { calendarEvents } from '../appointment/data';
import Swal from 'sweetalert2';
import { LoaderService } from '../../../core/services/loader.service';

import { HttpClient } from '@angular/common/http';
import { TeamsService } from 'src/app/core/services/teams.service';
import { AppointmentService } from 'src/app/core/services/appointment.service';
import { EvaluationService } from 'src/app/core/services/evaluation.service';
import { EventService } from 'src/app/core/services/event.service';
@Component({
  selector: 'app-appointment-screen',
  templateUrl: './appointment-screen.component.html',
  styleUrls: ['./appointment-screen.component.scss']
})
export class AppointmentScreenComponent implements OnInit {


  @ViewChild('apCalendar', { read: ElementRef }) public apCalendar: ElementRef<any>;

  // bread crumb items
  breadCrumbItems: Array<{}>;

  // event form
  formData: FormGroup;
  formEditData: FormGroup;
  randomAppointmentForm: FormGroup;


  // Form submition value
  submitted: boolean;

  // Form category data
  category = [];

  title = [];

  // Date added in event
  newEventDate: Date;

  // Edit event
  editEvent: EventInput;

  // Delete event
  deleteEvent: EventInput;

  calendarWeekends: any;
  // show events
  calendarEvents: EventInput[];

  filterForm: FormGroup;
  // calendar plugin
  calendarPlugins = [listPlugin, dayGridPlugin, bootstrapPlugin, timeGridPlugin, interactionPlugin];

  appointments = [];

  constructor(private eventService: EventService, private evalService: EvaluationService, private appointmentService: AppointmentService, private teamsService: TeamsService, private modalService: NgbModal, private formBuilder: FormBuilder, public loader: LoaderService, public http: HttpClient) {

    this.filterForm = this.formBuilder.group({
      limit: [100],
    });
  }

  makeScroll() {
    console.log("makeScroll");
    this.apCalendar.nativeElement.scrollTop = 250;
  }

  ngOnInit() {
    this.breadCrumbItems = [{ label: 'Rastmobile' }, { label: 'Calendar', active: true }];
    this.eventService.broadcast('changeLayout', "horizontal");
    this.getAppointments();
    this.getTeams();

    this.makeHttpCall();
    this.formData = this.formBuilder.group({
      title: ['', [Validators.required]],
      start_time: [''],
    });

    this.formEditData = this.formBuilder.group({
      editTitle: [],
      status: [],
    });

    this.randomAppointmentForm = this.formBuilder.group({
      start_time: [],
    });

    this._fetchData();
  }

  ngAfterViewChecked() {
    this.makeScroll();
  }

  closeModal() {
    this.modalService.dismissAll();
    this.goToEval = false;
  }


  teamId;
  goToEval = false;



  doneEvent() {
    const currentAppointment = this.appointments.find(appointment => appointment.id == this.editEvent.id);
    this.teamId = currentAppointment.team_id;

    // tslint:disable-next-line: radix
    let editObj = {
      'status': this.formEditData.get('status').value,
      'comments': 'nocomment',
    };
    this.appointmentService.patchAppointment(parseInt(this.editEvent.id + ''), editObj).subscribe((data: any) => {
      this.getAppointments();

      this.goToEval = data.data.team?.current_evaluation?.id;
      if (editObj.status === 'joined') {
        if (!data.data.team?.current_evaluation) {

          // startEval and show Eval detail button
          this.startEvaluation();
        }
      } else {
        this.modalService.dismissAll();

        this.messagePopup('Successfully updated', 'updated', 'OK', 'success');
      }
    });
  }

  startEvaluation() {
    let date = new Date();
    let evalObj = {
      'team_id': parseInt(this.teamId),
      'eval_date': date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate(),
    };

    this.evalService.startEvaluation(evalObj).subscribe((data: any) => {
      this.messagePopup('Successfully changed', 'Eval Started', 'OK', 'success');

      this.goToEval = data.data.id;
    }, error => {
      this.messagePopup('Error', error.error.message, 'OK', 'error');
    });
  }

  getTeams() {
    this.teamsService.getTeams(this.filterForm.value).subscribe(teams => {
      teams['data'].forEach(teamsData => {
        // console.log('teamsData');
        // console.log(teamsData);
        if (teamsData['completed'] === 1) {
          this.title.push({
            'id': teamsData['id'],
            'team_name': `${teamsData['university_name']} : ${teamsData['team_name']}`,
          });
        }
      });

    });
  }


  getAppointments() {
    this.appointmentService.getAppointments(this.filterForm.value).subscribe(appointment => {
      console.log(appointment);
      this.calendarEvents = [];
      this.appointments = appointment['data'];
      appointment['data'].forEach(data => {
        let dataStartTime = new Date().getTime();
        let current = new Date(data['start_time']).getTime() + 60000; // 10 minutes from now
        let className;
        if (data['status'] === 'not_joined') {
          className = 'bg-danger';
        } else if (data['status'] === 'joined') {
          className = 'bg-success';
        } else if (dataStartTime >= current) {
          className = 'bg-warning';
        } else {
          className = 'bg-primary';
        }
        this.calendarEvents.push(
          {
            id: data['id'],
            title: data['team']['team_name'],
            start: data['start_time'],
            className: className,
          });
      });

    });
  }

  makeRandomAppointment() {
    console.log(this.randomAppointmentForm.value);
    this.appointmentService.randomAppointment(this.randomAppointmentForm.value)
      .subscribe((data: any) => {
        this.getAppointments();
        const appointment = data.data;
        this.messagePopup('Random Appointment placed successfully',
          `${appointment.team_count} Appointment created in date
        between ${appointment.start_time} and ${appointment.end_time}`, 'OK', 'success');
        this.modalService.dismissAll();
      });
  }

  /**
   * Returns form
   */
  get form() {
    return this.formData.controls;
  }

  /**
   * Open Event Modal
   * @param content modal content
   * @param event calendar event
   */
  openModal(content: any, event: any) {
    this.newEventDate = event.date;
    this.modalService.open(content);
  }


  openRandomModal(randomAppointment: any, event: any) {
    this.modalService.open(randomAppointment);
  }

  makeHttpCall() {
    this.http.get('https://jsonplaceholder.typicode.com/comments')
      .subscribe((r) => {
        this.loader.isLoading.next(false);
      });
  }

  /**
   * Open Delete Confirmation Modal
   */
  confirm() {
    Swal.fire({
      title: 'Are you sure?',
      text: 'You won\'t be able to revert this!',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#34c38f',
      cancelButtonColor: '#f46a6a',
      confirmButtonText: 'Yes, delete it!',
    }).then(result => {
      if (result.value) {
        this.deleteEventData();
        Swal.fire('Deleted!', 'Event has been deleted.', 'success');
      }
    });
  }

  /**
   * Show successfull Save Dialog
   */
  position(title) {
    Swal.fire({
      position: 'center',
      icon: 'success',
      title: title,
      showConfirmButton: false,
      timer: 1500,
    });
  }

  openEditModal(editcontent: any, event: any) {
    this.formEditData = this.formBuilder.group({
      editTitle: event.event.title,
      status: [],
    });
    const currentAppointment = this.appointments.find(appointment => appointment.id == event.event.id);
    this.goToEval = currentAppointment?.team.current_evaluation?.id;

  

    // tslint:disable-next-line: max-line-length
    this.editEvent = {
      id: event.event.id,
      title: event.event.title,
      start: event.event.start,
      classNames: event.event.classNames[event.event.classNames.length - 1],
    };
    this.modalService.open(editcontent);
  }

  //TODO remove unused method
  editEventSave() {
    const editTitle = this.formEditData.get('editTitle').value;
    const editId = this.calendarEvents.findIndex(x => x.id + '' === this.editEvent.id + '');
    // tslint:disable-next-line: radix
    this.calendarEvents[editId] = { ...this.editEvent, title: editTitle, id: parseInt(this.editEvent.id + '') };
    this.position('Appointment Edited');
    this.formEditData = this.formBuilder.group({
      editTitle: '',
    });
    this.modalService.dismissAll();
  }

  deleteEventData() {
    // const deleteId = this.editEvent.id;
    // const deleteEvent = this.calendarEvents.findIndex(x => x.id + '' === deleteId + '');
    // this.calendarEvents[deleteEvent] = { ...this.deleteEvent, id: '' };
    // delete this.calendarEvents[deleteEvent].id;
    this.deleteAppointment(this.editEvent.id);
  }

  deleteAppointment(id) {
    this.appointmentService.deleteAppointment(id).subscribe(res => {
      alert('Appointment deleted!');
      this.getAppointments();
      this.modalService.dismissAll();
    });
  }

  closeEventModal() {
    const title = this.formData.get('title').value;
    // tslint:disable-next-line: no-shadowed-variable
    const category = this.formData.get('category').value;
    this.formData = this.formBuilder.group({
      title: '',
      start_time: '',
    });
    this.modalService.dismissAll();
  }

  saveEvent() {
    if (this.formData.valid) {
      const title = this.formData.get('title').value;
      const start_time = this.formData.get('start_time').value;



      // tslint:disable-next-line: no-shadowed-variable

      let createObj = { 'team_id': title, 'start_time': start_time, 'comments': 'default' };
      this.appointmentService.createAppointment(createObj).subscribe(appointment => {
        this.getAppointments();

        this.position('Appointment has been created');

        this.formData = this.formBuilder.group({
          title: '',
          start_time: [''],
        });
        this.modalService.dismissAll();
      }, error => {
        alert(error.error.message);
        this.modalService.dismissAll();
      });
    }
    this.submitted = true;
  }

  private _fetchData() {
    // Event category
    //  this.category = category;
    // Calender Event Data
    this.calendarEvents = calendarEvents;
    // form submit
    this.submitted = false;
  }


  messagePopup(title, text, confirmText, icon) {
    Swal.fire({
      title: title,
      text: text,
      icon: icon,
      confirmButtonColor: '#34c38f',
      cancelButtonColor: '#f46a6a',
      confirmButtonText: confirmText,
    }).then(result => {
      if (result.value) {
        // this.modalService.dismissAll();
      }
    });
  }

}
