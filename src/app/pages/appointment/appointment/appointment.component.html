<app-loader></app-loader>
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="text-center my-3">

        <a (click)="openRandomModal(randomAppointment, $event)" class="btn btn-success inner mr-1">
          Random Appointment </a>
        <a (click)="openModal(content, $event)" class="btn btn-success inner">
          Add Appointment </a>
      </div>
    </div> <!-- end col-->
  </div>

  <app-page-title title="Appointments" [breadCrumbItems]="breadCrumbItems"></app-page-title>
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <div class="app-calendar">
            <!-- calendar -->
            <full-calendar #calendar
                           defaultView="timeGridDay"
                           [header]="{
                              left: 'prev,next today',
                              center: 'title',
                              right: 'timeGridDay,listWeek'
                            }"
                           [plugins]="calendarPlugins"
                           [eventResizableFromStart]="true" [defaultTimedEventDuration]="'00:10:00'"
                           [allDaySlot]="false"
                           [minTime]="'09:00'"
                           [maxTime]="'18:20'"
                           [eventLimit]="true"
                           themeSystem='bootstrap'
                           [weekends]="calendarWeekends" [bootstrapFontAwesome]="false"
                           [slotDuration]="'00:10:00'"
                           [slotLabelInterval]="'00:10:00'"
                           [nowIndicator]="true"
                           [slotLabelFormat]="{
                              minute:'numeric',
                              hour:'numeric',
                              day:'numeric',
                              month:'long'
                            }"
                            [locales]="options.locales"
                            [locale]="options.locale"
                           deepChangeDetection="true" [events]="calendarEvents" deepChangeDetection="true"
                           (dateClick)="openModal(content, $event)" (eventClick)="openEditModal(editcontent, $event)">
            </full-calendar>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Event Modal -->
<ng-template #content let-modal>
  <div class="modal-header">
    <h5 class="modal-title">Add New Appointment</h5>
    <button type="button" class="close" aria-hidden="true" (click)="closeModal()">×</button>
  </div>
  <div class="modal-body p-3">
    <form (ngSubmit)="saveEvent()" [formGroup]="formData">
      <div class="">
        <div class="form-group">
          <label class="control-label">Team Name</label>
          <ng-select 
            [items]="title" 
            bindLabel="team_name" 
            bindValue="id" 
            formControlName="title"
            [searchable]="true"
            [clearable]="true"
            placeholder="Takım arayın"
            notFoundText="No teams found"
            [loading]="false"
            [searchFn]="searchFn">
          </ng-select>
        </div>

        <div class="form-group">
          <label class="control-label">Start Date</label>
          <input class="form-control" type="datetime-local" formControlName="start_time">
        </div>

        <div class="form-group">
          <label class="control-label">Comments</label>
          <textarea class="form-control" formControlName="comments"></textarea>
        </div>

      </div>

      <div class="text-right pt-4">
        <button type="button" class="btn btn-light" (click)="closeModal()">Close</button>
        <button type="submit" class="btn btn-success save-event ml-1">Save</button>
      </div>
    </form>
  </div>
</ng-template>
<!-- Random Appointment Modal -->


<ng-template #randomAppointment let-modal>
  <div class="modal-header">
    <h5 class="modal-title">Random Appointment</h5>
    <button type="button" class="close" aria-hidden="true" (click)="closeModal()">×</button>
  </div>
  <div class="modal-body p-3">
    <form (ngSubmit)="makeRandomAppointment()" [formGroup]="randomAppointmentForm">
      <div class="row">
        <div class="col-12">

          <div class="form-group">
            <label class="control-label">Start Date</label>
            <div class="col-md-10">
              <input class="form-control" type="date" formControlName="start_time"
                     id="example-datetime-local-input">
            </div>
          </div>

        </div>
      </div>

      <div class="text-right pt-4">
        <button type="button" class="btn btn-light" (click)="closeModal()">Close</button>
        <button type="submit" class="btn btn-success save-event ml-1">Auto Generate</button>
      </div>
    </form>
  </div>
</ng-template>
<!-- Edit event modal -->
<ng-template #editcontent let-modal>
  <div class="modal-header">
    <h5 class="modal-title">Edit Event</h5>
    <button type="button" class="close" aria-hidden="true" (click)="closeModal()">×</button>
  </div>
  <div class="modal-body p-3">
    <form [formGroup]="formEditData">
      <label>Team name</label>
      <button type="button" class="btn btn-sm btn-info ml-1 mb-2 float-right"
              [routerLink]="['/team/detail/', selectedTeamId]"
              (click)="closeModal()">Go to Team Detail
      </button>
      <div class="input-group m-b-15 mb-2">
        <input class="form-control" type="text" formControlName="editTitle" disabled="disabled">
      </div>

      <div class="input-group m-b-15 mb-2">
        <textarea class="form-control" placeholder="comments" formControlName="comments"></textarea>
      </div>
      <label>Status</label>
      <div class="input-group m-b-15">
        <div class="custom-control custom-radio mr-2">
          <input type="radio" id="customRadio1"
                 name="status" class="custom-control-input" value="active"
                 formControlName="status">
          <label class="custom-control-label" for="customRadio1">ACTIVE</label>
        </div>
        <div class="custom-control custom-radio mr-2">
          <input type="radio" id="customRadio2" name="status" class="custom-control-input" value="joined"
                 formControlName="status">
          <label class="custom-control-label" for="customRadio2">JOINED</label>
        </div>
        <div class="custom-control custom-radio mr-2">
          <input type="radio" id="customRadio3" name="status" class="custom-control-input" value="not_joined"
                 formControlName="status">
          <label class="custom-control-label" for="customRadio3">NOT_JOINED</label>
        </div>
      </div>
    </form>
  </div>
  <div class="text-right p-3 d-flex flex-column">
    <button type="button" class="btn btn-success ml-1 mb-2" (click)="doneEvent()">Save</button>

    <button type="button" class="btn btn-success ml-1 mb-2" *ngIf="goToEval"
            [routerLink]="['/evaluation/', goToEval]"
            (click)="closeModal()">Continue to Evaluation
    </button>
    <button type="button" class="btn btn-danger delete-event ml-1 mb-2" (click)="confirm()">Delete</button>
    <button type="button" class="btn btn-light mb-2" (click)="closeModal()">Close</button>

    <!-- <button type="button" class="btn btn-success ml-1" (click)="editEventSave()">Save</button> -->
  </div>
</ng-template>

<ngx-spinner type="ball-clip-rotate-multiple" size="medium"> </ngx-spinner>

