import { Component, OnInit } from '@angular/core';
import { Form<PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

import listPlugin from '@fullcalendar/list';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import bootstrapPlugin from '@fullcalendar/bootstrap';

import { EventInput } from '@fullcalendar/core';
import { calendarEvents } from './data';
import Swal from 'sweetalert2';
import { LoaderService } from '../../../core/services/loader.service';

import { HttpClient } from '@angular/common/http';
import { TeamsService } from 'src/app/core/services/teams.service';
import { AppointmentService } from 'src/app/core/services/appointment.service';
import { EvaluationService } from 'src/app/core/services/evaluation.service';
import { EvaluationStatusEnum } from '../../evaluation/evaluation-status.enum';
import trLocale from '@fullcalendar/core/locales/tr';
import { NgxSpinnerService } from 'ngx-spinner';

@Component({
  selector: 'app-appointment',
  templateUrl: './appointment.component.html',
  styleUrls: ['./appointment.component.scss'],
})
export class AppointmentComponent implements OnInit {
  // bread crumb items
  breadCrumbItems: Array<{}>;
  options: any = {
    locales: [trLocale],
    locale: 'tr',
  }
  selectedTeamId;
  // event form
  formData: FormGroup;
  formEditData: FormGroup;
  randomAppointmentForm: FormGroup;


  // Form submition value
  submitted: boolean;

  // Form category data
  category = [];

  title = [];

  // Date added in event
  newEventDate: Date;

  // Edit event
  editEvent: EventInput;

  // Delete event
  deleteEvent: EventInput;

  calendarWeekends: any;
  // show events
  calendarEvents: EventInput[];

  filterForm: FormGroup;
  // calendar plugin
  calendarPlugins = [listPlugin, dayGridPlugin, bootstrapPlugin, timeGridPlugin, interactionPlugin];

  appointments = [];

  constructor(private evalService: EvaluationService, private appointmentService: AppointmentService,
    private teamsService: TeamsService, private modalService: NgbModal, private formBuilder: FormBuilder,
    public loader: LoaderService, public http: HttpClient, private spinner: NgxSpinnerService) {

    this.filterForm = this.formBuilder.group({
      limit: [100],
    });
  }

  ngOnInit() {
    this.breadCrumbItems = [{ label: 'Rastmobile' }, { label: 'Calendar', active: true }];
    this.getAppointments();
    this.getTeams();

    this.makeHttpCall();
    this.formData = this.formBuilder.group({
      title: ['', [Validators.required]],
      start_time: [''],
      comments: [''],
    });

    this.formEditData = this.formBuilder.group({
      editTitle: [],
      status: [],
      comment: [],
    });

    this.randomAppointmentForm = this.formBuilder.group({
      start_time: [],
    });

    this._fetchData();
  }

  ngAfterViewInit(): void {
    //Called after ngAfterContentInit when the component's view has been initialized. Applies to components only.
    //Add 'implements AfterViewInit' to the class.
   }

  closeModal() {
    this.modalService.dismissAll();
    this.goToEval = false;
  }


  teamId;
  goToEval = false;

  doneEvent() {
    const currentAppointment = this.appointments.find(appointment => appointment.id == this.editEvent.id);
    this.teamId = currentAppointment.team_id;

    // tslint:disable-next-line: radix
    const editObj = {
      status: this.formEditData.get('status').value,
      comments: this.formEditData.get('comments').value,
    };

    this.appointmentService.patchAppointment(parseInt(this.editEvent.id + ''), editObj).subscribe((data: any) => {
      this.getAppointments();

      this.goToEval = data.data.team?.current_evaluation?.id;

      switch (editObj.status) {
        case 'joined':
          if (!data.data.team?.current_evaluation) {
            // startEval and show Eval detail button
            this.startEvaluation();
          } else {
            this.messagePopup('Successfully updated', 'updated', 'OK', 'success');
          }
          break;
        case 'not_joined':
          const notJoined = (evaluation) => {
            this.rejectEvaluation(evaluation);
          };

          if (!data.data.team?.current_evaluation) {
            // startEval and show Eval detail button
            this.startEvaluation(notJoined);
          } else {
            notJoined(data.data.team.current_evaluation);
          }
          break;
        default:
          this.modalService.dismissAll();
          this.messagePopup('Successfully updated', 'updated', 'OK', 'success');
      }
    }, error => {
      this.showValidationError(error);
    });
  }

  startEvaluation(onSuccess = null) {
    const date = new Date();
    const evalObj = {
      team_id: parseInt(this.teamId),
      eval_date: date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate(),
    };
    this.spinner.show();
    this.evalService.startEvaluation(evalObj).subscribe((data: any) => {
      this.spinner.hide();
      this.messagePopup('Successfully changed', 'Eval Started', 'OK', 'success');

      this.goToEval = data.data.id;
      if (onSuccess) {
        onSuccess(data.data);
      }
    }, error => {
      this.spinner.hide();
      this.showValidationError(error);
    });
  }


  rejectEvaluation(evaluation: any) {
    console.log('update', evaluation);
    this.spinner.show();
    this.evalService.updateStatus({ status: EvaluationStatusEnum.FAILED }, evaluation.id)
      .subscribe((data: any) => {
        this.spinner.hide();
        this.messagePopup('Successfully changed', 'Eval Started', 'OK', 'success');
        this.goToEval = data.data.id;
      }, error => {
        this.spinner.hide();
        this.showValidationError(error);
      });
  }


  getTeams() {
    this.teamsService.getTeams(this.filterForm.value).subscribe(teams => {
      teams['data'].forEach(teamsData => {
        // console.log('teamsData');
        // console.log(teamsData);
        if (teamsData['completed'] === 1) {
          this.title.push({
            'id': teamsData['id'],
            'team_name': `${teamsData['university_name']} : ${teamsData['team_name']}`,
          });
        }
      });

    });
  }


  getAppointments() {
    this.appointmentService.getAppointments(this.filterForm.value).subscribe(appointment => {

      this.calendarEvents = [];
      this.appointments = appointment['data'];
      appointment['data'].forEach(data => {
        let dataStartTime = new Date().getTime();
        let current = new Date(data['start_time']).getTime() + 60000; // 10 minutes from now
        let className;
        if (data['status'] === 'not_joined') {
          className = 'bg-danger';
        } else if (data['status'] === 'joined') {
          className = 'bg-success';
        } else if (dataStartTime >= current) {
          className = 'bg-warning';
        } else {
          className = 'bg-primary';
        }
        this.calendarEvents.push(
          {
            id: data['id'],
            title: data['team']['team_name'],
            start: data['start_time'],
            className: className,
            rawData: data,
          });
      });
    });
  }
  searchFn = (term: string, item: any) => {
  return item.team_name.replace(/İ/g, 'i').toLowerCase().includes(term.toLowerCase());
};
  makeRandomAppointment() {
    console.log(this.randomAppointmentForm.value);
    this.spinner.show();
    this.appointmentService.randomAppointment(this.randomAppointmentForm.value)
      .subscribe((data: any) => {
        this.spinner.hide();
        this.getAppointments();
        const appointment = data.data;
        this.messagePopup('Random Appointment placed successfully',
          `${appointment.team_count} Appointment created in date
          between ${appointment.start_time} and ${appointment.end_time}`, 'OK', 'success');
        this.modalService.dismissAll();
      }, error => {
        this.spinner.hide();
        this.showValidationError(error);
      });
  }

  /**
   * Returns form
   */
  get form() {
    return this.formData.controls;
  }

  /**
   * Open Event Modal
   * @param content modal content
   * @param event calendar event
   */
  openModal(content: any, event: any) {
    this.formData.reset();
    this.newEventDate = event.date;
    this.modalService.open(content);
  }


  openRandomModal(randomAppointment: any, event: any) {
    this.randomAppointmentForm.reset();
    this.modalService.open(randomAppointment);
  }

  makeHttpCall() {
    this.http.get('https://jsonplaceholder.typicode.com/comments')
      .subscribe((r) => {
        this.loader.isLoading.next(false);
      });
  }

  /**
   * Open Delete Confirmation Modal
   */
  confirm() {
    Swal.fire({
      title: 'Are you sure?',
      text: 'You won\'t be able to revert this!',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#34c38f',
      cancelButtonColor: '#f46a6a',
      confirmButtonText: 'Yes, delete it!',
    }).then(result => {
      if (result.value) {
        this.deleteEventData();
        Swal.fire('Deleted!', 'Event has been deleted.', 'success');
      }
    });
  }

  /**
   * Show successfull Save Dialog
   */
  position(title) {
    Swal.fire({
      position: 'center',
      icon: 'success',
      title: title,
      showConfirmButton: false,
      timer: 1500,
    });
  }

  openEditModal(editcontent: any, event: any) {
    console.log('event', event.event);
    this.formEditData = this.formBuilder.group({
      editTitle: event.event.title,
      status: event.event.extendedProps.rawData.status,
      comments: event.event.extendedProps.rawData.comments,
    });
    console.log('formval', this.formEditData.value);
    const currentAppointment = this.appointments.find(appointment => appointment.id == event.event.id);

    this.goToEval = currentAppointment?.team.current_evaluation?.id;

    console.log('editTitle', event.event);

    // tslint:disable-next-line: max-line-length
    this.editEvent = {
      id: event.event.id,
      title: event.event.title,
      start: event.event.start,
      classNames: event.event.classNames[event.event.classNames.length - 1],
    };
    this.selectedTeamId = currentAppointment['team']['id'];
    this.modalService.open(editcontent);
  }

  //TODO remove unused method
  editEventSave() {
    const editTitle = this.formEditData.get('editTitle').value;
    const editId = this.calendarEvents.findIndex(x => x.id + '' === this.editEvent.id + '');
    // tslint:disable-next-line: radix
    this.calendarEvents[editId] = { ...this.editEvent, title: editTitle, id: parseInt(this.editEvent.id + '') };
    this.position('Appointment Edited');
    this.formEditData = this.formBuilder.group({
      editTitle: '',
    });
    this.modalService.dismissAll();
  }

  deleteEventData() {
    // const deleteId = this.editEvent.id;
    // const deleteEvent = this.calendarEvents.findIndex(x => x.id + '' === deleteId + '');
    // this.calendarEvents[deleteEvent] = { ...this.deleteEvent, id: '' };
    // delete this.calendarEvents[deleteEvent].id;
    this.deleteAppointment(this.editEvent.id);
  }

  deleteAppointment(id) {
    this.appointmentService.deleteAppointment(id).subscribe(res => {
      //alert('Appointment deleted!');
      this.getAppointments();
      this.modalService.dismissAll();
    });
  }

  closeEventModal() {
    const title = this.formData.get('title').value;
    // tslint:disable-next-line: no-shadowed-variable
    const category = this.formData.get('category').value;
    this.formData = this.formBuilder.group({
      title: '',
      start_time: '',
    });
    this.modalService.dismissAll();
  }

  saveEvent() {
    if (this.formData.valid) {
      const title = this.formData.get('title').value;
      const start_time = this.formData.get('start_time').value;
      const comments = this.formData.get('comments').value;

      let createObj = { 'team_id': title, start_time, comments };


      this.appointmentService.createAppointment(createObj).subscribe(appointment => {
        this.getAppointments();

        this.position('Appointment has been created');

        this.formData = this.formBuilder.group({
          title: '',
          start_time: [''],
          comments: '',
        });
        this.modalService.dismissAll();
      }, error => {
        this.showValidationError(error);
      });
    }
    this.submitted = true;
  }

  private _fetchData() {
    // Event category
    //  this.category = category;
    // Calender Event Data
    this.calendarEvents = calendarEvents;
    // form submit
    this.submitted = false;
  }


  messagePopup(title, text, confirmText, icon) {
    Swal.fire({
      title: title,
      text: text,
      icon: icon,
      confirmButtonColor: '#34c38f',
      cancelButtonColor: '#f46a6a',
      confirmButtonText: confirmText,
    }).then(result => {
      if (result.value) {
        this.modalService.dismissAll();
      }
    });
  }

  showValidationError(error) {
    let message = error.error.message;
    if (message === 'validation.error') {
      message = Object.values(error.error.errors).join('\n<br>');
    }

    this.messagePopup('Error', message, 'OK', 'error');
  }

}
