import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { EvaluationRoutingModule } from './evaluation-routing.module';
import { EvaluationComponent } from './evaluation/evaluation.component';
import { UIModule } from 'src/app/shared/ui/ui.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbDropdownModule, NgbTooltipModule, NgbNavModule, NgbProgressbarModule, NgbAccordionModule, NgbCollapseModule } from '@ng-bootstrap/ng-bootstrap';
import { WidgetModule } from 'src/app/shared/widget/widget.module';
import { NgApexchartsModule } from 'ng-apexcharts';
import { PerfectScrollbarModule } from 'ngx-perfect-scrollbar';
import { TeamEvaluationDetailComponent } from './team-evaluation-detail/team-evaluation-detail.component';
import { NgxDatatableModule } from '@swimlane/ngx-datatable';
import { FullCalendarModule } from '@fullcalendar/angular';
import { EvalGenericComponent } from './eval-generic/eval-generic.component';
import { EvaluationDetailComponent } from './evaluation-detail/evaluation-detail.component';
import { ButtonGroupDirective } from './directives/button-group.directive';
import { TableModule } from 'primeng/table';
import { InputTextModule } from 'primeng/inputtext';
import { NgxSpinnerModule } from 'ngx-spinner';
import { NgSelectModule } from '@ng-select/ng-select';

@NgModule({
  declarations: [EvaluationComponent, TeamEvaluationDetailComponent, EvalGenericComponent, EvaluationDetailComponent, ButtonGroupDirective],
  imports: [
    CommonModule,
    TableModule,
    InputTextModule,
    EvaluationRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    UIModule,
    NgbDropdownModule,
    NgbTooltipModule,
    NgbNavModule,
    WidgetModule,
    NgApexchartsModule,
    PerfectScrollbarModule,
    NgxDatatableModule,
    NgbProgressbarModule,
    NgbAccordionModule,
    NgbCollapseModule,
    FormsModule,
    ReactiveFormsModule,
    FullCalendarModule,
    NgxSpinnerModule,
    NgSelectModule
  ]
})
export class EvaluationModule { }
