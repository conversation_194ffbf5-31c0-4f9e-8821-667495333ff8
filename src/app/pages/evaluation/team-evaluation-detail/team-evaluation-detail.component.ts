import { AfterViewInit, Component, OnInit } from '@angular/core';

import { taskChart, tasks } from './data';

import { ChartType, Tasklist } from './list.model';
import { EvaluationService } from '../../../core/services/evaluation.service';
import { ActivatedRoute, Router } from '@angular/router';
import { FormArray, FormBuilder, FormGroup } from '@angular/forms';
import Swal from 'sweetalert2';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-team-evaluation-detail',
  templateUrl: './team-evaluation-detail.component.html',
  styleUrls: ['./team-evaluation-detail.component.scss'],
})
export class TeamEvaluationDetailComponent implements OnInit, AfterViewInit {
  activeId;
  criteriaStatus;

  showEval: boolean = false;

  // bread crumb items
  breadCrumbItems: Array<{}>;

  taskChart: ChartType;

  listed: boolean = false;
  evalResults;

  upcomingTasks: Tasklist[];
  teamId;
  evalId;
  evaluations;
  evalTeamDetails;
  conditionText;

  form = new FormGroup({
    criteria: this.fb.array([]),
  });

  get criteria(): FormArray { return this.form.get('criteria') as FormArray; }

  formHardware = new FormGroup({
    hardwareCriteria: this.fb.array([]),
  });

  get hardwareCriteria(): FormArray { return this.formHardware.get('hardwareCriteria') as FormArray; }

  formElectrical = new FormGroup({
    electricalCriteria: this.fb.array([]),
  });

  get electricalCriteria(): FormArray { return this.formElectrical.get('electricalCriteria') as FormArray; }

  formSafety = new FormGroup({
    safetyHardware: this.fb.array([]),
  });

  get safetyHardware(): FormArray { return this.formSafety.get('safetyHardware') as FormArray; }

  formBattery = new FormGroup({
    battery: this.fb.array([]),
  });

  get battery(): FormArray { return this.formBattery.get('battery') as FormArray; }

  formMotorDriver = new FormGroup({
    motorDriver: this.fb.array([]),
  });

  get motorDriver(): FormArray { return this.formMotorDriver.get('motorDriver') as FormArray; }

  formTelemetry = new FormGroup({
    telemetry: this.fb.array([]),
  });

  get telemetry(): FormArray { return this.formTelemetry.get('telemetry') as FormArray; }

  formSticker = new FormGroup({
    sticker: this.fb.array([]),
  });

  get sticker(): FormArray { return this.formSticker.get('sticker') as FormArray; }

  formTest = new FormGroup({
    test: this.fb.array([]),
  });

  get test(): FormArray { return this.formTest.get('test') as FormArray; }


  formHydromobile = new FormGroup({
    hydromobile: this.fb.array([]),
  });

  get hydromobile(): FormArray { return this.formHydromobile.get('hydromobile') as FormArray; }

  formDomestics = new FormGroup({
    domestics: this.fb.array([]),
  });

  get domestics(): FormArray { return this.formDomestics.get('domestics') as FormArray; }

  formAfterRace = new FormGroup({
    afterRace: this.fb.array([]),
  });

  get afterRace(): FormArray { return this.formAfterRace.get('afterRace') as FormArray; }


  constructor(private modalService: NgbModal, private router: Router, private fb: FormBuilder, private route: ActivatedRoute, private evaluationService: EvaluationService) { }

  ngOnInit() {
    this.breadCrumbItems = [{ label: 'Tasks' }, { label: 'Task List', active: true }];
    this.route.params.subscribe(params => {
      this.teamId = params['id'];
      this._fetchData();
      // this.getEvaluation();
      this.getEvaluations();
    });
  }

  getTeamInfo(teamInfo) {
    return teamInfo.team?.team_name + ' - ' + teamInfo.team?.university_name;
  }

  getEvaluations() {
    this.evalTeamDetails = [];
    this.evaluations = [];
    this.evalResults = [];

    this.evaluationService.getEvaluations(this.teamId).subscribe(evaluation => {

      this.evalTeamDetails = evaluation['data'];

      this.evaluations = evaluation['data']['details'];
      this.evalId = evaluation['data']['id'];

      evaluation['data']['details'].forEach(data => {

        if (data.category === 'Physical Specification') {
          this.addCriteria(data);
        } else if (data.category === 'Hardware') {
          this.addCriteriaForHardware(data);
        } else if (data.category === 'Electrical Safety') {
          this.addCriteriaForESafety(data);
        } else if (data.category === 'Safety Hardware') {
          this.addCriteriaSafety(data);
        } else if (data.category === 'Sticker') {
          this.addCriteriaSticker(data);
        } else if (data.category === 'Telemetry') {
          this.addTelemetryCriteria(data);
        } else if (data.category === 'Battery') {
          this.addBatteryCriteria(data);
        } else if (data.category === 'Test') {
          this.addTestCriteria(data);
        } else if (data.category === 'Motor Driver') {
          this.addMotorDriver(data);
        } else if (data.category === 'Hydromobile') {
          this.addHydromobileCriteria(data);
        } else if (data.category === 'Domestic Parts') {
          this.addDomesticCriteria(data);
        } else if (data.category === 'After Race Checks') {
          this.addAfterRaceCriteria(data);
        }

      });
      this.evalResults = this.evaluations.reduce(function(r, a) {

        r[a.category] = r[a.category] || [];
        r[a.category].push(a);
        return r;
      }, Object.create(null));

      this.listed = true;
    });
  }


  currentUser;

  checkAuth(type) {
    this.currentUser = JSON.parse(localStorage.getItem('currentUser'));

    for (let j = 0; j < this.currentUser["user"]["role"].length; j++) {
      if (this.currentUser['user']['role'][j].match('superadmin')) {
        return true;
      }
      if (this.currentUser['user']['role'][j].match(type)) {
        return true;
      }
    }
    return false;
  }

  onChange(event) {
    if (event.target.value === 'null') {
      this.criteriaStatus = null;
    } else {
      this.criteriaStatus = event.target.value;
    }
  }

  addMotorDriver(obj) {
    let control = <FormArray> this.formMotorDriver.controls.motorDriver;
    control.push(
      this.fb.group({
        category: [{ value: obj.category, disabled: true }],
        subject: [{ value: obj.subject, disabled: true }],
        condition: [{ value: obj.condition, disabled: true }],
        required: [obj.required],
        value: [obj.value],
        notes: [obj.notes],
        compatibility: [obj.compatibility],
        penalty: [obj.penalty],
        id: [obj.id],
      }),
    );
  }


  addDomesticCriteria(obj) {
    let control = <FormArray> this.formDomestics.controls.domestics;
    control.push(
      this.fb.group({
        category: [{ value: obj.category, disabled: true }],
        subject: [{ value: obj.subject, disabled: true }],
        condition: [{ value: obj.condition, disabled: true }],
        required: [obj.required],
        value: [obj.value],
        notes: [obj.notes],
        compatibility: [obj.compatibility],
        penalty: [obj.penalty],
        id: [obj.id],
      }),
    );
  }

  addAfterRaceCriteria(obj) {
    let control = <FormArray> this.formAfterRace.controls.afterRace;
    control.push(
      this.fb.group({
        category: [{ value: obj.category, disabled: true }],
        subject: [{ value: obj.subject, disabled: true }],
        condition: [{ value: obj.condition, disabled: true }],
        required: [obj.required],
        value: [obj.value],
        notes: [obj.notes],
        compatibility: [obj.compatibility],
        penalty: [obj.penalty],
        id: [obj.id],
      }),
    );
  }

  addHydromobileCriteria(obj) {
    let control = <FormArray> this.formHydromobile.controls.hydromobile;
    control.push(
      this.fb.group({
        category: [{ value: obj.category, disabled: true }],
        subject: [{ value: obj.subject, disabled: true }],
        condition: [{ value: obj.condition, disabled: true }],
        required: [obj.required],
        value: [obj.value],
        notes: [obj.notes],
        compatibility: [obj.compatibility],
        penalty: [obj.penalty],
        id: [obj.id],
      }),
    );
  }


  addTestCriteria(obj) {
    let control = <FormArray> this.formTest.controls.test;
    control.push(
      this.fb.group({
        category: [{ value: obj.category, disabled: true }],
        subject: [{ value: obj.subject, disabled: true }],
        condition: [{ value: obj.condition, disabled: true }],
        required: [obj.required],
        value: [obj.value],
        notes: [obj.notes],
        compatibility: [obj.compatibility],
        penalty: [obj.penalty],
        id: [obj.id],
      }),
    );
  }


  addBatteryCriteria(obj) {
    let control = <FormArray> this.formBattery.controls.battery;
    control.push(
      this.fb.group({
        category: [{ value: obj.category, disabled: true }],
        subject: [{ value: obj.subject, disabled: true }],
        condition: [{ value: obj.condition, disabled: true }],
        required: [obj.required],
        value: [obj.value],
        notes: [obj.notes],
        compatibility: [obj.compatibility],
        penalty: [obj.penalty],
        id: [obj.id],
      }),
    );
  }

  addTelemetryCriteria(obj) {
    let control = <FormArray> this.formTelemetry.controls.telemetry;
    control.push(
      this.fb.group({
        category: [{ value: obj.category, disabled: true }],
        subject: [{ value: obj.subject, disabled: true }],
        condition: [{ value: obj.condition, disabled: true }],
        required: [obj.required],
        value: [obj.value],
        notes: [obj.notes],
        compatibility: [obj.compatibility],
        penalty: [obj.penalty],
        id: [obj.id],
      }),
    );
  }

  addCriteriaSticker(obj) {
    let control = <FormArray> this.formSticker.controls.sticker;
    control.push(
      this.fb.group({
        category: [{ value: obj.category, disabled: true }],
        subject: [{ value: obj.subject, disabled: true }],
        condition: [{ value: obj.condition, disabled: true }],
        required: [obj.required],
        value: [obj.value],
        notes: [obj.notes],
        compatibility: [obj.compatibility],
        penalty: [obj.penalty],
        id: [obj.id],
      }),
    );
  }

  addCriteriaSafety(obj) {
    let control = <FormArray> this.formSafety.controls.safetyHardware;
    control.push(
      this.fb.group({
        category: [{ value: obj.category, disabled: true }],
        subject: [{ value: obj.subject, disabled: true }],
        condition: [{ value: obj.condition, disabled: true }],
        required: [obj.required],
        value: [obj.value],
        notes: [obj.notes],
        compatibility: [obj.compatibility],
        penalty: [obj.penalty],
        id: [obj.id],
      }),
    );
  }

  addCriteria(obj) {
    let control = <FormArray> this.form.controls.criteria;
    control.push(
      this.fb.group({
        category: [{ value: obj.category, disabled: true }],
        subject: [{ value: obj.subject, disabled: true }],
        condition: [{ value: obj.condition, disabled: true }],
        required: [obj.required],
        value: [obj.value],
        notes: [obj.notes],
        compatibility: [obj.compatibility],
        penalty: [obj.penalty],
        id: [obj.id],
      }),
    );
  }

  addCriteriaForHardware(obj) {
    let control = <FormArray> this.formHardware.controls.hardwareCriteria;
    control.push(
      this.fb.group({
        category: [{ value: obj.category, disabled: true }],
        subject: [{ value: obj.subject, disabled: true }],
        condition: [{ value: obj.condition, disabled: true }],
        required: [obj.required],
        value: [obj.value],
        notes: [obj.notes],
        compatibility: [obj.compatibility],
        penalty: [obj.penalty],
        id: [obj.id],
      }),
    );
  }

  addCriteriaForESafety(obj) {
    let control = <FormArray> this.formElectrical.controls.electricalCriteria;
    control.push(
      this.fb.group({
        category: [{ value: obj.category, disabled: true }],
        subject: [{ value: obj.subject, disabled: true }],
        condition: [{ value: obj.condition, disabled: true }],
        required: [obj.required],
        value: [obj.value],
        notes: [obj.notes],
        compatibility: [obj.compatibility],
        penalty: [obj.penalty],
        id: [obj.id],
      }),
    );
  }

  trackByFn(index, item) {
    return index; // or item.id
  }

  updateStatus(status) {
    let obj = { status: status };
    this.evaluationService.updateStatus(obj, this.teamId).subscribe(data => {
      this.router.navigate(['/evaluation']);
    });
  }

  checkSticker() {
    this.evaluationService.checkSticker(this.teamId).subscribe(data => {

      this.decideMessage(data as any);

      //  this.router.navigate(['/evaluation']);
      // open Popup
    });
  }


  openCondition(formName, attachmentModal: any, id) {

    if (formName === 'formMotorDriver') {
      this.conditionText = this.formMotorDriver.controls.motorDriver['controls'][id]['controls']['condition']['value'];
    } else if (formName === 'formTelemetry') {
      this.conditionText = this.formTelemetry.controls.telemetry['controls'][id]['controls']['condition']['value'];
    } else if (formName === 'formSticker') {
      this.conditionText = this.formSticker.controls.sticker['controls'][id]['controls']['condition']['value'];
    } else if (formName === 'formTest') {
      this.conditionText = this.formTest.controls.test['controls'][id]['controls']['condition']['value'];
    } else if (formName === 'formBattery') {
      this.conditionText = this.formBattery.controls.battery['controls'][id]['controls']['condition']['value'];
    } else if (formName === 'formSafety') {
      this.conditionText = this.formSafety.controls.safetyHardware['controls'][id]['controls']['condition']['value'];
    } else if (formName === 'formElectrical') {
      this.conditionText = this.formElectrical.controls.electricalCriteria['controls'][id]['controls']['condition']['value'];
    } else if (formName === 'formHardware') {
      this.conditionText = this.formHardware.controls.hardwareCriteria['controls'][id]['controls']['condition']['value'];
    } else if (formName === 'formHydromobile') {
      this.conditionText = this.formHydromobile.controls.hydromobile['controls'][id]['controls']['condition']['value'];
    } else if (formName === 'formDomestics') {
      this.conditionText = this.formDomestics.controls.domestics['controls'][id]['controls']['condition']['value'];
    } else if (formName === 'formAfterRace') {
      this.conditionText = this.formAfterRace.controls.afterRace['controls'][id]['controls']['condition']['value'];
    } else if (formName === 'form') {
      this.conditionText = this.form.controls.criteria['controls'][id]['controls']['condition']['value'];
    }
    this.form.controls.criteria['controls'][id]['controls'].r;
    this.modalService.open(attachmentModal, { centered: true, windowClass: 'modal-holder' });
  }


  stickerConfirm(title, text, confirmText, icon) {
    Swal.fire({
      title: title,
      text: text,
      icon: icon,
      showCancelButton: true,
      showConfirmButton: confirmText !== false,
      confirmButtonColor: '#34c38f',
      cancelButtonColor: '#f46a6a',
      confirmButtonText: confirmText,
    }).then(result => {
      if (result.value) {
        this.giveSticker();
      }
    });
  }

  rejectConfirm() {
    Swal.fire({
      title: 'Are you sure to reject?',
      text: 'It will reject this evaluation of the team.',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#34c38f',
      cancelButtonColor: '#f46a6a',
      confirmButtonText: 'Yes, reject.',
    }).then(result => {
      if (result.value) {
        this.closeEval();
      }
    });
  }


  saveInfoPopup(title, text, icon) {
    Swal.fire({
      title: title,
      text: text,
      icon: icon,
      confirmButtonColor: '#34c38f',
      cancelButtonColor: '#f46a6a',
      confirmButtonText: 'OK.',
    }).then(result => {
      this.closeModal();
    });
  }

  decideMessage(message: { passed: boolean, domestic: boolean, required: any[] }) {
    let title;
    let text;
    let confirmText;
    let icon;
    if (message.passed) {
      title = 'Team is ready to have STICKER!';
      text = 'All set. Team is ready to have sticker. Click Yes, confirm it to give sticker.';
      confirmText = 'Yes, confirm it!';
      icon = 'success';
      if (!message.domestic) {
        title = 'Domestic parts penalties prevent to have stickers';
        text = 'Domestic parts penalties can\'t be above 30';
        confirmText = false;
        icon = 'warning';
      }

    } else {
      title = 'Team is NOT ready to have STICKER!';
      text = `Has ${message.required.length} criteria to approve.`;
      confirmText = 'Confirm anyway';
      icon = 'warning';
    }

    this.stickerConfirm(title, text, confirmText, icon);
  }


  giveSticker() {
    this.evaluationService.giveSticker(this.teamId).subscribe(data => {

      //alert('Successfully set to Sticker Ready');
    });
  }

  ngAfterViewInit() {

  }


  checkCompatibility(id) {
    let selectedComp = this.form.controls.criteria['value'][id];
    return selectedComp['compatibility'];
  }

  updateCompatibility(id, type) {

    let selectedComp = this.form.controls.criteria['value'][id];
    // selectedComp.patchValue({compatibility : })
    selectedComp['compatibility'] = type;
  }

  _fetchData() {
    // all tasks
    this.upcomingTasks = tasks.filter(t => t.taskType === 'upcoming');
    this.taskChart = taskChart;
  }

  getEvals(evalResult) {
    return this.evaluations.filter(x => x.category === evalResult);
  }

  isItemButtonDisabled(id) {
    let findDisabled = this.disabledItems.filter(data => data.id === id);

  }


  exportEval() {
    this.evaluationService.exportEval(this.evalId).subscribe(data => {

      // alert('Exported');
      this.downLoadFile(data, 'application/ms-excel', this.evalTeamDetails.team.university_name
        + '-' + this.evalTeamDetails.team.team_name + '_evalaution.xlsx');
    });
  }

  downLoadFile(data: any, type: string, fileName: string) {
    let blob = new Blob([data], { type: type });

    let url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    link.click();

    // let pwa = window.open(url);
    // if (!pwa || pwa.closed || typeof pwa.closed == 'undefined') {
    //   alert('Please disable your Pop-up blocker and try again.');
    // }
  }

  disabledItems = [];

  saveEvalRow(formName, id) {
    let selectedComp;
    if (formName === 'formMotorDriver') {
      selectedComp = this.formMotorDriver.controls.motorDriver['value'][id];
    } else if (formName === 'formTelemetry') {
      selectedComp = this.formTelemetry.controls.telemetry['value'][id];
    } else if (formName === 'formSticker') {
      selectedComp = this.formSticker.controls.sticker['value'][id];
    } else if (formName === 'formTest') {
      selectedComp = this.formTest.controls.test['value'][id];
    } else if (formName === 'formBattery') {
      selectedComp = this.formBattery.controls.battery['value'][id];
    } else if (formName === 'formSafety') {
      selectedComp = this.formSafety.controls.safetyHardware['value'][id];
    } else if (formName === 'formElectrical') {
      selectedComp = this.formElectrical.controls.electricalCriteria['value'][id];
    } else if (formName === 'formHardware') {
      selectedComp = this.formHardware.controls.hardwareCriteria['value'][id];
    } else if (formName === 'formHydromobile') {
      selectedComp = this.formHydromobile.controls.hydromobile['value'][id];
    } else if (formName === 'formDomestics') {
      selectedComp = this.formDomestics.controls.domestics['value'][id];
    } else if (formName === 'form') {
      selectedComp = this.form.controls.criteria['value'][id];
      this.disabledItems.push(id);
    }
    let obj = {
      'value': selectedComp['value'],
      'compatibility': selectedComp['compatibility'],
      'notes': selectedComp['notes'],
      'penalty': selectedComp['penalty'],
      'id': selectedComp['id'],
    };

    if (obj['compatibility'] === 'null') {
      obj['compatibility'] = null;
    }
    this.evaluationService.saveEval(obj).subscribe((data: any) => {
      if (data?.data?.evaluation) {
        Object.assign(this.evalTeamDetails, data.data.evaluation);
        this.saveInfoPopup('Success', 'Item successfully saved.', 'success');
      }
    }, error => {
      this.saveInfoPopup('Error', 'Item can\'t be saved.', 'warning');
    });
  }

  closeEval() {
    this.evaluationService.closeEval(this.teamId).subscribe(data => {
      //alert('Evaluation closed successfully');
      this.router.navigate(['/evaluation']);
    });
  }

  closeModal() {
    this.modalService.dismissAll();
  }

  openEval() {
    this.evaluationService.openEval(this.teamId).subscribe(data => {
      //alert('Evaluation opened successfully');
      location.reload();
    });
  }

  saveAllRows() {
    let changedProperties = [];
    let keyValue;
    let arrayName;
    let formName;
    if (this.activeId === 6) {
      keyValue = Object.entries(this.formMotorDriver.controls).filter(value => value[1].dirty);
      arrayName = 'motorDriver';
      formName = 'formMotorDriver';
    } else if (this.activeId === 7) {
      keyValue = Object.entries(this.formTelemetry.controls).filter(value => value[1].dirty);
      arrayName = 'telemetry';
      formName = 'formTelemetry';
    } else if (this.activeId === 8) {
      keyValue = Object.entries(this.formSticker.controls).filter(value => value[1].dirty);
      arrayName = 'sticker';
      formName = 'formSticker';
    } else if (this.activeId === 9) {
      keyValue = Object.entries(this.formTest.controls).filter(value => value[1].dirty);
      arrayName = 'test';
      formName = 'formTest';
    } else if (this.activeId === 5) {
      keyValue = Object.entries(this.formBattery.controls).filter(value => value[1].dirty);
      arrayName = 'battery';
      formName = 'formBattery';
    } else if (this.activeId === 4) {
      keyValue = Object.entries(this.formSafety.controls).filter(value => value[1].dirty);
      arrayName = 'safetyHardware';
      formName = 'formSafety';
    } else if (this.activeId === 3) {
      keyValue = Object.entries(this.formElectrical.controls).filter(value => value[1].dirty);
      arrayName = 'electricalCriteria';
      formName = 'formElectrical';
    } else if (this.activeId === 2) {
      keyValue = Object.entries(this.formHardware.controls).filter(value => value[1].dirty);
      arrayName = 'hardwareCriteria';
      formName = 'formHardware';
    } else if (this.activeId === 10) {
      keyValue = Object.entries(this.formHydromobile.controls).filter(value => value[1].dirty);
      arrayName = 'hydromobile';
      formName = 'formHydromobile';
    } else if (this.activeId === 11) {
      keyValue = Object.entries(this.formDomestics.controls).filter(value => value[1].dirty);
      arrayName = 'domestics';
      formName = 'formDomestics';
    } else if (this.activeId === 1) {
      keyValue = Object.entries(this.form.controls).filter(value => value[1].dirty);
      arrayName = 'criteria';
      formName = 'form';
    }

    let dirties = keyValue.map(value => value[1]);
    let criteria = keyValue.reduce((a, v) => Object.assign(a, { [v[0]]: v[1] }), {});
    if (dirties.length > 0) {
      criteria[arrayName]['controls'].forEach((data, index) => {
        if (data['touched']) {
          this.saveEvalRow(formName, index);
        }
      });
    } else {
      //alert('You did not change any prop!');
    }

  }

}
