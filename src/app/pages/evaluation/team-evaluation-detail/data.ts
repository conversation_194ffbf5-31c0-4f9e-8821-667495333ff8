import { ChartType } from './list.model';

const taskChart: ChartType = {
    chart: {
        height: 280,
        type: 'line',
        stacked: false,
        toolbar: {
            show: false,
        }
    },
    stroke: {
        width: [0, 2, 5],
        curve: 'smooth'
    },
    plotOptions: {
        bar: {
            columnWidth: '20%',
            endingShape: 'rounded'
        }
    },
    colors: ['#556ee6', '#34c38f'],
    series: [{
        name: 'Complete Tasks',
        type: 'column',
        data: [23, 11, 22, 27, 13, 22, 52, 21, 44, 22, 30]
    },
    {
        name: 'All Tasks',
        type: 'line',
        data: [23, 11, 34, 27, 17, 22, 62, 32, 44, 22, 39]
    }],
    fill: {
        gradient: {
            inverseColors: false,
            shade: 'light',
            type: 'vertical',
            opacityFrom: 0.85,
            opacityTo: 0.55,
            stops: [0, 100, 100, 100]
        }
    },
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov'],
    markers: {
        size: 0
    },
    yaxis: {
        min: 0
    },
};

const tasks = [
    {
        index: 1,
        taskType: 'upcoming',
        name: 'Vehicle height',
        images: ['assets/images/users/avatar-2.jpg', 'assets/images/users/avatar-1.jpg'],
        status: 'Waiting',
        checked: false
    },
    {
        index: 2,
        taskType: 'upcoming',
        name: 'Vehicle dynamic testing',
        images: ['assets/images/users/avatar-4.jpg', 'assets/images/users/avatar-5.jpg'],
        status: 'Approved',
        checked: true
    },
    {
        index: 3,
        taskType: 'upcoming',
        name: 'Speedometer',
        images: ['assets/images/users/avatar-1.jpg'],
        status: 'Waiting',
        checked: false
    },
    {
        index: 3,
        taskType: 'upcoming',
        name: 'Minimum height',
        images: ['assets/images/users/avatar-1.jpg'],
        status: 'Waiting',
        checked: true
    },
    {
        index: 3,
        taskType: 'upcoming',
        name: 'Vehicle measurements',
        images: ['assets/images/users/avatar-1.jpg'],
        status: 'Waiting',
        checked: true
    },
    {
        index: 3,
        taskType: 'upcoming',
        name: 'Cockpit (for driver and passenger)',
        images: ['assets/images/users/avatar-1.jpg'],
        status: 'Waiting',
        checked: false
    },
    {
        index: 3,
        taskType: 'upcoming',
        name: 'Vehicle body',
        images: ['assets/images/users/avatar-1.jpg'],
        status: 'Waiting',
        checked: true
    },
    {
        index: 3,
        taskType: 'upcoming',
        name: 'Door',
        images: ['assets/images/users/avatar-1.jpg'],
        status: 'Waiting',
        checked: false
    },
    {
        index: 3,
        taskType: 'upcoming',
        name: 'Door mechanism',
        images: ['assets/images/users/avatar-1.jpg'],
        status: 'Waiting',
        checked: false
    },
    {
        index: 3,
        taskType: 'upcoming',
        name: 'Wheel width',
        images: ['assets/images/users/avatar-1.jpg'],
        status: 'Waiting',
        checked: false
    },
    {
        index: 3,
        taskType: 'upcoming',
        name: 'Flag',
        images: ['assets/images/users/avatar-1.jpg'],
        status: 'Waiting',
        checked: false
    },
    {
        index: 3,
        taskType: 'upcoming',
        name: 'Windscreen',
        images: ['assets/images/users/avatar-1.jpg'],
        status: 'Waiting',
        checked: false
    },
    {
        index: 3,
        taskType: 'upcoming',
        name: 'Wiper',
        images: ['assets/images/users/avatar-1.jpg'],
        status: 'Waiting',
        checked: false
    },
    {
        index: 3,
        taskType: 'upcoming',
        name: 'Rearview mirrors',
        images: ['assets/images/users/avatar-1.jpg'],
        status: 'Waiting',
        checked: false
    },
    {
        index: 3,
        taskType: 'upcoming',
        name: 'Wheel width',
        images: ['assets/images/users/avatar-1.jpg'],
        status: 'Waiting',
        checked: false
    },
    {
        index: 3,
        taskType: 'upcoming',
        name: 'Horn',
        images: ['assets/images/users/avatar-1.jpg'],
        status: 'Waiting',
        checked: false
    },
    {
        index: 3,
        taskType: 'upcoming',
        name: 'Headlights',
        images: ['assets/images/users/avatar-1.jpg'],
        status: 'Waiting',
        checked: false
    },
    {
        index: 1,
        taskType: 'upcoming',
        name: 'Vehicle height',
        images: ['assets/images/users/avatar-2.jpg', 'assets/images/users/avatar-1.jpg'],
        status: 'Waiting',
        checked: false
    },
    {
        index: 2,
        taskType: 'upcoming',
        name: 'Vehicle dynamic testing',
        images: ['assets/images/users/avatar-4.jpg', 'assets/images/users/avatar-5.jpg'],
        status: 'Approved',
        checked: true
    },
    {
        index: 3,
        taskType: 'upcoming',
        name: 'Speedometer',
        images: ['assets/images/users/avatar-1.jpg'],
        status: 'Waiting',
        checked: false
    },
    {
        index: 3,
        taskType: 'upcoming',
        name: 'Minimum height',
        images: ['assets/images/users/avatar-1.jpg'],
        status: 'Waiting',
        checked: false
    },
    {
        index: 3,
        taskType: 'upcoming',
        name: 'Vehicle measurements',
        images: ['assets/images/users/avatar-1.jpg'],
        status: 'Waiting',
        checked: false
    },
    {
        index: 3,
        taskType: 'upcoming',
        name: 'Cockpit (for driver and passenger)',
        images: ['assets/images/users/avatar-1.jpg'],
        status: 'Waiting',
        checked: false
    },
    {
        index: 3,
        taskType: 'upcoming',
        name: 'Vehicle body',
        images: ['assets/images/users/avatar-1.jpg'],
        status: 'Waiting',
        checked: false
    },
    {
        index: 3,
        taskType: 'upcoming',
        name: 'Door',
        images: ['assets/images/users/avatar-1.jpg'],
        status: 'Waiting',
        checked: false
    },
    {
        index: 3,
        taskType: 'upcoming',
        name: 'Door mechanism',
        images: ['assets/images/users/avatar-1.jpg'],
        status: 'Waiting',
        checked: false
    },
    {
        index: 3,
        taskType: 'upcoming',
        name: 'Wheel width',
        images: ['assets/images/users/avatar-1.jpg'],
        status: 'Waiting',
        checked: false
    },
    {
        index: 3,
        taskType: 'upcoming',
        name: 'Flag',
        images: ['assets/images/users/avatar-1.jpg'],
        status: 'Waiting',
        checked: false
    },
    {
        index: 3,
        taskType: 'upcoming',
        name: 'Windscreen',
        images: ['assets/images/users/avatar-1.jpg'],
        status: 'Waiting',
        checked: false
    },
    {
        index: 3,
        taskType: 'upcoming',
        name: 'Wiper',
        images: ['assets/images/users/avatar-1.jpg'],
        status: 'Waiting',
        checked: false
    },
    {
        index: 3,
        taskType: 'upcoming',
        name: 'Rearview mirrors',
        images: ['assets/images/users/avatar-1.jpg'],
        status: 'Waiting',
        checked: false
    },
    {
        index: 3,
        taskType: 'upcoming',
        name: 'Wheel width',
        images: ['assets/images/users/avatar-1.jpg'],
        status: 'Waiting',
        checked: false
    },
    {
        index: 3,
        taskType: 'upcoming',
        name: 'Horn',
        images: ['assets/images/users/avatar-1.jpg'],
        status: 'Waiting',
        checked: false
    },
    {
        index: 3,
        taskType: 'upcoming',
        name: 'Headlights',
        images: ['assets/images/users/avatar-1.jpg'],
        status: 'Waiting',
        checked: false
    }
    
];

export { taskChart, tasks };
