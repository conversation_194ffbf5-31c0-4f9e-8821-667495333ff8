<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="text-center my-3">
        <p>
          This is the <strong>{{ evalTeamDetails.eval_day }}</strong> evaluation of this team.
          Progress is <strong>{{evalTeamDetails.progress}}/100</strong>
          <ngb-progressbar [value]="evalTeamDetails.progress" [striped]="true" [animated]="true" type="success">
          </ngb-progressbar>
        </p>

      </div>
    </div> <!-- end col-->
  </div>

  <a target="_blank" href="javascript: void(0);"
    [routerLink]="['/team/detail/', evalTeamDetails.team_id]">Click to see Team details. (Opened via new tab)</a>
  <!-- start page title -->
  <app-page-title [title]="getTeamInfo(evalTeamDetails)" [breadCrumbItems]="breadCrumbItems"></app-page-title>
  <!-- end page title -->

  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          <ul ngbNav #justifiednav="ngbNav" [(activeId)]="activeId" class="nav-pills nav-justified">
            <li [ngbNavItem]="1" *ngIf="checkAuth('DDK_Physical_Specification')">
              <a ngbNavLink>
                <span class="d-block d-sm-none"><i class="fas fa-home"></i></span>
                <span class="d-none d-sm-block">Physical Specification</span>
              </a>
              <ng-template ngbNavContent>
                <ng-template [ngTemplateOutlet]="PhysicalSpecification"></ng-template>
              </ng-template>
            </li>
            <li [ngbNavItem]="2" *ngIf="checkAuth('DDK_Hardware')">
              <a ngbNavLink>
                <span class="d-block d-sm-none"><i class="far fa-user"></i></span>
                <span class="d-none d-sm-block">Hardware</span>
              </a>
              <ng-template ngbNavContent>
                <ng-template [ngTemplateOutlet]="Hardware"></ng-template>
              </ng-template>
            </li>
            <li [ngbNavItem]="3" *ngIf="checkAuth('DDK_Electrical_Safety')">
              <a ngbNavLink>
                <span class="d-block d-sm-none"><i class="far fa-envelope"></i></span>
                <span class="d-none d-sm-block">Electrical Safety</span>
              </a>
              <ng-template ngbNavContent>
                <ng-template [ngTemplateOutlet]="ElectricalSafety"></ng-template>
              </ng-template>
            </li>
            <li [ngbNavItem]="4" *ngIf="checkAuth('DDK_Safety_Hardware')">
              <a ngbNavLink>
                <span class="d-block d-sm-none"><i class="far fa-address-card"></i></span>
                <span class="d-none d-sm-block">Safety Hardware</span>
              </a>
              <ng-template ngbNavContent>
                <ng-template [ngTemplateOutlet]="SafetyHardware"></ng-template>
              </ng-template>
            </li>
            <li [ngbNavItem]="5" *ngIf="checkAuth('DDK_Battery')">
              <a ngbNavLink>
                <span class="d-block d-sm-none"><i class="far fa-arrow-alt-circle-down"></i></span>
                <span class="d-none d-sm-block">Battery</span>
              </a>
              <ng-template ngbNavContent>
                <ng-template [ngTemplateOutlet]="Battery"></ng-template>
              </ng-template>
            </li>
            <li [ngbNavItem]="6" *ngIf="checkAuth('DDK_Motor_Driver')">
              <a ngbNavLink>
                <span class="d-block d-sm-none"><i class="far fa-bell"></i></span>
                <span class="d-none d-sm-block">Motor Driver</span>
              </a>
              <ng-template ngbNavContent>
                <ng-template [ngTemplateOutlet]="MotorDriver"></ng-template>
              </ng-template>
            </li>
            <li [ngbNavItem]="7" *ngIf="checkAuth('DDK_Telemetry')">
              <a ngbNavLink>
                <span class="d-block d-sm-none"><i class="far fa-copyright"></i></span>
                <span class="d-none d-sm-block">Telemetry</span>
              </a>
              <ng-template ngbNavContent>
                <ng-template [ngTemplateOutlet]="Telemetry"></ng-template>
              </ng-template>
            </li>
            <li [ngbNavItem]="8" *ngIf="checkAuth('DDK_Sticker')">
              <a ngbNavLink>
                <span class="d-block d-sm-none"><i class="far fa-check-circle"></i></span>
                <span class="d-none d-sm-block">Sticker</span>
              </a>
              <ng-template ngbNavContent>
                <ng-template [ngTemplateOutlet]="Sticker"></ng-template>
              </ng-template>
            </li>
            <li [ngbNavItem]="9" *ngIf="checkAuth('DDK_Test')">
              <a ngbNavLink>
                <span class="d-block d-sm-none"><i class="far fa-clipboard"></i></span>
                <span class="d-none d-sm-block">Test</span>
              </a>
              <ng-template ngbNavContent>
                <ng-template [ngTemplateOutlet]="Test"></ng-template>
              </ng-template>
            </li>
            <li [ngbNavItem]="10" *ngIf="checkAuth('DDK_Hydromobile')">
              <a ngbNavLink>
                <span class="d-block d-sm-none"><i class="far fa-clipboard"></i></span>
                <span class="d-none d-sm-block">Hydromobile</span>
              </a>
              <ng-template ngbNavContent>
                <ng-template [ngTemplateOutlet]="HYDROMOBILE"></ng-template>
              </ng-template>
            </li>
            <li [ngbNavItem]="11" *ngIf="checkAuth('DDK_Domestic_Parts')">
              <a ngbNavLink>
                <span class="d-block d-sm-none"><i class="far fa-clipboard"></i></span>
                <span class="d-none d-sm-block">Domestic Parts</span>
              </a>
              <ng-template ngbNavContent>
                <ng-template [ngTemplateOutlet]="DomesticParts"></ng-template>
              </ng-template>
            </li>
            <li [ngbNavItem]="12" *ngIf="checkAuth('DDK_After_Race_Checks') && evalTeamDetails.after_race_started">
              <a ngbNavLink>
                <span class="d-block d-sm-none"><i class="far fa-clipboard"></i></span>
                <span class="d-none d-sm-block">After Race Checks</span>
              </a>
              <ng-template ngbNavContent>
                <ng-template [ngTemplateOutlet]="AfterRaceChecks"></ng-template>
              </ng-template>
            </li>

          </ul>
          <div [ngbNavOutlet]="justifiednav"></div>
        </div>
      </div>






    </div>
    <!-- end col -->

  </div>
  <!-- end row -->

  <div class="row">
    <div class="col-12">
      <div class="text-center my-3">
        <button type="button" class="btn btn-primary mr-1" (click)="saveAllRows()">Save All</button>
        <button type="submit" *ngIf="evalTeamDetails.status === 'active'" class="btn btn-secondary mr-1"
          (click)="rejectConfirm()">Reject</button>
        <button type="submit" *ngIf="evalTeamDetails.status === 'closed'" class="btn btn-secondary mr-1"
          (click)="openEval()">Open Evaluation</button>
        <button *ngIf="evalTeamDetails.eval_day === 6" type="submit" class="btn btn-secondary mr-1"
          (click)="updateStatus('failed')">Permanently Failed</button>
        <button type="button" class="btn btn-success mr-1" (click)="checkSticker()">
          <i class="bx bx-check-double font-size-16 align-middle mr-2"></i> Sticker Ready
        </button>

        <button type="submit" class="btn btn-success mr-1" (click)="exportEval()">Export Evaluation</button>
      </div>
    </div> <!-- end col-->
  </div>
</div> <!-- container-fluid -->

<ng-template #conditionModal let-modal>
  <div class="modal-header">
    <h5 class="modal-title mt-0">{{conditionText}}</h5>
    <button type="button" class="close" (click)="modal.dismiss('Cross click')" aria-hidden="true">×</button>
  </div>
</ng-template>

<!-- PhysicalSpecification -->
<ng-template #PhysicalSpecification>
  <div class="card" *ngIf="listed">
    <div *ngIf="evalTeamDetails.status === 'closed'" class="layer">
    </div>
    <form [formGroup]="form">
      <ng-container formArrayName="criteria">
        <div class="card-body">
          <h4 class="card-title mb-4">Physical Specification</h4>
          <div class="table-responsive">

            <table class="table table-nowrap table-centered mb-0">
              <thead>

                <th>Subject</th>
                <th>Note</th>
                <th>Comp.</th>
                <th>Value</th>
                <th>Penalty</th>
                <th>Save</th>
              </thead>
              <tbody>
                <ng-container *ngFor="let list of criteria?.controls; index as i; trackBy: trackByFn">
                  <tr>
                    <ng-container [formGroupName]="i">
                      <td>
                        <h5 class="text-truncate font-size-14 m-0" ngbTooltip="Subject Information" placement="top">
                          <input type="text" style="border: 0px solid; background: white;" class="text-dark"
                            formControlName="subject"></h5>
                        <h5 class="text-truncate font-size-10 m-0" (click)="openCondition('form', conditionModal, i)">
                          <textarea style="border: 0px solid; background: white;" class="text-dark"
                            formControlName="condition"></textarea></h5>
                      </td>
                      <td>
                        <div class="col-md-9">
                          <textarea style="background: white; width: 100px; border: 1px solid #f1f1f1" class="text-dark"
                            formControlName="notes" placeholder="Note"></textarea>
                        </div>
                      </td>

                      <td>

                        <select formControlName="compatibility" style="width:50px;" ngbTooltip="Compatibility"
                          placement="top" (change)="onChange($event)">
                          <option value="null">Default</option>
                          <option value="1">OK</option>
                          <option value="0">NOK</option>
                        </select>
                      </td>
                      <td>
                        <div class="col-md-9">
                          <input class="form-control" name="score" style="width:60px;" type="text" ngbTooltip="Value"
                            placement="top" formControlName="value" placeholder="Val">
                        </div>
                      </td>
                      <td>
                        <div class="col-md-9">
                          <input class="form-control" name="penalty" formControlName="penalty" style="width:50px;"
                            ngbTooltip="Penalty" placement="top" type="text" placeholder="Ceza ">
                        </div>
                      </td>


                      <td>
                        <div class="col-md-9">
                          <button type="button" class="btn btn-success" (click)="saveEvalRow('form',i)"
                            ngbTooltip="Save" placement="top">SAVE</button>
                        </div>

                      </td>
                    </ng-container>
                  </tr>
                </ng-container>
              </tbody>
            </table>
          </div>
        </div>
      </ng-container>
    </form>
  </div>
</ng-template>

<!-- Hardware -->
<ng-template #Hardware>
  <div class="card" *ngIf="listed">
    <div *ngIf="evalTeamDetails.status === 'closed'" class="layer">
    </div>
    <form [formGroup]="formHardware">
      <ng-container formArrayName="hardwareCriteria">
        <div class="card-body">
          <h4 class="card-title mb-4">Hardware</h4>
          <div class="table-responsive">

            <table class="table table-nowrap table-centered mb-0">
              <thead>

                <th>Subject</th>
                <th>Note</th>
                <th>Comp.</th>
                <th>Value</th>
                <th>Penalty</th>
                <th>Save</th>
              </thead>
              <tbody>
                <ng-container *ngFor="let list of hardwareCriteria?.controls; index as i; trackBy: trackByFn">
                  <tr>
                    <ng-container [formGroupName]="i">

                      <td>
                        <h5 class="text-truncate font-size-14 m-0" ngbTooltip="Subject Information" placement="top">
                          <input type="text" style="border: 0px solid; background: white;" class="text-dark"
                            formControlName="subject"></h5>
                        <h5 class="text-truncate font-size-10 m-0"
                          (click)="openCondition('formHardware', conditionModal, i)"><textarea
                            style="border: 0px solid; background: white;" class="text-dark"
                            formControlName="condition"></textarea></h5>
                      </td>
                      <td>
                        <div class="col-md-9">
                          <textarea style="background: white; width: 100px; border: 1px solid #f1f1f1" class="text-dark"
                            formControlName="notes" placeholder="Note"></textarea>
                        </div>
                      </td>

                      <td>
                        <select formControlName="compatibility" style="width:50px;" ngbTooltip="Compatibility"
                          placement="top" (change)="onChange($event)">
                          <option value="null">Default</option>
                          <option value="1">OK</option>
                          <option value="0">NOK</option>
                        </select>
                      </td>

                      <td>
                        <div class="col-md-9">
                          <input class="form-control" name="score" style="width:60px;" type="text" ngbTooltip="Value"
                            placement="top" formControlName="value" placeholder="Val">
                        </div>
                      </td>
                      <td>
                        <div class="col-md-9">
                          <input class="form-control" name="penalty" formControlName="penalty" style="width:50px;"
                            ngbTooltip="Penalty" placement="top" type="text" placeholder="Ceza ">
                        </div>
                      </td>


                      <td>
                        <div class="col-md-9">
                          <button type="button" class="btn btn-success" (click)="saveEvalRow('formHardware',i)"
                            ngbTooltip="Save" placement="top">SAVE</button>
                        </div>

                      </td>
                    </ng-container>
                  </tr>
                </ng-container>
              </tbody>
            </table>
          </div>
        </div>
      </ng-container>
    </form>
  </div>
</ng-template>
<!-- ElectricalSafety -->
<ng-template #ElectricalSafety>

  <div class="card" *ngIf="listed">
    <div *ngIf="evalTeamDetails.status === 'closed'" class="layer">
    </div>
    <form [formGroup]="formElectrical">
      <ng-container formArrayName="electricalCriteria">
        <div class="card-body">
          <h4 class="card-title mb-4">Electrical Safety</h4>
          <div class="table-responsive">

            <table class="table table-nowrap table-centered mb-0">
              <thead>

                <th>Subject</th>
                <th>Note</th>
                <th>Comp.</th>
                <th>Value</th>
                <th>Penalty</th>
                <th>Save</th>
              </thead>
              <tbody>
                <ng-container *ngFor="let list of electricalCriteria?.controls; index as i; trackBy: trackByFn">
                  <tr>
                    <ng-container [formGroupName]="i">


                      <td>
                        <h5 class="text-truncate font-size-14 m-0" ngbTooltip="Subject Information" placement="top">
                          <input type="text" style="border: 0px solid; background: white;" class="text-dark"
                            formControlName="subject"></h5>
                        <h5 class="text-truncate font-size-10 m-0"
                          (click)="openCondition('formElectrical',conditionModal, i)"><textarea
                            style="border: 0px solid; background: white;" class="text-dark"
                            formControlName="condition"></textarea></h5>
                      </td>

                      <td>
                        <div class="col-md-9">
                          <textarea style="background: white; width: 100px; border: 1px solid #f1f1f1" class="text-dark"
                            formControlName="notes" placeholder="Note"></textarea>
                        </div>
                      </td>
                      <td>
                        <select formControlName="compatibility" style="width:50px;" ngbTooltip="Compatibility"
                          placement="top" (change)="onChange($event)">
                          <option value="null">Default</option>
                          <option value="1">OK</option>
                          <option value="0">NOK</option>
                        </select>
                      </td>

                      <td>
                        <div class="col-md-9">
                          <input class="form-control" name="score" style="width:60px;" type="text" ngbTooltip="Value"
                            placement="top" formControlName="value" placeholder="Val">
                        </div>
                      </td>
                      <td>
                        <div class="col-md-9">
                          <input class="form-control" name="penalty" formControlName="penalty" style="width:50px;"
                            ngbTooltip="Penalty" placement="top" type="text" placeholder="Ceza ">
                        </div>
                      </td>


                      <td>
                        <div class="col-md-9">
                          <button type="button" class="btn btn-success" (click)="saveEvalRow('formElectrical',i)"
                            ngbTooltip="Save" placement="top">SAVE</button>
                        </div>

                      </td>
                    </ng-container>
                  </tr>
                </ng-container>
              </tbody>
            </table>
          </div>
        </div>
      </ng-container>
    </form>
  </div>
</ng-template>
<!-- SafetyHardware -->
<ng-template #SafetyHardware>

  <div class="card" *ngIf="listed">
    <div *ngIf="evalTeamDetails.status === 'closed'" class="layer">
    </div>
    <form [formGroup]="formSafety">
      <ng-container formArrayName="safetyHardware">
        <div class="card-body">
          <h4 class="card-title mb-4">Safety Hardware</h4>
          <div class="table-responsive">

            <table class="table table-nowrap table-centered mb-0">
              <thead>

                <th>Subject</th>
                <th>Note</th>
                <th>Comp.</th>
                <th>Value</th>
                <th>Penalty</th>
                <th>Save</th>
              </thead>
              <tbody>
                <ng-container *ngFor="let list of safetyHardware?.controls; index as i; trackBy: trackByFn">
                  <tr>
                    <ng-container [formGroupName]="i">


                      <td>
                        <h5 class="text-truncate font-size-14 m-0" ngbTooltip="Subject Information" placement="top">
                          <input type="text" style="border: 0px solid; background: white;" class="text-dark"
                            formControlName="subject"></h5>
                        <h5 class="text-truncate font-size-10 m-0"
                          (click)="openCondition('formSafety',conditionModal, i)"><textarea
                            style="border: 0px solid; background: white;" class="text-dark"
                            formControlName="condition"></textarea></h5>
                      </td>

                      <td>
                        <div class="col-md-9">
                          <textarea style="background: white; width: 100px; border: 1px solid #f1f1f1" class="text-dark"
                            formControlName="notes" placeholder="Note"></textarea>
                        </div>
                      </td>

                      <td>
                        <select formControlName="compatibility" style="width:50px;" ngbTooltip="Compatibility"
                          placement="top" (change)="onChange($event)">
                          <option value="null">Default</option>
                          <option value="1">OK</option>
                          <option value="0">NOK</option>
                        </select>
                      </td>


                      <td>
                        <div class="col-md-9">
                          <input class="form-control" name="score" style="width:60px;" type="text" ngbTooltip="Value"
                            placement="top" formControlName="value" placeholder="Val">
                        </div>
                      </td>
                      <td>
                        <div class="col-md-9">
                          <input class="form-control" name="penalty" formControlName="penalty" style="width:50px;"
                            ngbTooltip="Penalty" placement="top" type="text" placeholder="Ceza ">
                        </div>
                      </td>


                      <td>
                        <div class="col-md-9">
                          <button type="button" class="btn btn-success" (click)="saveEvalRow('formSafety',i)"
                            ngbTooltip="Save" placement="top">SAVE</button>
                        </div>

                      </td>
                    </ng-container>
                  </tr>
                </ng-container>
              </tbody>
            </table>
          </div>
        </div>
      </ng-container>
    </form>
  </div>
</ng-template>
<!-- Battery -->
<ng-template #Battery>

  <div class="card" *ngIf="listed">
    <div *ngIf="evalTeamDetails.status === 'closed'" class="layer">
    </div>
    <form [formGroup]="formBattery">
      <ng-container formArrayName="battery">
        <div class="card-body">
          <h4 class="card-title mb-4">Battery</h4>
          <div class="table-responsive">

            <table class="table table-nowrap table-centered mb-0">
              <thead>

                <th>Subject</th>
                <th>Note</th>
                <th>Comp.</th>
                <th>Value</th>
                <th>Penalty</th>
                <th>Save</th>
              </thead>
              <tbody>
                <ng-container *ngFor="let list of battery?.controls; index as i; trackBy: trackByFn">
                  <tr>
                    <ng-container [formGroupName]="i">


                      <td>
                        <h5 class="text-truncate font-size-14 m-0" ngbTooltip="Subject Information" placement="top">
                          <input type="text" style="border: 0px solid; background: white;" class="text-dark"
                            formControlName="subject"></h5>
                        <h5 class="text-truncate font-size-10 m-0"
                          (click)="openCondition('formBattery', conditionModal, i)"><textarea
                            style="border: 0px solid; background: white;" class="text-dark"
                            formControlName="condition"></textarea></h5>
                      </td>
                      <td>
                        <div class="col-md-9">
                          <textarea style="background: white; width: 100px; border: 1px solid #f1f1f1" class="text-dark"
                            formControlName="notes" placeholder="Note"></textarea>
                        </div>
                      </td>

                      <td>
                        <select formControlName="compatibility" style="width:50px;" ngbTooltip="Compatibility"
                          placement="top" (change)="onChange($event)">
                          <option value="null">Default</option>
                          <option value="1">OK</option>
                          <option value="0">NOK</option>
                        </select>
                      </td>


                      <td>
                        <div class="col-md-9">
                          <input class="form-control" name="score" style="width:60px;" type="text" ngbTooltip="Value"
                            placement="top" formControlName="value" placeholder="Val">
                        </div>
                      </td>
                      <td>
                        <div class="col-md-9">
                          <input class="form-control" name="penalty" formControlName="penalty" style="width:50px;"
                            ngbTooltip="Penalty" placement="top" type="text" placeholder="Ceza ">
                        </div>
                      </td>


                      <td>
                        <div class="col-md-9">
                          <button type="button" class="btn btn-success" (click)="saveEvalRow('formBattery',i)"
                            ngbTooltip="Save" placement="top">SAVE</button>
                        </div>

                      </td>
                    </ng-container>
                  </tr>
                </ng-container>
              </tbody>
            </table>
          </div>
        </div>
      </ng-container>
    </form>
  </div>
</ng-template>
<!-- MotorDriver -->
<ng-template #MotorDriver>

  <div class="card" *ngIf="listed">
    <div *ngIf="evalTeamDetails.status === 'closed'" class="layer">
    </div>
    <form [formGroup]="formMotorDriver">
      <ng-container formArrayName="motorDriver">
        <div class="card-body">
          <h4 class="card-title mb-4">Motor Driver</h4>
          <div class="table-responsive">

            <table class="table table-nowrap table-centered mb-0">
              <thead>

                <th>Subject</th>
                <th>Note</th>
                <th>Comp.</th>
                <th>Value</th>
                <th>Penalty</th>
                <th>Save</th>
              </thead>
              <tbody>
                <ng-container *ngFor="let list of motorDriver?.controls; index as i; trackBy: trackByFn">
                  <tr>
                    <ng-container [formGroupName]="i">


                      <td>
                        <h5 class="text-truncate font-size-14 m-0" ngbTooltip="Subject Information" placement="top">
                          <input type="text" style="border: 0px solid; background: white;" class="text-dark"
                            formControlName="subject"></h5>
                        <h5 class="text-truncate font-size-10 m-0"
                          (click)="openCondition('formMotorDriver',conditionModal, i)"><textarea
                            style="border: 0px solid; background: white;" class="text-dark"
                            formControlName="condition"></textarea></h5>
                      </td>
                      <td>
                        <div class="col-md-9">
                          <textarea style="background: white; width: 100px; border: 1px solid #f1f1f1" class="text-dark"
                            formControlName="notes" placeholder="Note"></textarea>
                        </div>
                      </td>

                      <td>
                        <select formControlName="compatibility" style="width:50px;" ngbTooltip="Compatibility"
                          placement="top" (change)="onChange($event)">
                          <option value="null">Default</option>
                          <option value="1">OK</option>
                          <option value="0">NOK</option>
                        </select>
                      </td>


                      <td>
                        <div class="col-md-9">
                          <input class="form-control" name="score" style="width:60px;" type="text" ngbTooltip="Value"
                            placement="top" formControlName="value" placeholder="Val">
                        </div>
                      </td>
                      <td>
                        <div class="col-md-9">
                          <input class="form-control" name="penalty" formControlName="penalty" style="width:50px;"
                            ngbTooltip="Penalty" placement="top" type="text" placeholder="Ceza ">
                        </div>
                      </td>

                      <td>
                        <div class="col-md-9">
                          <button type="button" class="btn btn-success" (click)="saveEvalRow('formMotorDriver',i)"
                            ngbTooltip="Save" placement="top">SAVE</button>
                        </div>

                      </td>
                    </ng-container>
                  </tr>
                </ng-container>
              </tbody>
            </table>
          </div>
        </div>
      </ng-container>
    </form>
  </div>
</ng-template>
<!-- Telemetry -->
<ng-template #Telemetry>

  <div class="card" *ngIf="listed">
    <div *ngIf="evalTeamDetails.status === 'closed'" class="layer">
    </div>
    <form [formGroup]="formTelemetry">
      <ng-container formArrayName="telemetry">
        <div class="card-body">
          <h4 class="card-title mb-4">Telemetry</h4>
          <div class="table-responsive">

            <table class="table table-nowrap table-centered mb-0">
              <thead>

                <th>Subject</th>
                <th>Note</th>
                <th>Comp.</th>
                <th>Value</th>
                <th>Penalty</th>
                <th>Save</th>
              </thead>
              <tbody>
                <ng-container *ngFor="let list of telemetry?.controls; index as i; trackBy: trackByFn">
                  <tr>
                    <ng-container [formGroupName]="i">


                      <td>
                        <h5 class="text-truncate font-size-14 m-0" ngbTooltip="Subject Information" placement="top">
                          <input type="text" style="border: 0px solid; background: white;" class="text-dark"
                            formControlName="subject"></h5>
                        <h5 class="text-truncate font-size-10 m-0"
                          (click)="openCondition('formTelemetry', conditionModal, i)"><textarea
                            style="border: 0px solid; background: white;" class="text-dark"
                            formControlName="condition"></textarea></h5>
                      </td>
                      <td>
                        <div class="col-md-9">
                          <textarea style="background: white; width: 100px; border: 1px solid #f1f1f1" class="text-dark"
                            formControlName="notes" placeholder="Note"></textarea>
                        </div>
                      </td>

                      <td>
                        <select formControlName="compatibility" style="width:50px;" ngbTooltip="Compatibility"
                          placement="top" (change)="onChange($event)">
                          <option value="null">Default</option>
                          <option value="1">OK</option>
                          <option value="0">NOK</option>
                        </select>
                      </td>
                      <td>
                        <div class="col-md-9">
                          <input class="form-control" name="score" style="width:60px;" type="text" ngbTooltip="Value"
                            placement="top" formControlName="value" placeholder="Val">
                        </div>
                      </td>
                      <td>
                        <div class="col-md-9">
                          <input class="form-control" name="penalty" formControlName="penalty" style="width:50px;"
                            ngbTooltip="Penalty" placement="top" type="text" placeholder="Ceza ">
                        </div>
                      </td>


                      <td>
                        <div class="col-md-9">
                          <button type="button" class="btn btn-success" (click)="saveEvalRow('formTelemetry',i)"
                            ngbTooltip="Save" placement="top">SAVE</button>
                        </div>

                      </td>
                    </ng-container>
                  </tr>
                </ng-container>
              </tbody>
            </table>
          </div>
        </div>
      </ng-container>
    </form>
  </div>
</ng-template>
<!-- Sticker -->
<ng-template #Sticker>

  <div class="card" *ngIf="listed">
    <div *ngIf="evalTeamDetails.status === 'closed'" class="layer">
    </div>
    <form [formGroup]="formSticker">
      <ng-container formArrayName="sticker">
        <div class="card-body">
          <h4 class="card-title mb-4">Sticker</h4>
          <div class="table-responsive">

            <table class="table table-nowrap table-centered mb-0">
              <thead>

                <th>Subject</th>
                <th>Note</th>
                <th>Comp.</th>
                <th>Value</th>
                <th>Penalty</th>
                <th>Save</th>
              </thead>
              <tbody>
                <ng-container *ngFor="let list of sticker?.controls; index as i; trackBy: trackByFn">
                  <tr>
                    <ng-container [formGroupName]="i">


                      <td>
                        <h5 class="text-truncate font-size-14 m-0" ngbTooltip="Subject Information" placement="top">
                          <input type="text" style="border: 0px solid; background: white;" class="text-dark"
                            formControlName="subject"></h5>
                        <h5 class="text-truncate font-size-10 m-0"
                          (click)="openCondition('formSticker', conditionModal, i)"><textarea
                            style="border: 0px solid; background: white;" class="text-dark"
                            formControlName="condition"></textarea></h5>
                      </td>
                      <td>
                        <div class="col-md-9">
                          <textarea style="background: white; width: 100px; border: 1px solid #f1f1f1" class="text-dark"
                            formControlName="notes" placeholder="Note"></textarea>
                        </div>
                      </td>

                      <td>
                        <select formControlName="compatibility" style="width:50px;" ngbTooltip="Compatibility"
                          placement="top" (change)="onChange($event)">
                          <option value="null">Default</option>
                          <option value="1">OK</option>
                          <option value="0">NOK</option>
                        </select>
                      </td>

                      <td>
                        <div class="col-md-9">
                          <input class="form-control" name="score" style="width:60px;" type="text" ngbTooltip="Value"
                            placement="top" formControlName="value" placeholder="Val">
                        </div>
                      </td>
                      <td>
                        <div class="col-md-9">
                          <input class="form-control" name="penalty" formControlName="penalty" style="width:50px;"
                            ngbTooltip="Penalty" placement="top" type="text" placeholder="Ceza ">
                        </div>
                      </td>

                      <td>
                        <div class="col-md-9">
                          <button type="button" class="btn btn-success" (click)="saveEvalRow('formSticker',i)"
                            ngbTooltip="Save" placement="top">SAVE</button>
                        </div>

                      </td>
                    </ng-container>
                  </tr>
                </ng-container>
              </tbody>
            </table>
          </div>
        </div>
      </ng-container>
    </form>
  </div>
</ng-template>
<!-- Test -->
<ng-template #Test>

  <div class="card" *ngIf="listed">
    <div *ngIf="evalTeamDetails.status === 'closed'" class="layer">
    </div>
    <form [formGroup]="formTest">
      <ng-container formArrayName="test">
        <div class="card-body">
          <h4 class="card-title mb-4">Test</h4>
          <div class="table-responsive">

            <table class="table table-nowrap table-centered mb-0">
              <thead>

                <th>Subject</th>
                <th>Note</th>
                <th>Comp.</th>
                <th>Value</th>
                <th>Penalty</th>
                <th>Save</th>
              </thead>
              <tbody>
                <ng-container *ngFor="let list of test?.controls; index as i; trackBy: trackByFn">
                  <tr>
                    <ng-container [formGroupName]="i">


                      <td>
                        <h5 class="text-truncate font-size-14 m-0" ngbTooltip="Subject Information" placement="top">
                          <input type="text" style="border: 0px solid; background: white;" class="text-dark"
                            formControlName="subject"></h5>
                        <h5 class="text-truncate font-size-10 m-0"
                          (click)="openCondition('formTest', conditionModal, i)"><textarea
                            style="border: 0px solid; background: white;" class="text-dark"
                            formControlName="condition"></textarea></h5>
                      </td>
                      <td>
                        <div class="col-md-9">
                          <textarea style="background: white; width: 100px; border: 1px solid #f1f1f1" class="text-dark"
                            formControlName="notes" placeholder="Note"></textarea>
                        </div>
                      </td>


                      <td>
                        <select formControlName="compatibility" style="width:50px;" ngbTooltip="Compatibility"
                          placement="top" (change)="onChange($event)">
                          <option value="null">Default</option>
                          <option value="1">OK</option>
                          <option value="0">NOK</option>
                        </select>
                      </td>

                      <td>
                        <div class="col-md-9">
                          <input class="form-control" name="score" style="width:60px;" type="text" ngbTooltip="Value"
                            placement="top" formControlName="value" placeholder="Val">
                        </div>
                      </td>
                      <td>
                        <div class="col-md-9">
                          <input class="form-control" name="penalty" formControlName="penalty" style="width:50px;"
                            ngbTooltip="Penalty" placement="top" type="text" placeholder="Ceza ">
                        </div>
                      </td>


                      <td>
                        <div class="col-md-9">
                          <button type="button" class="btn btn-success" (click)="saveEvalRow('formTest',i)"
                            ngbTooltip="Save" placement="top">SAVE</button>
                        </div>

                      </td>
                    </ng-container>
                  </tr>
                </ng-container>
              </tbody>
            </table>
          </div>
        </div>
      </ng-container>
    </form>
  </div>
</ng-template>


<!-- HydroMobile -->
<ng-template #HYDROMOBILE>
  <div class="card" *ngIf="listed">
    <div *ngIf="evalTeamDetails.status === 'closed'" class="layer">
    </div>
    <form [formGroup]="formHydromobile">
      <ng-container formArrayName="hydromobile">
        <div class="card-body">
          <h4 class="card-title mb-4">HYDRO MOBILE</h4>
          <div class="table-responsive">

            <table class="table table-nowrap table-centered mb-0">
              <thead>

                <th>Subject</th>
                <th>Note</th>
                <th>Comp.</th>
                <th>Value</th>
                <th>Penalty</th>
                <th>Save</th>
              </thead>
              <tbody>
                <ng-container *ngFor="let list of hydromobile?.controls; index as i; trackBy: trackByFn">
                  <tr>
                    <ng-container [formGroupName]="i">

                      <td>
                        <h5 class="text-truncate font-size-14 m-0" ngbTooltip="Subject Information" placement="top">
                          <input type="text" style="border: 0px solid; background: white;" class="text-dark"
                            formControlName="subject"></h5>
                        <h5 class="text-truncate font-size-10 m-0"
                          (click)="openCondition('formHydromobile',conditionModal, i)"><textarea
                            style="border: 0px solid; background: white;" class="text-dark"
                            formControlName="condition"></textarea></h5>
                      </td>
                      <td>
                        <div class="col-md-9">
                          <textarea style="background: white; width: 100px; border: 1px solid #f1f1f1" class="text-dark"
                            formControlName="notes" placeholder="Note"></textarea>
                        </div>
                      </td>

                      <td>
                        <select formControlName="compatibility" style="width:50px;" ngbTooltip="Compatibility"
                          placement="top" (change)="onChange($event)">
                          <option value="null">Default</option>
                          <option value="1">OK</option>
                          <option value="0">NOK</option>
                        </select>
                      </td>

                      <td>
                        <div class="col-md-9">
                          <input class="form-control" name="score" style="width:60px;" type="text" ngbTooltip="Value"
                            placement="top" formControlName="value" placeholder="Val">
                        </div>
                      </td>
                      <td>
                        <div class="col-md-9">
                          <input class="form-control" name="penalty" formControlName="penalty" style="width:50px;"
                            ngbTooltip="Penalty" placement="top" type="text" placeholder="Ceza ">
                        </div>


                      <td>
                        <div class="col-md-9">
                          <button type="button" class="btn btn-success" (click)="saveEvalRow('formHydromobile',i)"
                            ngbTooltip="Save" placement="top">SAVE</button>
                        </div>

                      </td>
                    </ng-container>
                  </tr>
                </ng-container>
              </tbody>
            </table>
          </div>
        </div>
      </ng-container>
    </form>
  </div>
</ng-template>


<!-- DDK_Domestic_Parts -->

<ng-template #DomesticParts>
  <div class="card" *ngIf="listed">
    <div *ngIf="evalTeamDetails.status === 'closed'" class="layer">
    </div>
    <form [formGroup]="formDomestics">
      <ng-container formArrayName="domestics">
        <div class="card-body">
          <h4 class="card-title mb-4">Domestic Parts</h4>
          <div class="table-responsive">

            <table class="table table-nowrap table-centered mb-0">
              <thead>

                <th>Subject</th>
                <th>Note</th>
                <th>Comp.</th>
                <th>Value</th>
                <th>Penalty</th>
                <th>Save</th>
              </thead>
              <tbody>
                <ng-container *ngFor="let list of domestics?.controls; index as i; trackBy: trackByFn">
                  <tr>
                    <ng-container [formGroupName]="i">

                      <td>
                        <h5 class="text-truncate font-size-14 m-0" ngbTooltip="Subject Information" placement="top">
                          <input type="text" style="border: 0px solid; background: white;" class="text-dark"
                            formControlName="subject"></h5>
                        <h5 class="text-truncate font-size-10 m-0"
                          (click)="openCondition('formDomestics',conditionModal, i)"><textarea
                            style="border: 0px solid; background: white;" class="text-dark"
                            formControlName="condition"></textarea></h5>
                      </td>
                      <td>
                        <div class="col-md-9">
                          <textarea style="background: white; width: 100px; border: 1px solid #f1f1f1" class="text-dark"
                            formControlName="notes" placeholder="Note"></textarea>
                        </div>
                      </td>


                      <td>
                        <select formControlName="compatibility" style="width:50px;" ngbTooltip="Compatibility"
                          placement="top" (change)="onChange($event)">
                          <option value="null">Default</option>
                          <option value="1">OK</option>
                          <option value="0">NOK</option>
                        </select>
                      </td>


                      <td>
                        <div class="col-md-9">
                          <input class="form-control" name="score" style="width:60px;" type="text" ngbTooltip="Value"
                            placement="top" formControlName="value" placeholder="Val">
                        </div>
                      </td>
                      <td>
                        <div class="col-md-9">
                          <input class="form-control" name="penalty" formControlName="penalty" style="width:50px;"
                            ngbTooltip="Penalty" placement="top" type="text" placeholder="Ceza ">
                        </div>
                      </td>


                      <td>
                        <div class="col-md-9">
                          <button type="button" class="btn btn-success" (click)="saveEvalRow('formDomestics',i)"
                            ngbTooltip="Save" placement="top">SAVE</button>
                        </div>

                      </td>
                    </ng-container>
                  </tr>
                </ng-container>
              </tbody>
            </table>
          </div>
        </div>
      </ng-container>
    </form>
  </div>
</ng-template>

<!-- After Race Checks -->

<ng-template #AfterRaceChecks>
  <div class="card" *ngIf="listed">
    <div *ngIf="evalTeamDetails.status === 'closed'" class="layer">
    </div>
    <form [formGroup]="formAfterRace">
      <ng-container formArrayName="afterRace">
        <div class="card-body">
          <h4 class="card-title mb-4">After Race Checks</h4>
          <div class="table-responsive">

            <table class="table table-nowrap table-centered mb-0">
              <thead>

                <th>Subject</th>
                <th>Note</th>
                <th>Comp.</th>
                <th>Value</th>
                <th>Penalty</th>
                <th>Save</th>
              </thead>
              <tbody>
                <ng-container *ngFor="let list of afterRace?.controls; index as i; trackBy: trackByFn">
                  <tr>
                    <ng-container [formGroupName]="i">

                      <td>
                        <h5 class="text-truncate font-size-14 m-0" ngbTooltip="Subject Information" placement="top">
                          <input type="text" style="border: 0px solid; background: white;" class="text-dark"
                            formControlName="subject"></h5>
                        <h5 class="text-truncate font-size-10 m-0"
                          (click)="openCondition('formAfterRace',conditionModal, i)"><textarea
                            style="border: 0px solid; background: white;" class="text-dark"
                            formControlName="condition"></textarea></h5>
                      </td>
                      <td>
                        <div class="col-md-9">
                          <textarea style="background: white; width: 100px; border: 1px solid #f1f1f1" class="text-dark"
                            formControlName="notes" placeholder="Note"></textarea>
                        </div>
                      </td>


                      <td>
                        <select formControlName="compatibility" style="width:50px;" ngbTooltip="Compatibility"
                          placement="top" (change)="onChange($event)">
                          <option value="null">Default</option>
                          <option value="1">OK</option>
                          <option value="0">NOK</option>
                        </select>
                      </td>


                      <td>
                        <div class="col-md-9">
                          <input class="form-control" name="score" style="width:60px;" type="text" ngbTooltip="Value"
                            placement="top" formControlName="value" placeholder="Val">
                        </div>
                      </td>
                      <td>
                        <div class="col-md-9">
                          <input class="form-control" name="penalty" formControlName="penalty" style="width:50px;"
                            ngbTooltip="Penalty" placement="top" type="text" placeholder="Ceza ">
                        </div>
                      </td>


                      <td>
                        <div class="col-md-9">
                          <button type="button" class="btn btn-success" (click)="saveEvalRow('formAfterRace',i)"
                            ngbTooltip="Save" placement="top">SAVE</button>
                        </div>

                      </td>
                    </ng-container>
                  </tr>
                </ng-container>
              </tbody>
            </table>
          </div>
        </div>
      </ng-container>
    </form>
  </div>
</ng-template>
