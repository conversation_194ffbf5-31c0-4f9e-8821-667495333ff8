import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanDeactivate, RouterStateSnapshot, UrlTree } from '@angular/router';
import { Observable } from 'rxjs';
import { EvaluationDetailComponent } from '../evaluation-detail/evaluation-detail.component';

@Injectable({
  providedIn: 'root'
})
export class FormCanDeactivateGuardService implements CanDeactivate<EvaluationDetailComponent> {

  canDeactivate(component: EvaluationDetailComponent): boolean | UrlTree | Observable<boolean | UrlTree> | Promise<boolean | UrlTree> {
      if(component.evalForm?.dirty){
        return confirm('Are you sure you want to discard your changes?');
      }
      return true;
  }
}
