import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { TeamsService } from 'src/app/core/services/teams.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { EvaluationService } from 'src/app/core/services/evaluation.service';
import { ChangeDetectorRef } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Router } from '@angular/router';
import { fromEvent } from 'rxjs';
import { debounceTime, map } from 'rxjs/operators';
import { EvaluationStatusEnum } from '../evaluation-status.enum';

@Component({
  selector: 'app-evaluation',
  templateUrl: './evaluation.component.html',
  styleUrls: ['./evaluation.component.scss'],
})
export class EvaluationComponent implements OnInit, AfterViewInit {
  @ViewChild('search', { static: false }) search: any;
  editing = {};
  teamId;
  formData: FormGroup;
  rows = [];
  orginalEvals = [];
  columns = [];
  loadingIndicator = true;
  reorderable = true;
  breadCrumbItems: Array<{}>;
  filterForm: FormGroup;
  temp = [];
  title = [];
  showType: boolean;
  listed = false;
  ddkList: any[] = [];
  filteredDdkList: any[] = [];
  ddkSearchText: string = '';

  constructor(private router: Router,
              private modalService: NgbModal,
              private teamService: TeamsService,
              private evalService: EvaluationService,
              private formBuilder: FormBuilder,
              private cdr :ChangeDetectorRef
  ) {
    this.filterForm = this.formBuilder.group({
      limit: [300],
    });
    setTimeout(() => { this.loadingIndicator = false; }, 5000);

    this.formData = this.formBuilder.group({
      title: ['', [Validators.required]],
    });

    this.fetch((data) => {
      // this.rows = data;
    });
  }

  ngOnInit() {
    this.breadCrumbItems = [{ label: 'Teams' }, { label: 'Team List', active: true }];

    this.columns = [
      // { prop: 'id' },
      { prop: 'vehicle_number', name: 'Vehicle Number' },
      { prop: 'org_team_id', name: 'Team Id' },
      { prop: 'team_name', name: 'Team Name' },
      { prop: 'university_name', name: 'University Name' },
      { prop: 'vehicle_name', name: 'Vehicle Name' },
      { prop: 'status', name: 'Status' },
      { prop: 'progress' },
      { prop: 'eval_day', name: 'Evaluation Day' },
      { prop: 'vehicle_category', name: 'Vehicle Category' },
      { prop: 'team_leader', name: 'Team Captain' }
    ];
    this.showType = localStorage.getItem('filterOnEvaluation') !== 'false' ? true : false;
    this.getEvals();
  }

  ngAfterViewInit(): void {
    // Called after ngAfterContentInit when the component's view has been initialized. Applies to components only.
    // Add 'implements AfterViewInit' to the class.

    fromEvent(this.search.nativeElement, 'keydown')
      .pipe(
        debounceTime(550),
        map(x => x['target']['value']),
      )
      .subscribe(value => {
        this.updateFilter(value);
      });
  }

  updateFilter(val: any) {
    this.showType = false;
    const filterValLower = val.toLocaleLowerCase().trim();
    const filterValOrj = val;
    // get the amount of columns in the table
    const count = this.columns.length;
    // get the key names of each column in the dataset
    const keys = Object.keys(this.temp[0]);
    // assign filtered matches to the active datatable
    this.rows = this.temp.filter(item => {
      // iterate through each row's column data
      for (let i = 0; i < count; i++) {
        // check for a match
        if ((!filterValOrj || (!!item[keys[i]] && item[keys[i]].toString().indexOf(filterValOrj) !== -1)) || (!filterValLower || (!!item[keys[i]] && item[keys[i]].toString().toLocaleLowerCase().indexOf(filterValLower) !== -1))) {

          // found match, return true to add to result set
          return true;
        }
      }
    });


    // Whenever the filter changes, always go back to the first page
    // this.table.offset = 0;
  }

  getEvals() {
    this.evalService.getEvalList(this.filterForm.value).subscribe((evalList: any) => {
      evalList.data.forEach(data => {
        this.temp.push({
          progress: data.progress,
          status: data.status,
          team_name: data.team.team_name,
          university_name: data.team.university_name,
          vehicle_name: data.team.vehicle_name,
          team_leader: data.team.team_leader,
          vehicle_category: data.team.vehicle_category,
          vehicle_number: data.team.vehicle_number,
          team_id: data.team.id,
          eval_day: data.eval_day,
          id: data.id,
          org_team_id: data?.team?.team_id
        });
        this.rows = this.temp;
        this.orginalEvals = this.temp;
        this.filterOnEvaluation(this.showType);
      });
      this.listed = !this.listed;
    });
  }

  getTeams() {
    this.teamService.getTeams(this.filterForm.value).subscribe((teams: any) => {
      teams.data.forEach(teamsData => {

        if (teamsData.completed === 1 && !this.temp.some(_eval => _eval.team_id === teamsData.id)) {
          this.title.push({
            id: teamsData.id,
            team_name: `${teamsData.university_name} : ${teamsData.team_name} - ${teamsData.vehicle_category} `,
          });
        }
      });
      this.cdr.detectChanges();
    });
  }

  startEvaluation() {
    const date = new Date();
    const evalObj = {
      team_id: this.formData.get('title').value,
      eval_date: date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate(),
    };
    this.evalService.startEvaluation(evalObj).subscribe((data: any) => {
      this.modalService.dismissAll();
      this.router.navigate(['/evaluation/' + data.data.id]);
    });
  }

  searchFn = (term: string, item: any) => {
  return item.team_name.replace(/İ/g, 'i').toLowerCase().includes(term.toLowerCase());
};
  /**
   * Open Event Modal
   * @param content modal content
   * @param event calendar event
   */
  openModal(content: any) {
    this.modalService.open(content);
    if(this.title.length == 0)
      this.getTeams();
  }

  openDdkModal(content: any) {
    this.getDdkList().then(() => {
      this.ddkSearchText = '';
      this.filterDdkList();
      this.modalService.open(content, { size: 'lg' });
    });
  }

  async getDdkList() {
    try {
      const res: any = await this.evalService.getDdkList().toPromise();
      let filtered = (res || []).filter(item => item.roleName !== 'ddk');
      filtered = [
        ...filtered.filter(item => item.roleName === 'ddk_all'),
        ...filtered.filter(item => item.roleName !== 'ddk_all')
      ];
      this.ddkList = filtered;
    } catch (e) {
      this.ddkList = [];
    }
  }

  filterDdkList() {
    const search = this.ddkSearchText.toLocaleLowerCase().trim();
    if (!search) {
      this.filteredDdkList = this.ddkList;
      return;
    }
    this.filteredDdkList = this.ddkList
      .map(group => ({
        ...group,
        users: group.users.filter(user =>
          (user.first_name + ' ' + user.last_name).toLocaleLowerCase().includes(search) ||
          user.email.toLocaleLowerCase().includes(search)
        )
      }))
      .filter(group => group.users.length > 0);
  }

  fetch(cb) {
    const req = new XMLHttpRequest();
    req.open('GET', 'https://unpkg.com/@swimlane/ngx-datatable@6.3.0/assets/data/company.json');

    req.onload = () => {
      cb(JSON.parse(req.response));
    };

    req.send();
  }


  updateValue(event, cell, rowIndex) {
    console.log('inline editing rowIndex', rowIndex);
    this.editing[rowIndex + '-' + cell] = false;
    this.rows[rowIndex][cell] = event.target.value;
    this.rows = [...this.rows];
    console.log('UPDATED!', this.rows[rowIndex][cell]);
  }

  closeEventModal() {
    this.modalService.dismissAll();
  }

  pickCellColor(obj) {

    const colored = [
      'team_name',
      'status',
    ];

    if (colored.indexOf(obj.column.prop) === -1) {
      return [];
    }

    return {
      'row-color-active': obj.row.status === EvaluationStatusEnum.ACTIVE,
      'row-color-close': obj.row.status === EvaluationStatusEnum.CLOSED,
      'row-color-failed': obj.row.status === EvaluationStatusEnum.FAILED,
      'row-color-success': obj.row.status === EvaluationStatusEnum.SUCCESS,
    };
  }

  filterOnEvaluation(checked) {
    this.search.nativeElement.value = '';
    localStorage.setItem('filterOnEvaluation', checked);
    this.rows = this.orginalEvals.filter(data => {
      data.bool_status = data.status === 'active'? true : false;
      if (checked) {
        return data.bool_status === true;
      }
      return data.bool_status || !data.bool_status;
    });
  }

}
