import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { EvaluationComponent } from './evaluation/evaluation.component';
import { TeamEvaluationDetailComponent } from './team-evaluation-detail/team-evaluation-detail.component';
import { EvaluationDetailComponent } from './evaluation-detail/evaluation-detail.component';
import { FormCanDeactivateGuardService } from './guards/form-can-deactivate-guard.service';

const routes: Routes = [
  {
    path: 'evaluation',
    component: EvaluationComponent
  },
  {
    path: 'evaluation/:id',
    component: EvaluationDetailComponent,
    canDeactivate: [FormCanDeactivateGuardService]
  },
  {
    path: 'evaluation/start/:id',
    component: TeamEvaluationDetailComponent
  }
];
@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class EvaluationRoutingModule { }
