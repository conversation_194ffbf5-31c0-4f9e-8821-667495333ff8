import {
  Directive,
  ElementRef,
  HostListener,
  Input,
  SimpleChanges
} from "@angular/core";

@Directive({
  selector: "[evaluationButtonGroup]",
})
export class ButtonGroupDirective {
  @Input() selectedButtonIndex: number = null;
  buttons: any [] = [];

  buttonsIndexes = [
     'secondary','success','warning', 'danger'
  ]

  constructor(private el: ElementRef) {}

  ngOnInit(): void {
    //Called after the constructor, initializing input properties, and the first call to ngOnChanges.
    //Add 'implements OnInit' to the class.
    
  }

  @HostListener("click", ["$event.target"])
  onClick(btn) {
    this.setDefaultClassOfButtons();
    btn.classList?.remove("btn-light");
    btn.classList?.add("btn-" + btn.id, "btn-sm");
  }
  
  ngAfterViewInit(): void {
    this.buttons = this.el.nativeElement.parentElement.querySelectorAll("button") as any[];
    this.setSelectedButton();
  }

  ngOnChanges(changes: SimpleChanges): void {
    //Called before any other lifecycle hook. Use it to inject dependencies, but avoid any serious work here.
    //Add '${implements OnChanges}' to the class.
    this.setSelectedButton();
  }

  setSelectedButton() {
    this.setDefaultClassOfButtons();
    if (this.selectedButtonIndex !== null) {
      let btn = this.buttons.forEach(element => {
        if(element.id === this.buttonsIndexes[this.selectedButtonIndex]) {
          element.classList?.remove("btn-light");
          element.classList?.add("btn-" + element.id, "btn-sm");
        }
      });
    }
  }

  setDefaultClassOfButtons() {
    this.buttons.forEach((button) => {
      button.classList?.remove(...button.classList);
      button.classList?.add("btn", "btn-light", "btn-sm");
    });
  }
}
