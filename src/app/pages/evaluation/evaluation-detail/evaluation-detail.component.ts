import { Component, HostListener, OnInit, ViewChild } from '@angular/core';
import { EvaluationService } from '../../../core/services/evaluation.service';
import { ActivatedRoute, Router } from '@angular/router';
import { FormArray, FormBuilder, FormGroup, NgForm } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import Swal from 'sweetalert2';
import { debounceTime } from 'rxjs/operators';
import { TechnicalDesignService } from 'src/app/core/services/technical-design.service';
import { CompatibilityValueEnum } from './../evaluation-compatibility.enum';
import { environment } from 'src/environments/environment';
import { RolesEnum } from '../../admin/roles.enum';
import { NgxSpinnerService } from 'ngx-spinner';

@Component({
  selector: 'app-evaluation-detail',
  templateUrl: './evaluation-detail.component.html',
  styleUrls: ['./evaluation-detail.component.scss'],
})
export class EvaluationDetailComponent implements OnInit {
  @HostListener('window:beforeunload', ['$event'])
  closeBrowser($event) {
    return this.evalForm.dirty ? false : true;
  }

  @ViewChild('evalForm') public evalForm: NgForm;

  constructor(
    private evaluationService: EvaluationService,
    private technicalDesignService: TechnicalDesignService,
    private route: ActivatedRoute,
    private fb: FormBuilder,
    private modalService: NgbModal,
    private router: Router,
    private spinner: NgxSpinnerService
  ) {
    this.currentUser = JSON.parse(localStorage.getItem('currentUser'));
  }

  rolesEnum = RolesEnum;
  currentUser;
  loading: any = {};
  apiUrl = environment.apiUrl;
  evalTeamDetails: any;
  private evalId: any;
  breadCrumbItems: any;
  activeId: any;
  categories: any[];
  form: FormGroup;
  conditionText: any;
  note: any;
  formId: any; // guncellenecek form
  technicalDesign = {};
  penaltySign = 1;
  imgPath: string;
  tableRows;
  isShown = false;
  _columns = [
    // { prop: 'id',},
    { id: 1, prop: 'team_name', name: 'Takim Adı', editable: false },
    { id: 2, prop: 'vehicle_number', name: 'Arac No', editable: false },
    { id: 3, prop: 'takim_no', name: 'Takim ID', editable: false },
    { id: 4, prop: 'toplam', name: 'Toplam', editable: false },
    //{ id: 5, prop: "gr", name: "GR", editable: false },
    //{ id: 6, prop: "gr_20", name: "GR %20", editable: false },
    //{ id: 7, prop: "gr_20_eklenmis_puan", name: "GR %20 EKLENMİŞ PUAN", editable: false },
    {
      id: 8,
      prop: 'arac_ozellikleri_tablosu',
      name: 'ARAÇ ÖZELLİKLERİ TABLOSU',
      editable: false,
    },
    { id: 9, prop: 'motor', name: 'MOTOR' },
    { id: 10, prop: 'motor_surucusu', name: 'MOTOR SÜRÜCÜSÜ' },
    {
      id: 11,
      prop: 'batarya_yonetim_sistemi',
      name: 'BATARYA YÖNETİM SİSTEMİ',
    },
    { id: 12, prop: 'yerlesik_sarj_birimi', name: 'YERLEŞİK ŞARJ BİRİMİ' },
    { id: 13, prop: 'batarya_paketleme', name: 'BATARYA PAKETLEME' },
    {
      id: 14,
      prop: 'elektronik_diferansiyel_uygulamasi',
      name: 'ELEKTRONİK DİFERANSİYEL UYGULAMASI',
    },
    { id: 15, prop: 'arac_kontrol_sistemi', name: 'ARAÇ KONTROL SİSTEMİ' },
    {
      id: 16,
      prop: 'izolasyon_izleme_cihazi',
      name: 'İZOLASYON İZLEME CİHAZI',
    },
    { id: 17, prop: 'direksiyon_sistemi', name: 'DİREKSİYON SİSTEMİ' },
    { id: 18, prop: 'kapi_mekanizmasi', name: 'KAPI MEKANİZMASI' },
    { id: 19, prop: 'mekanik_detaylar', name: 'MEKANİK DETAYLAR' },
    { id: 20, prop: 'arac_elektrik_semasi', name: 'ARAÇ ELEKTRİK ŞEMASI' },
    { id: 21, prop: 'orijinal_tasarim', name: 'ORİJİNAL TASARIM' },
    { id: 22, prop: 'yakit_pili', name: 'YAKIT PİLİ' },
    {
      id: 23,
      prop: 'yakit_pili_kontrol_sistemi',
      name: 'YAKIT PİLİ KONTROL SİSTEMİ',
    },
    { id: 24, prop: 'enerji_yonetim_sistemi', name: 'ENERJİ YÖNETİM SİSTEMİ' },
    { id: 25, prop: 'dinamik_surus_testi', name: 'DİNAMİK SÜRÜŞ TESTİ' },
    { id: 26, prop: 'telemetri', name: 'TELEMETRİ' },
    { id: 27, prop: 'yerlilik_durumu', name: 'YERLİLİK DURUMU' },

    // { prop: 'orijinal_tasarim', name: 'Orjinal Tasarım' },
    // { prop: 'kriter_aciklama', name: 'Kriter Açıklama' },
  ];

  uygun = {
    label: 'Uygun',
    value: CompatibilityValueEnum.UYGUN,
    btnId: 'success',
  };
  kusurlu = {
    label: 'Kusurlu',
    value: CompatibilityValueEnum.KUSURLU,
    btnId: 'warning',
  };
  bos = {
    label: 'Boş',
    value: CompatibilityValueEnum.BOS,
    btnId: 'secondary',
    hidden: false,
  };
  disk = { label: 'Disk', value: CompatibilityValueEnum.DISK, btnId: 'danger' };

  private techInputTimers: { [id: string]: any } = {};

  ngOnInit(): void {
    this.breadCrumbItems = [
      { label: 'Evaluations' },
      { label: 'Detail', active: true },
    ];

    this.route.params.subscribe((params) => {
      this.evalId = params.id;
      this.getEvaluations();
    });
  }

  getTeamInfo(teamInfo) {
    return teamInfo.team?.team_name + ' - ' + teamInfo.team?.university_name;
  }

  private getEvaluations() {
    this.evaluationService
      .getEvaluations(this.evalId)
      .subscribe((evaluation: any) => {
        this.evalTeamDetails = evaluation.data;
        //this.evalTeamDetails.files[0].asset.path = this.evalTeamDetails.files[0].asset.path.replaceAll('PNG', 'png');
        //this.getTechnicalDesign(this.evalTeamDetails?.team_id);
        this.categories = Array.from(
          new Set(
            evaluation.data.details.map((i) => {
              return this.getCategory(i);
            })
          )
        );
        this.categories = this.categories.filter((category) => {
          return this.haveRoleForTechnicalDesign(category);
        });
        this.form = new FormGroup({});
        this.setTechnicalDesign(this.evalTeamDetails.technical_design);
        this.categories.push('Technical Design');
        this.form.addControl('Technical Design', this.fb.array([]));
        evaluation.data.details.map((item) => {
          item.criteria.category = item.criteria.category.replaceAll('.', ' ');
          item.criteria.category = this.getCategory(item);

          // console.log('item',item);
          if (!this.form.get(item.criteria.category)) {
            this.form.addControl(item.criteria.category, this.fb.array([]));
          } else {
          }
          // console.log('this.form.get(item.category)', this.form);

          (this.form.get(item.criteria.category) as FormArray)?.push(
            this.fb.group({
              category: [{ value: item.criteria.category, disabled: true }],
              subject: [{ value: item.criteria.subject, disabled: true }],
              condition: [{ value: item.criteria.content || item.criteria.raw_rule, disabled: true }],
              // required: [item.criteria.is_required],
              required: [item.is_required],
              value: [item.value],
              notes: [{ value: item.notes, disabled: true }],
              updated_by: [{ value: ((item.updated_by?.first_name) ?? '') + ' ' + ((item.updated_by?.last_name) ?? ''), disabled: true }],
              compatibility: [item.compatibility],
              disabled: [!item.active || this.haveSticker(item.criteria.category)],
              compatibilityOptions: [this.setCompabilityOptions(item.criteria)],
              id: [item.id],
              maddeNo: [this.getCriteriaNo(item)],
              penaltyType: [this.setPenaltyType(item.penalty, item)],
              allowPenalty: [false],
              allowPenaltyValue: [item.allow_penalty.type === 'dynamic'],
              penalty: [Math.abs(item.penalty)],
              rule: [item.criteria.rule],
            })
          );
        });
        this.formAutoSave();
        // console.log('###########test', this.form.controls);
      });
  }

  protected getCategory(i) {
    return i.criteria.category + (i.after_race ? ('[' + i.after_race + ']') : '');
  }

  protected getCriteriaNo(i) {
    return i.criteria.no + (i.after_race ? ('[' + i.after_race + ']') : '');
  }

  haveSticker(category) {
    if (this.categories.includes('After Race Checks') && category !== 'After Race Checks') {
      return true;
    }
    return false;
  }

  setPenaltyType(penalty, item) {
    if (penalty === 0) {
      return item?.allow_penalty?.function === 'award' ? -1 : 1;
    }

    if (penalty < 0) {
      return -1;
    } else {
      return 1;
    }
  }

  activeIdChange($event: any) {
    //console.log("event", $event);
    this.activeId = $event.replaceAll('.', ' ');
    this.navbarOpen = false;
  }

  onChange($event: Event) {}

  setFormId(id) {
    this.formId = id;
  }


  saveTechnicalDesign(data) {
    if (data?.value > 400) {
      //this.technicalDesign[$event.data.key] = $event.originalEvent.target.value;
      //this.tableRows = [...this.getTableRows()];
      this.setTechnicalDesign(this.evalTeamDetails.technical_design);
      this.showValidationError({ error: { message: 'Maximum deger aşıldı.' } });
    } else {
      const obj: any = Object.assign({}, {
        id: data.id,
        point: data.value,
        team_id: this.evalTeamDetails?.team_id,
        created_at: '2022-06-28T09:12:06.000000Z',
        user_id: this.currentUser.user.id,
      });

      if (data.copy != -1) {
        obj.copy = data.copy;
      }
      if (data.domestic != -1) {
        obj.domestic = data.domestic;
      }

      this.technicalDesignService
        .updateTechnicalDesigns(obj)
        .subscribe((response: any) => {
          // const updatedTechDesignIndex = this.evalTeamDetails.technical_design.findIndex( tech => tech.id === response.data.id);
          // this.evalTeamDetails.technical_design[updatedTechDesignIndex] = response.data;
          this.evalTeamDetails.technical_design = response.data;
          this.setTechnicalDesign(this.evalTeamDetails.technical_design);
        });
    }
  }

  saveEvalRow(form: any) {
    this.saveRow(form.value);
    form.reset(form.getRawValue());
  }

  openCondition(conditionModal: any, activeId: any, i: number) {
    this.conditionText =
      this.form.get(activeId)['controls'][i].controls.condition.value;

    this.modalService.open(conditionModal, {
      centered: true,
      windowClass: 'modal-holder',
    });
  }

  openAddNote(addNoteModal: any, activeId: any, i: number) {
    this.note = this.form.get(activeId)['controls'][i].controls.notes.value;
    if (this.note) {
      this.note += '\n';
    }

    this.modalService.open(addNoteModal, {
      centered: true,
      windowClass: 'modal-holder',
    });
  }

  openBigTeamPhoto(teamPhotoModal: any, imgPath) {
    this.imgPath = this.getPath(imgPath);
    this.modalService.open(teamPhotoModal, {
      centered: true,
      windowClass: 'modal-holder',
    });
  }

  rejectConfirm() {
    Swal.fire({
      title: 'Are you sure to reject?',
      text: 'It will reject this evaluation of the team.',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#34c38f',
      cancelButtonColor: '#f46a6a',
      confirmButtonText: 'Yes, reject.',
    }).then((result) => {
      if (result.value) {
        this.closeEval();
      }
    });
  }

  openEval() {
    this.evaluationService.openEval(this.evalId).subscribe((data) => {
      //alert('Evaluation opened successfully');
      location.reload();
    });
  }

  closeModal() {
    this.modalService.dismissAll();
  }

  closeEval() {
    this.evaluationService.closeEval(this.evalId).subscribe(
      (data) => {
        this.position(
          'Evaluation closed successfully',
          'closed',
          'OK',
          'success'
        );
        this.router.navigate(['/evaluation']);
      },
      (error) => this.showValidationError(error)
    );
  }

  updateStatus(status) {
    const obj = { status };
    this.evaluationService.updateStatus(obj, this.evalId).subscribe((data) => {
      this.router.navigate(['/evaluation']);
    });
  }

  checkSticker() {
    this.spinner.show();
    this.evaluationService.checkSticker(this.evalId).subscribe((data: any) => {
      this.spinner.hide();
      this.decideMessage(data.data);
    }, error => {
      this.spinner.hide();
    });
  }

  decideMessage(message: {
    disqualified: any[];
    passed: boolean;
    domestic: boolean;
    required: any[];
  }) {
    let title;
    let text;
    let confirmText;
    let icon;
    if (message.passed) {
      title = 'Team is ready to have STICKER!';
      text =
        'All set. Team is ready to have sticker. Click Yes, confirm it to give sticker.';
      confirmText = 'Yes, confirm it!';
      icon = 'success';
      if (!message.domestic) {
        title = 'Domestic parts penalties prevent to have stickers';
        text = 'Domestic parts penalties can\'t be above 30';
        confirmText = false;
        icon = 'warning';
        this.stickerConfirm(title, text, confirmText, icon);
      } else {
        this.stickerConfirm(title, text, confirmText, icon);
      }
    } else {
      title = 'Team is NOT ready to have STICKER!';
      if (message.disqualified.length > 0) {
        confirmText = null;
      } else {
        confirmText = 'Confirm anyway';
      }
      icon = 'warning';
      this.setStickerReadyContent(message, title, icon, confirmText);
    }
  }

  setStickerReadyContent(message, title, icon, confirmText) {
    let text = '';
    if (message.required.length <= 20) {
      text = `<div style="display: block; position: relative; height: 400px; overflow: auto;">
      <table class="table table-bordered" style="font-size: 10px;"> <thead><tr>
    <th>No</th>
    <th>Subject</th>
    <th>Status</th>
  </tr></thead> <tbody>`;
      message.required.forEach((element) => {
        text += `<tr>
      <td>${element.criteria.no}</td>
      <td><h6>${element.criteria.subject}</h6><p
      style="white-space: pre-line; font-size: 9px;"
      class="text-truncate m-0"
    >${element.criteria.content}</p></td>
      <td>${this.getCompatibilityText(element.compatibility)}</td>
    </tr>`;
      });
      text += `</tbody></table></div>`;
    } else {
      message.required.forEach((element) => {
        text += `${element.criteria.no} - `;
      });
      text = text.slice(0, text.length - 3);
      text += ' are blocking to have sticker.';
    }
    this.stickerConfirm(title, text, confirmText, icon);
  }

  getCompatibilityText(compatibility) {
    switch (compatibility) {
      case this.uygun.value:
        return this.uygun.label;
      case this.bos.value:
        return this.bos.label;
      case this.disk.value:
        return this.disk.label;
      case this.kusurlu.value:
        return this.kusurlu.label;
    }
  }

  stickerConfirm(title, text, confirmText, icon) {
    Swal.fire({
      html: text,
      title,
      icon,
      customClass: {
        popup: 'custom-swal2-popup'
      },
      showCancelButton: true,
      showConfirmButton: !!confirmText,
      confirmButtonColor: '#34c38f',
      cancelButtonColor: '#f46a6a',
      confirmButtonText: confirmText,
    }).then((result) => {
      if (result.value) {
        this.giveSticker();
      }
    });
  }

  giveSticker() {
    this.spinner.show();
    this.evaluationService.giveSticker(this.evalId).subscribe(
      (data: any) => {
        const check = data.data || data;
        this.spinner.hide();
        const icon = check?.success ? 'success' : 'warning';
        const text = check?.message;
        console.log('test', text);
        const title = check?.success ? 'Sticker given successfully' : 'An error occurred while give sticker process';
        this.position(
          title,
          text,
          'OK',
          icon
        );
      },
      (error) => {
        this.showValidationError(error);
      }
    );
  }

  exportEval() {
    this.spinner.show();
    this.evaluationService.exportEval(this.evalId).subscribe((data) => {
      // alert('Exported');
      this.spinner.hide();
      this.downLoadFile(
        data,
        'application/ms-excel',
        this.evalTeamDetails.team.university_name +
        '-' +
        this.evalTeamDetails.team.team_name +
        '_evalaution.xlsx'
      );
    }, error => {
      this.spinner.hide();
      this.showValidationError(error);
    });
  }

  downLoadFile(data: any, type: string, fileName: string) {
    const blob = new Blob([data], { type });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    link.click();

    // let pwa = window.open(url);
    // if (!pwa || pwa.closed || typeof pwa.closed == 'undefined') {
    //   alert('Please disable your Pop-up blocker and try again.');
    // }
  }

  saveNote(activeId) {
    var index = this.form.controls[activeId]['controls'].findIndex(
      (item) => item.value.id === this.formId
    );
    //this.form.controls[activeId]["controls"][index].patchValue({ notes: this.note });
    this.form.controls[activeId]['controls'][index].value.notes = this.note;
    this.form.controls[activeId]['controls'][index].markAsDirty();
    this.saveAllRows();
    this.closeModal();
  }

  saveAllRows() {
    const val = Object.values(this.form.controls).reduce(
      (acc: any, form: any) => {
        const values = form.controls
          .filter((i) => i.dirty && !this.loading[i.value.id])
          .map((i) => i.value);
        // form.reset(form.getRawValue());

        return acc.concat(values);
      },
      []
    );

    console.log('val', val);
    val.map((i) => {
      this.saveRow(i);
    });

    // this.form.get('Test')['controls'].map(item=>{
    //   console.log('item',item.value);
    //   console.log('dirty',item.dirty);
    // });
    // console.log('all', this.form.get('Test')['controls']);
  }

  saveRow(data) {
    this.loading[data.id] = true;

    if (data.compatibility === 'null') {
      data.compatibility = null;
    }
    data.penalty = data.penalty * this.penaltySign;
    this.evaluationService.saveEval(data).subscribe(
      (res: any) => {
        this.loading[data.id] = false;
        this.categories.forEach((category) => {
          category = category.replaceAll('.', ' ');

          var index = this.form.controls[category]?.['controls'].findIndex(
            (item) => item.value.id === data.id
          );
          if (index !== -1) {
            this.form.controls[category]['controls'][index].markAsPristine();
          }

        });

        // console.log('res', res);
        res?.data?.map((i) => {
          this.updateRowState(i);
        });
      },
      (error) => {
        this.loading[data.id] = false;

        // this.saveInfoPopup('Error', 'Item can\'t be saved.', 'warning');
        // this.position('Error', 'Item can\'t be saved.', 'OK', 'danger');
        this.showValidationError(error);
      }
    );
  }

  updateRowState(data) {
    if (data?.evaluation) {
      Object.assign(this.evalTeamDetails, data.evaluation);
      // this.saveInfoPopup('Success', 'Item successfully saved.', 'success');
    }
    this.categories.forEach((category) => {
      category = category.replaceAll('.', ' ');

      const index = this.form.controls[category]['controls'].findIndex(
        (item) => item.value.id === data.id
      );
      if (index !== -1) {
        this.form.controls[category]['controls'][index].patchValue(
          {
            penaltyType: this.setPenaltyType(data.penalty, data),
            penalty: Math.abs(data.penalty * this.penaltySign),
            compatibility: data.compatibility,
            notes: data.notes,
            disabled: !data.active,
            required: data.is_required,
            allowPenalty: false,
            allowPenaltyValue: data.allow_penalty.type === 'dynamic',
          },
          { onlySelf: true, emitEvent: false }
        );
      }
    });


  }

  position(title, text, confirmText, icon, dismissAll = true) {
    Swal.fire({
      title,
      html: text,
      icon,
      confirmButtonColor: '#34c38f',
      cancelButtonColor: '#f46a6a',
      confirmButtonText: confirmText,
    }).then((result) => {
      if (result.value && dismissAll) {
        this.modalService.dismissAll();
      }
    });
  }

  onCompatibilityChange(control, value) {
    if (control.value.compatibility === value) {
      return;
    }
    (window as any).c = control;
    control.patchValue({
      compatibility: value,
    });
    control.controls.compatibility.markAsDirty();
    control.controls.compatibility.markAsTouched();
  }

  showValidationError(error) {
    let message = error.error.message;
    if (message === 'validation.error') {
      message = Object.values(error.error.errors).join('\n<br>');
    }

    this.position('Error', message, 'OK', 'error', false);
  }

  setTechnicalDesign(techArray: any[]) {
    this.tableRows = techArray.map((tech) => {
      const color = tech?.original_point !== tech?.point ? 'yellow' : 'white';
      return {
        id: tech?.id,
        name: tech?.key,
        value: tech?.point,
        color,
        domestic: parseInt(tech?.domestic, 10),
        copy: parseInt(tech?.copy, 10),
        user_name: (tech?.user?.first_name ?? '') + ' ' + (tech?.user?.last_name ?? ''),
      };
    });
  }

  /*
  getTechnicalDesignp(id) {
    this.technicalDesignService
      .getTechnicalDesign(id)
      .subscribe((response: any) => {
        this.technicalDesign = response.data;
        this.tableRows = this.getTableRows();
      }, (error) => {
        console.log('error', error);
      });
  }

  getTableRows() {
    this.technicalDesign['vehicle_number'] =
      this.technicalDesign['team']?.vehicle_number;
    this.technicalDesign['team_name'] = this.technicalDesign['team']?.team_name;
    return this._columns.map((col) => {
      const color =
        typeof this.technicalDesign['orj_' + col.prop] !== 'undefined' &&
        this.technicalDesign[col.prop] !==
        this.technicalDesign['orj_' + col.prop]
          ? 'yellow'
          : 'white';
      return {
        id: col.id,
        key: col.prop,
        name: col.name,
        value: this.technicalDesign[col.prop],
        editable: col.editable,
        color,
      };
    });
  }
  */
  formAutoSave() {
    this.form.valueChanges.pipe(debounceTime(500)).subscribe((event) => {
      this.saveAllRows();
    });
  }

  setCompabilityOptions(criteria: any): any[] {
    const compobilityOptions = [];

    if (criteria.allow_approve !== '0') {
      compobilityOptions.push(this.uygun);
    }

    compobilityOptions.push(this.bos);

    if (criteria.allow_defective !== '0') {
      compobilityOptions.push(this.kusurlu);
    }
    if (criteria.allow_disqualified !== '0') {
      compobilityOptions.push(this.disk);
    }

    return compobilityOptions;
  }

  changePenaltyType(control, event) {
    this.penaltySign = Number(event.target.value);
    control.patchValue(
      {
        penaltyType: Number(event.target.value),
      },
      { onlySelf: true, emitEvent: false }
    );
  }

  navbarOpen = false;

  toggleNavbar() {
    this.navbarOpen = !this.navbarOpen;
  }

  getPath(path: string) {
    return `${this.apiUrl}storage/${path}`;
  }

  haveRole(role) {
    return !!(this.currentUser['user'] && this.currentUser['user']['role'] && this.currentUser['user']['role'].includes(role));
  }

  haveRoleForTechnicalDesign(role) {
    const userRoles = this.currentUser['user']['role'];
    if (userRoles.includes('superadmin') || userRoles.includes('ddk_all')) {
      return true;
    }

    return !!(userRoles.includes('ddk') && !userRoles.includes('ddk_all') && userRoles.includes('DDK ' + role));
  }

  onClickNote($event: MouseEvent) {
    console.log('eent', $event);
  }

  /**
   * Debounced save for technical design input
   */
  onTechInputChange(data: any) {
    const id = data.id;
    if (this.techInputTimers[id]) {
      clearTimeout(this.techInputTimers[id]);
    }
    this.techInputTimers[id] = setTimeout(() => {
      this.saveTechnicalDesign(data);
      this.techInputTimers[id] = null;
    }, 1000);
  }

  /**
   * Save immediately on blur
   */
  onTechInputBlur(data: any) {
    const id = data.id;
    if (this.techInputTimers[id]) {
      clearTimeout(this.techInputTimers[id]);
      this.techInputTimers[id] = null;
    }
    this.saveTechnicalDesign(data);
  }

  /**
   * Save immediately on Enter
   */
  onTechInputEnter(data: any) {
    const id = data.id;
    if (this.techInputTimers[id]) {
      clearTimeout(this.techInputTimers[id]);
      this.techInputTimers[id] = null;
    }
    this.saveTechnicalDesign(data);
  }
}
