<link
  href="https://cdn.jsdelivr.net/css-toggle-switch/latest/toggle-switch.css"
  rel="stylesheet"
/>

<div class="container-fluid" *ngIf="evalTeamDetails; else loading">
  <div class="row" style="margin-bottom: 10px;">
    <div [ngClass]="evalTeamDetails.team?.photo?.asset?.path? 'col-xl-11 col-lg-10 col-md-10 col-sm-10 col-9' : 'col-12'">
      <div class="text-center my-2">
        <p>
          This is the <strong>{{ evalTeamDetails.eval_day }}</strong> evaluation
          of this team. Progress is
          <strong>{{ evalTeamDetails.progress }}%</strong>

          <ngb-progressbar
            [value]="evalTeamDetails.progress"
            [striped]="true"
            [animated]="true"
            type="success"
          >
          </ngb-progressbar>
        </p>
      </div>
    </div>
    <div class="col-xl-1 col-lg-2 col-md-2 col-sm-2 col-3 team-photo-border" *ngIf="evalTeamDetails.team?.photo?.asset?.path">
      <img class="team-photo" [src]="getPath(evalTeamDetails.team?.photo?.asset?.path)" (click)="openBigTeamPhoto(teamPhoto, evalTeamDetails.team?.photo?.asset?.path)">
    </div>
    <!-- end col-->
  </div>

  <a
    target="_blank"
    href="javascript: void(0);"
    [routerLink]="['/team/detail/', evalTeamDetails.team_id]"
    >Click to see Team details. (Opened via new tab)</a
  >
  <div>
  <!-- start page title -->
    ID: {{ evalTeamDetails.team?.team_id }} VN: {{ evalTeamDetails.team.vehicle_number }}
  </div>
  <app-page-title
    [title]="getTeamInfo(evalTeamDetails)"
    [breadCrumbItems]="breadCrumbItems"
  ></app-page-title>
  <!-- end page title -->
<!--
  <nav class="navbar navbar-expand-lg navbar-light bg-light">
    <button class="navbar-toggler" style="float: left;" type="button" (click)="toggleNavbar()">
      <span class="navbar-toggler-icon"></span>
    </button>

    <div class="collapse navbar-collapse"
    [ngClass]="{ 'show': navbarOpen }">
      <ul class="navbar-nav mr-auto">
        <li class="nav-item active" (click)="activeIdChange($event.target.innerText)" *ngFor="let category of categories">
          <a class="nav-link" routerLink="#" routerLinkActive="" [routerLinkActiveOptions]="{exact: false}"><span>{{category}} </span></a>
        </li>
      </ul>
    </div>
  </nav>
-->
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          <ul
            ngbNav
            #justifiednav="ngbNav"
            class="nav-pills nav-justified d-none d-sm-flex"
            (activeIdChange)="activeIdChange($event)"
          >
            <li [ngbNavItem]="category" *ngFor="let category of categories">
              <a ngbNavLink>
                <span >{{ category }}</span>
              </a>
              <ng-template ngbNavContent>
                <!--                *ngIf="evalTeamDetails.status !=='success'"-->
                <ng-template [ngTemplateOutlet]="mainContent"></ng-template>
              </ng-template>
            </li>
          </ul>
          <div [ngbNavOutlet]="justifiednav"></div>
        </div>
      </div>
    </div>
    <!-- end col -->
  </div>
  <!-- end row -->

  <div class="row">
    <div class="col-12">
      <div class="text-center my-3">
        <!--
        <button
          type="button"
          class="btn btn-primary mr-1"
          (click)="saveAllRows()"
        >
          Save All
        </button>
      -->
        <button
          type="submit"
          *ngIf="evalTeamDetails.status === 'active' && (haveRole(rolesEnum.CLOSE_EVALUATION) || haveRole(rolesEnum.SUPERADMIN))"
          class="btn btn-secondary mr-1"
          (click)="rejectConfirm()"
        >
          Reject
        </button>
        <button
          type="submit"
          *ngIf="evalTeamDetails.status === 'closed'"
          class="btn btn-secondary mr-1"
          (click)="openEval()"
        >
          Open Evaluation
        </button>
        <button
          *ngIf="evalTeamDetails.eval_day >= 4"
          type="submit"
          class="btn btn-secondary mr-1"
          (click)="updateStatus('failed')"
        >
          Permanently Failed
        </button>
        <button
          *ngIf="!evalTeamDetails.after_race_started && (haveRole(rolesEnum.GIVE_STICKER) || haveRole(rolesEnum.SUPERADMIN))"
          type="button"
          class="btn btn-success mr-1"
          (click)="checkSticker()"
        >
          <i class="bx bx-check-double font-size-16 align-middle mr-2"></i>
          Sticker Ready
        </button>
        <button
          *ngIf="evalTeamDetails.after_race_started  && (haveRole(rolesEnum.GIVE_STICKER) || haveRole(rolesEnum.SUPERADMIN))"
          type="button"
          class="btn btn-warning mr-1"
          (click)="checkSticker()"
        >
          <i class="bx bx-check-double font-size-16 align-middle mr-2"></i>
          After Race Check
        </button>

        <button
          type="submit"
          class="btn btn-success mr-1"
          (click)="exportEval()"
        >
          Export Evaluation
        </button>
      </div>
    </div>
    <!-- end col-->
  </div>
</div>
<!-- container-fluid -->

<ng-template #conditionModal let-modal>
  <div class="modal-header">
    <h5 class="modal-title mt-0">{{ conditionText }}</h5>
    <button
      type="button"
      class="close"
      (click)="modal.dismiss('Cross click')"
      aria-hidden="true"
    >
      ×
    </button>
  </div>
</ng-template>
<ngx-spinner type="ball-clip-rotate-multiple" size="medium"> </ngx-spinner>
<!-- mainContent -->
<ng-template #mainContent>
  <div class="card">
    <div *ngIf="evalTeamDetails.status === 'closed'" class="layer"></div>
    <form [formGroup]="form" #evalForm="ngForm">
      <ng-container [formGroupName]="activeId">
        <div class="card-body">
          <div class="dropdown d-sm-none">
            <button class="btn dropdown-toggle dropdown-button select-box mb-2" (click)="toggleNavbar()" type="button">
              {{ activeId }}
              <i class="fa fa-angle-down" style="float: right;" aria-hidden="true"></i>
            </button>
            <div [ngClass]="{ 'show': navbarOpen }" class="dropdown-menu" >
              <a class="dropdown-item" (click)="activeIdChange(category)" [ngClass]="{'dropdown-item-active': category === activeId}" *ngFor="let category of categories">{{category}}</a>
            </div>
          </div>
          <div class="table-responsive">
            <table
              *ngIf="activeId !== 'Technical Design'"
              class="table table-nowrap table-centered mb-0"
              style="overflow:scroll"
            >
              <thead>
                <th class="no-column" style="background-color: white;">No</th>
                <th class="subject-column">Subject</th>
                <th class="other-column">Comp.</th>
                <th class="other-column">Penalty</th>
                <th class="note-column">Updated By</th>
                <th class="note-column">Note</th>
              </thead>
              <tbody>
                <ng-container
                  *ngFor="
                    let list of form.controls[activeId]['controls'];
                    index as i
                  "
                >
                  <tr (click)="setFormId(list.value.id)" [ngClass]="{'have-rule': list.controls.rule.value}">
                    <ng-container [formGroupName]="i">
                      <td class="no-column"
                      [style.background-color]="list.controls.rule.value? '#EBEBEB': '#fff'"
                      [ngClass]="{'no-column-after-race': list.controls.category.value === 'After Race Checks'}"
                      >
                        <h6 style="min-width: 50px; height: 20px;">
                          {{ list.controls.maddeNo.value }}
                          <div style=""
                            class="spinner-border spinner-border-sm"
                            role="status"
                            *ngIf="loading[list.controls.id.value]"
                          >
                            <span class="sr-only">Loading...</span>
                        </div>
                        </h6>
                      </td>
                      <td class="subject-column">
                        <p
                          style="white-space: pre-line; font-weight: 600"
                          class="text-truncate font-size-12 m-0"
                          ngbTooltip="Subject Information"
                          placement="top"
                        >
                          {{ list.controls.subject.value }}
                          <span
                            style="color: red"
                            *ngIf="list.controls.required.value"
                            >*
                          </span>
                        </p>
                        <h5
                          class="text-truncate font-size-10 m-0"
                          (click)="openCondition(conditionModal, activeId, i)"
                        >
                          <textarea
                            [style.background-color]="list.controls.rule.value? '#EBEBEB': '#fff'"
                            style="border: 0px solid;"
                            class="text-dark subject-column"
                            formControlName="condition"
                          ></textarea>
                        </h5>
                      </td>

                      <td class="compatibility-column">
                        <div [ngClass]="{'disable': loading[list.controls.id.value] }"
                          class="btn-group"
                          evaluationButtonGroup
                          [selectedButtonIndex]="list.value.compatibility"
                          role="group"
                          aria-label="First group"
                        >
                          <button
                            [disabled]="list.value?.disabled || evalTeamDetails.status == 'closed'"
                            *ngFor="let item of list.value.compatibilityOptions"
                            type="button"
                            [id]="item.btnId"
                            (click)="onCompatibilityChange(list, item.value)"
                            class="btn"
                          >
                            {{ item.label }}
                          </button>
                        </div>
                      </td>
                      <td>
                        <div class="col-md-9">
                          <div class="input-group input-group-sm mr-3 penalty-column"
                            [ngClass]="{'disable': list.controls.rule.value }">
                            <div class="input-group-prepend">
                              <select
                                [disabled]="!list.controls.allowPenalty.value"
                                class="select-box"
                                [value]="list.controls.penaltyType.value"
                                (change)="changePenaltyType(list, $event)"
                              >
                                <option  value="-1">Ödül</option>
                                <option value="1">Ceza</option>
                              </select>
                            </div>
                            <input
                              [attr.disabled]="!list.controls.allowPenaltyValue.value ? '' : null "
                              [style]="{
                              'background-color': list.controls.rule.value? '#F5F5F5': '#fff',
                              'color': !list.controls.allowPenaltyValue.value? 'rgb(141 144 146)': '#495057'
                              }"
                              class="form-control"
                              name="penalty"
                              formControlName="penalty"
                              style="width: 20px;"
                              ngbTooltip="Penalty"
                              placement="top"
                              type="text"
                              placeholder="Ceza"
                              aria-label="Ceza"
                              aria-describedby="inputGroup-sizing-sm"
                            />
                            <div class="input-group-append">
                              <span class="input-group-text">Wh</span>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td class="updated-column">
                        <pre>{{list.controls.updated_by.value}}</pre>
                      </td>
                      <td class="note-column">
                        <div
                          class="text-truncate font-size-14 m-0"
                          ngbTooltip="Note Information"
                          placement="top"
                          (click)="openAddNote(addNoteModal, activeId, i)"
                        >
                          <pre>{{list.controls.notes.value}}</pre>
                        </div>

                        <button
                          *ngIf="!list.controls.notes.value"
                          type="button"
                          class="btn btn-sm btn-link"
                          (click)="openAddNote(addNoteModal, activeId, i)"
                        >
                          Add Note
                        </button>
                      </td>
                    </ng-container>
                  </tr>
                </ng-container>
              </tbody>
            </table>
          </div>
        </div>
      </ng-container>
    </form>

    <div *ngIf="activeId === 'Technical Design'" class="py-2">
      <a target="_blank" [href]="evalTeamDetails.team.ttr_link"> Click for Technical Design Report</a>
    </div>
    <p-table
      *ngIf="activeId === 'Technical Design'"
      [value]="tableRows"
      [scrollable]="true"
      dataKey="id"
    >
      <ng-template pTemplate="header">
        <tr>
<!--          <th class="no-columns">No</th>-->
          <th class="columns">Madde</th>
          <th class="columns">Değer</th>
          <th class="columns">Yerlilik</th>
<!--          <th class="columns">Kopya/İntihal</th>-->
          <th class="columns">Updated By</th>

        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-data let-f="data">
        <tr style="" [pEditableRow]="data">
<!--          <td class="no-columns">-->
<!--            {{ data.id }}-->
<!--          </td>-->
          <td class="columns">
            {{ data.name }}
          </td>
          <td class="columns" [pEditableColumn]="data" [pEditableColumnDisabled]="data.editable === false">
            <p-cellEditor>
              <ng-template pTemplate="input">
                <input *ngIf="data.key !== 'yerlilik_durumu'"
                  type="number"
                  pInputText
                  class="form-control"
                  [style.background-color]="data.color"
                  [disabled]="data.editable === false"
                  [(ngModel)]="data.value"
                  (ngModelChange)="onTechInputChange(data)"
                  (blur)="onTechInputBlur(data)"
                  (keydown.enter)="onTechInputEnter(data)"
                />
                <select
                  *ngIf="data.key === 'yerlilik_durumu'"
                  class="select-box select-box-yerlilik"
                  [(ngModel)]="data.value"
                  (ngModelChange)="saveTechnicalDesign(data)"
                  >
                  <option [value]="1">Var</option>
                  <option [value]="0">Yok</option>
                </select>
              </ng-template>
              <ng-template pTemplate="output">
                <p [style.background-color]="data.color" *ngIf="data.key !== 'yerlilik_durumu'" >
                  {{ data.value }}
                </p>
                <p [style.background-color]="data.color"  *ngIf="data.key === 'yerlilik_durumu'" >
                  {{ data.value? 'Var': 'Yok' }}
                </p>
              </ng-template>
          </p-cellEditor>
          </td>
          <td class="columns">
            <ng-container class="center" *ngIf="data.domestic !== -1">
              <input type="checkbox" [id]="'yerli' + data.id" id="yerli" name="yerli" (change)="saveTechnicalDesign(data)" [(ngModel)]="data.domestic">
              <label class="ml-1" [for]="'yerli' + data.id"> Yerli</label>
            </ng-container>
          </td>
<!--          <td class="columns">-->
<!--            <ng-container *ngIf="data.copy !== -1">-->
<!--              <input type="checkbox" [id]="'kopya' + data.id" name="kopya" (change)="saveTechnicalDesign(data)" [(ngModel)]="data.copy">-->
<!--              <label class="ml-1" [for]="'kopya' + data.id"> Kopya/İntihal</label>-->
<!--            </ng-container>-->
<!--          </td>-->
          <td class="columns">
            {{ data.user_name}}
          </td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</ng-template>

<ng-template #addNoteModal let-modal>
  <div class="modal-header">
    <h5 class="modal-title mt-0">Add Note</h5>
    <button
      type="button"
      class="close"
      (click)="modal.dismiss('Cross click')"
      aria-hidden="true"
    >
      ×
    </button>
  </div>
  <div class="modal-body">
    <textarea
      [(ngModel)]="note"
      placeholder="Add note"
      class="note-text "
    ></textarea>
    <button
      style="float: right"
      class="btn btn-sm btn-secondary mt-2"
      (click)="saveNote(activeId)"
    >
      Save
    </button>
  </div>
</ng-template>

<ng-template #teamPhoto let-modal>
  <div class="modal-header">
    <h5 class="modal-title mt-0">Team Photo</h5>
    <button
      type="button"
      class="close"
      (click)="modal.dismiss('Cross click')"
      aria-hidden="true"
    >
      ×
    </button>
  </div>
  <div class="modal-body">
  <img style="width: 100%;" [src]="imgPath" alt="">
  </div>
</ng-template>

<ng-template #loading>
  <div class="d-flex justify-content-center align-items-center" style="height: 300px;">
    <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;"></div>
  </div>
</ng-template>
