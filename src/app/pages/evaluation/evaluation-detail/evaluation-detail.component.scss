.layer {
  background-color: rgba(208, 206, 206, 0.5);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index:1001;
  pointer-events: none; 
}
textarea {
  resize: none;
  //width:150px;
  width: 100%;
  // IOS text renk sorunu
  -webkit-text-fill-color: #000;
  opacity: 1;
}

.table th, .table td{
  padding: 0.25rem;
}

@media only screen and (max-width: 1024px) {
  .text-dark{
    max-width:140px;
  }
}


.nav-link{
  height: 100%;
  width: 100%;
  border-radius: 10px;

}

.nav-pills li {
  margin-left: 2px;
  margin-bottom: 2px;
  border-radius: 10px;
}
.switch-toggle {
  width: 10em;
}

.switch-toggle label:not(.disabled) {
  cursor: pointer;
}

.red {
  color: red !important;
}

.note-text {
  padding: 5px;
  border-radius: 5px;
  background-color: #f5f5f5;
  border: 1px solid #e3e3e3;
  height: 130px;
}

.table-column {
  width: 300px;
}

:host ::ng-deep .card-body {
  padding: 0;
}

.select-box {
  color: #495057;
  border-color: #ced4da;
  background: #eff2f7;
  border-radius: 3px;
  text-align: center;

  &-yerlilik {
    height: 30px;
    min-width: 100px;
  }
}
.dropdown{
  z-index: 1002;
}
.dropdown-menu {
  color: #495057;
  border-color: #ced4da;
  background: #eff2f7;
  border-radius: 5px;
  height: 300px;
  overflow: scroll;
  width: 100%;
}

.dropdown-button {
  width: 100%;
  border: 1px solid;
}

.dropdown-item-active {
  color: white;
  background-color: #556ee6;
}

.subject-column {
  min-width: 180px;
}

.compatibility-column {
  width: 100px;
}

.penalty-column {
  width: 130px;
}

.note-column {
  min-width: 200px;
}

.no-column {
  min-width: 40px;
  width: 40px;
  position: sticky;
  left: 0px;
  margin-left: 0px;
  z-index: 2;
  &-after-race {
    min-width: 60px;
  }
}

.spinner-column {
  width: 5%;
}

.team-photo {
  width: 80px;
  height: 80px;
  margin-bottom: 8px;
}

.team-photo-border{
  display: flex;
  flex-direction: column;
  justify-content: center;  /* Centering y-axis */
  align-items :center; /* Centering x-axis */
  padding-right: 8px;
}

.disable{
  pointer-events: none;
}

::ng-deep .custom-swal2-popup {
  width: 50% ;
  min-width: 330px;
}

.have-rule {
  background-color: #EBEBEB;
}

.center {
  align-items: center;
}

:host ::ng-deep .columns {
  width: 150px !important;
}

:host ::ng-deep .no-columns {
  width: 50px !important;
}
.modal-body {
  /* 80% of window height */
  height: 200px;
}
::ng-deep .p-datatable .p-datatable-tbody > tr > td {
  text-align: left;
  border: 1px solid #e9ecef;
  border-width: 0 0 1px 0;
  padding: 13px 5px;
}
p {
  margin: auto;
}
