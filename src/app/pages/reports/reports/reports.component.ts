import { Component, OnInit } from '@angular/core';
import { RaceService } from 'src/app/core/services/race.service';
import { Router, ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-reports',
  templateUrl: './reports.component.html',
  styleUrls: ['./reports.component.scss']
})
export class ReportsComponent implements OnInit {


  // bread crumb items
  breadCrumbItems: Array<{}>;
  leaderBoard;
  sessionId;
  sessionType;
  raceList: any[] = [];
  selectedRaceId: string;
  constructor(private raceService: RaceService, private route: ActivatedRoute, private router: Router) { }

  ngOnInit() {
    this.breadCrumbItems = [{ label: '' }];
    this.getRaceList();
    this.route.queryParams.subscribe(params => {
      this.sessionId = params.id;
      this.selectedRaceId = this.sessionId;
      this.getRaceDetail();
    });
  }

  getRaceList() {
    this.raceService.publicRaceList().subscribe((res: any) => {
      if (res && res.data) {
        this.raceList = res.data;
        this.router.navigate([], {
          queryParams: { id : this.raceList[0].id },
          queryParamsHandling: 'merge',
      });
      }
    });
  }

  onRaceSelect(event: any) {
    const selectedId = event.target.value;
    this.router.navigate([], {
      queryParams: { id: selectedId },
      queryParamsHandling: 'merge',
    });
  }

  getRaceDetail() {
    if(!this.sessionId) return;
    this.raceService.getPublicRace(this.sessionId).subscribe(data => {
      this.leaderBoard = data['data'].scores;
      this.sessionType = data["data"].sessions?.[0]?.type;
    });
  }

}
