<div class="container-fluid">
  <div class="wrapper">
    <div class="list">
      <div class="list__header">
        <h5>Efficiency Challange</h5>
        <h1></h1>
        <div style="margin-top: 10px;">
          <select [ngModel]="selectedRaceId" (change)="onRaceSelect($event)">
            <option value="" disabled selected>Yarış Seçiniz</option>
            <option *ngFor="let race of raceList" [value]="race.id">{{ race.name }}</option>
          </select>
        </div>
      </div>
      <div class="list__body" *ngIf="leaderBoard">
        <table class="list__table">
          <tr class="list__row"
              [ngClass]="{
              'list__row-first': i === 0 && team.score > 0,
              'list__row-second': i === 1&& team.score > 0,
              'list__row-third': i === 2&& team.score > 0,
              'list__row-passive': !team.score

              }"

              *ngFor="let team of leaderBoard; let i = index;">
            <td class="list__cell"><span class="list__value">{{ team.score ? i + 1  :'-'}}</span></td>

            <td class="list__cell"><span class="list__value"><img class="avatar-custom"
                                                                  [src]="team.team.logo_url"></span><small
              class="list__label">LOGO</small></td>


            <td class="list__cell"><span
              class="list__value" style="font-weight:bold;">{{ team.team.team_name }}</span><small
              class="list__label">TEAM NAME</small>
            </td>
            <td class="list__cell"><span
              class="list__value">{{ team.team.university_name }}</span>
              <!--              <small class="list__label">{{ team.team.driver_name }}</small>-->
            </td>

            <td class="list__cell"><span
              class="list__value">{{ team.team.vehicle_number }}</span><small
              class="list__label">Araç No</small>
            </td>
            <td class="list__cell"><span
              class="list__value">{{ team.total_cons || 0 }}</span><small
              class="list__label">Tüketilen Enerji (Wh)</small></td>
            <td *ngIf="sessionType == 'hydromobile'" class="list__cell"><span
              class="list__value">{{ team.total_hydrogen_cons || 0 }}</span><small
              class="list__label">Toplam Hidrojen Tüketimi (lt)</small></td>
            <td class="list__cell"><span
              class="list__value">{{ team.penalty < 0 ? '+' : '' }}{{ team.penalty * -1 }}</span><small
              class="list__label">Ödül ( + ) / Ceza ( - )</small>
            </td>
            <td class="list__cell"><span
              class="list__value">{{ team.score || '-' }}</span><small
              class="list__label">Final Yarış Puanı</small></td>
          </tr>

        </table>
      </div>
    </div>
    <div class="warning-text">
      <p>Bu ekranda yer alan sonuçlar geçici olup, nihai sonuçlar resmi duyuru ile açıklanacaktır.</p>
    </div>
  </div>
</div>
