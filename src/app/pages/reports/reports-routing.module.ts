import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ReportsComponent } from './reports/reports.component';
import { RaceLeaderboardComponent } from './race-leaderboard/race-leaderboard.component';

const routes: Routes = [
  {
    path: 'reports',
    component: ReportsComponent
  },
  {
    path: 'reports/:id',
    component: ReportsComponent
  },
  {
    path: 'race-leaderboard/:id',
    component: RaceLeaderboardComponent
  }
];
@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ReportsRoutingModule { }
