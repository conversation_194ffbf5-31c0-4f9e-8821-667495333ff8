import { Component, OnInit } from '@angular/core';
import { RaceService } from 'src/app/core/services/race.service';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-race-leaderboard',
  templateUrl: './race-leaderboard.component.html',
  styleUrls: ['./race-leaderboard.component.scss']
})
export class RaceLeaderboardComponent implements OnInit {


 
  // bread crumb items
  breadCrumbItems: Array<{}>;
  leaderBoard;
  raceId; 
  constructor(private raceService: RaceService, private route: ActivatedRoute) { }

  ngOnInit() {
    this.breadCrumbItems = [{ label: '' }];
    this.route.params.subscribe(params => {
      this.raceId = params['id'];
      this.getRaceDetail();
    });
  
  }


  getRaceDetail() {
    this.raceService.getRace(this.raceId).subscribe(data => {
      this.leaderBoard = data["data"]["scores"];
    })
  }

}
