* {
  padding: 0;
  margin: 0;
  outline: none;
  box-sizing: border-box;
  font-family: var(--font-family);
}

:root {
  --font-family: "Poppins", Arial, sans-serif;
  --green-color: #4eae32;
}

.chart-container {
  padding: 1vw;
}

::ng-deep .nav-item.clicked .nav-link {
  background-color: #007bff;
  color: white;
}

::ng-deep p {
  font-size: 0.8rem !important;
  font-family: var(--font-family);
}

::ng-deep button {
  color: #000;
  text-decoration: none;
  font-size: 0.9rem !important;
  background-color: transparent;
}

::ng-deep .card-header {
  padding: 0.75rem 0.75rem;
}

::ng-deep .btn-accordion {
  width: 100%;
  display: flex;
}

::ng-deep .btn-link {
  color: #000;
}

::ng-deep .card-body {
  padding: 0;
}

#accordion thead {
  background: #f8f8f8;
}

.sub-title {
  color: #828282;
}

::ng-deep .table td,
::ng-deep .table th {
  border: 1px solid #dee2e6;
}

::ng-deep header img {
  margin-top: -4px;
}

::ng-deep .card {
  border: transparent 1px solid;
}

::ng-deep .card-header {
  background-color: transparent;
  border: 1px #4eae32 solid;
}

div#headingOne {
  background: #4eae32;
}

div#headingTwo {
  background: #4eae32;
}

div#headingThree {
  background: #4eae32;
}

div#headingFour {
  background-color: #ff3b3b;
  color: #fff;
}

#headingFour h6 {
  color: #fff;
}

#headingFour p {
  color: #fff;
}

div#headingFive {
  background: #f2f2f2;
  border: 1px solid transparent;
}

::ng-deep .btn-link:hover {
  color: #000;
  text-decoration: none;
}

::ng-deep .btn.focus,
.btn:focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem transparent;
}

::ng-deep .btn-link.focus,
.btn-link:focus {
  text-decoration: none;
}

::ng-deep .number-box {
  background: #4eae32 !important;
  padding: 10px;
  border-radius: 10px 10px 0 0;
  color: #fff;
}

::ng-deep .number {
  font-size: 2rem !important;
}

::ng-deep .green-title {
  color: #4eae32 !important;
}

::ng-deep .gray-line {
  background: #f8f8f8;
}

::ng-deep .gray-line li {
  color: #4eae32 !important;
  list-style: none;
}

::ng-deep .nav-link {
  color: #4eae32 !important;
}

::ng-deep .nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: #fff;
  border: none !important;
  background-color: transparent !important;
}

.active {
  border-bottom: 2px solid #1d1d1b !important;
}

::ng-deep .nav-pills li {
  border: none !important;
}

::ng-deep .nav-pills .nav-link {
  border-radius: 0;
}

::ng-deep .table thead th {
  border-bottom: 2px solid transparent !important;
}

::ng-deep .table td,
.table th {
  border: 1px solid transparent !important;
}

::ng-deep .table td,
.table th {
  border-bottom: 2px solid transparent !important;
}

::ng-deep .result-table th {
  font-weight: 500;
  font-size: 1rem;
}

::ng-deep .text-decoration-underline {
  text-decoration: underline;
}

::ng-deep .progress {
  height: 1.5rem;
  margin-top: 11px;
}

::ng-deep .progress-bar {
  background-color: #4eae32 !important;
}

canvas.myDonutChart {
  width: 80px !important;
  height: 40px !important;
}

canvas#myChart2 {
  width: 265px !important;
  height: 133px !important;
}
