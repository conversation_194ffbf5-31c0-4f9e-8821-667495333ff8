<link
  rel="stylesheet"
  href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css"
  integrity="sha384-xOolHFLEh07PJGoPkLv1IbcEPTNtaed2xpHsD9ESMhqIYd0nLMwNLD69Npy4HI+N"
  crossorigin="anonymous"
/>
<header>
  <img src="../../../../assets/images/inspection-detail/ec-line.png" class="w-100 p-0" alt=""/>
  <div class="container">
    <div class="row">
      <div class="col-md-12">
        <div class="d-flex justify-content-center">
          <img src="../../../../assets/images/inspection-detail/ec-logo.svg" alt=""/>
        </div>
        <div class="green-title mt-4 mb-4 d-flex justify-content-center">
          <h4 class="green-title">Tübitak Efficiency Challenge</h4>
        </div>
      </div>
    </div>
  </div>
</header>
<div *ngIf="!races" class="loading-container">
  <div class="spinner-border" role="status">
  </div>
</div>
<main>
  <nav class="navbar navbar-expand-lg navbar-light bg-light">
    <div class="container">
      <div class="navbar-collapse justify-content-center" id="navbarNav">
        <ul class="navbar-nav">
          <li
            class="nav-item"
            *ngFor="let race of races"
            (click)="selectRace(race.id)"
          >
            <a class="nav-link" href="javascript:void(0)" [class.active]="selectedRace === race.id">{{ race.name }}</a>
          </li>
        </ul>
      </div>
    </div>
  </nav>
  <section>
    <div class="container">
      <ul class="nav nav-pills mb-3" id="pills-tab" role="tablist">
        <li class="nav-item" role="presentation">
          <button
            class="nav-link active"
            id="pills-home-tab"
            data-toggle="pill"
            data-target="#pills-home"
            type="button"
            role="tab"
            aria-controls="pills-home"
            aria-selected="true"
          >
          </button>
        </li>
      </ul>
      <div class="tab-content" id="pills-tabContent">
        <div
          class="tab-pane fade show active"
          id="pills-home"
          role="tabpanel"
          aria-labelledby="pills-home-tab"
        >
          <h4 class="text-center green-title pt-4 pb-4">
            On-track results
          </h4>
          <div *ngIf="!raceDetail" class="loading-container">
            <div class="spinner-border" role="status">
            </div>
          </div>
          <table *ngIf="raceDetail" class="table table-striped result-table">
            <thead>
            <tr>
              <th scope="col">Rank</th>
              <th scope="col">No.</th>
              <th scope="col">Institute & team name</th>
              <th scope="col">Attempts</th>
              <th scope="col">Best result</th>
            </tr>
            </thead>
            <tbody>
            <tr *ngFor="let score of scores; let i = index">
              <th scope="row">{{i + 1}}</th>
              <td>{{score?.team?.vehicle_number}}</td>
              <td>
                <p class="mb-0">{{score?.team?.team_name}}</p>
                <p class="text-decoration-underline">
                  {{score?.team?.university_name}}
                </p>
              </td>
              <td></td>
              <td>{{score?.score}}</td>
            </tr>
            </tbody>
          </table>
        </div>
        <div
          class="tab-pane fade"
          id="pills-profile"
          role="tabpanel"
          aria-labelledby="pills-profile-tab"
        >
          ...
        </div>
        <div
          class="tab-pane fade"
          id="pills-contact"
          role="tabpanel"
          aria-labelledby="pills-contact-tab"
        >
          ...
        </div>
        <p class="text-center mt-5 mb-5">
          Note: All data contained within this page has not been formally
          verified, and therefore Tübitak is not responsible for the data
          accuracy. For informational purposes only.
        </p>
      </div>
    </div>
  </section>
  <div class="container">
    <div class="row"></div>
  </div>
</main>

<footer class="mt-4">
  <img src="../../../../assets/images/inspection-detail/bottom-line.svg" class="w-100" alt=""/>
</footer>
