import { Component, OnInit } from '@angular/core';
import { RaceService } from '../../../core/services/race.service';

@Component({
  selector: 'app-race-result',
  templateUrl: './race-result.component.html',
  styleUrls: ['./race-result.component.scss'],
})
export class RaceResultComponent implements OnInit {
  raceDetail: any;
  selectedRace: number | null = null;
  scores: any;
  races: any;

  constructor(private raceService: RaceService) {
  }

  ngOnInit(): void {
    this.selectedRace = 1;
    this.getRaces();
    this.getRaceDetail(this.selectedRace);
  }

  getRaces() {
    this.raceService.raceList().subscribe(races => {
      this.races = races['data'];
    });
  }

  selectRace(raceId: number) {
    this.selectedRace = raceId;
    this.getRaceDetail(raceId);
  }

  getRaceDetail(id: number) {
    this.raceDetail = null;
    this.raceService.getRace(id).subscribe(data => {
      this.raceDetail = data['data'];
      // this.raceDetail = data['data'];
      // this.sessions = data['data']['sessions'];
      this.scores = data['data']['scores'];
    });
  }
}
