<link
  rel="stylesheet"
  href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css"
  integrity="sha384-xOolHFLEh07PJGoPkLv1IbcEPTNtaed2xpHsD9ESMhqIYd0nLMwNLD69Npy4HI+N"
  crossorigin="anonymous"
/>

<header>
  <img src="../../../../assets/images/inspection-detail/ec-line.png" class="w-100 p-0" alt=""/>
  <div class="container">
    <div class="row">
      <div class="col-md-12">
        <div class="d-flex justify-content-center mb-4 mt-4">
          <img src="../../../../assets/images/inspection-detail/ec-logo.svg" alt=""/>
        </div>
      </div>
    </div>
  </div>
</header>
<div *ngIf="!groupedData" class="loading-container">
  <div class="spinner-border" role="status">
  </div>
</div>
<main>
  <!-- breadgrumb area -->
  <section>
    <div class="container">
      <div class="mt-2 mb-2">
        <img src="../../../../assets/images/inspection-detail/arrow-left.svg" alt=""/>
        <a href="/#/evaluation-results">Tübitak Efficiency Challenge</a>
      </div>
    </div>
  </section>

  <section>
    <div class="container">
      <div style="display: flex;">
        <div>
          <img
            *ngIf="teamPhoto; else defaultPhoto"
            [src]="apiUrl + 'storage/'+teamPhoto"
            alt="Team Photo"
            class="avatar-sm mr-4"
          />
          <ng-template #defaultPhoto>
            <img [src]="evalTeamDetails?.team?.logo_url || 'assets/images/companies/img-1.png'" alt="" class="avatar-sm mr-4" />
          </ng-template>
        </div>
        <div>
          <h6>{{teamNumber}} <strong>{{teamName}}</strong></h6>
          <p class="sub-title mb-0">{{teamUniversityName}}</p>
        </div>
      </div>
    </div>
  </section>

  <section>
    <div class="container">
      <div class="col-md-12">
        <div class="d-flex justify-content-end">
          <div class="col-md-11 col-10"></div>
          <div class="col-md-1 col-2 p-0">
            <div class="number-box">
              <p class="mb-0">{{teamVehicleNumber}}</p>
            </div>
          </div>
        </div>
        <img src="../../../../assets/images/inspection-detail/banner.png" class="w-100" alt=""/>
      </div>
      <div>
        <h6 class="mt-4 mb-2"><strong>Technical Inspections</strong></h6>
        <p>
          All vehicles must pass technical inspection. Those who fail at this stage cannot participate in the race.
        </p>
      </div>
      <div class="container d-flex justify-content-center flex-wrap">
        <div class="chart-custom">
          <canvas baseChart
                  [data]="chartData"
                  [labels]="chartCount"
                  [chartType]="chartType"
                  [options]="chartOptions"
                  [colors]="chartColors"
                  width="250" height="150">
          </canvas>
          <div class="donut-inner">
            <h5 class="d-flex">{{countData['Passed']}}/{{ groupedData?.length }}</h5>
            <h1 class="d-flex">{{this.teamStatus}}</h1>
          </div>
        </div>
      </div>
    </div>
  </section>
  <section>
    <div class="container">
      <div id="accordion">
        <div (click)="collapseClick(key)" *ngFor="let item of groupedData; let key = index" class="card">
          <div [ngClass]="{
              greenbox: calculatePassed(item[1]),
              redbox: isDisq(item[1]),
              greybox: !isDisq(item[1]) && !calculatePassed(item[1])
            }"
               class="card-header mb-1" id="headingOne">
            <h5 class="mb-0 col-md-12 d-flex p-0">
              <a
                class="btn btn-link btn-accordion"
                data-toggle="collapse"
                data-target="#collapseOne"
                aria-controls="collapseOne"
              >

                <div class="col-md-0 d-flex">
                  <img class="arrow-image" [@openClose]="gfg[key] ? 'open' : 'closed'"
                       src="../../../../assets/images/inspection-detail/arrow-y.svg" alt=""/>
                  <p class="number mt-1">{{key + 1}}.</p>
                </div>
                <div class="col-md-10">
                  <p class="ml-lg-2 mt-1 text-justify mb-1 pb-0">{{calculateStatus(item[1])}}</p>
                  <h6 class="ml-lg-1 text-justify">{{item[0]}}</h6>
                </div>
                <div class="col-md-1 d-flex justify-content-end align-items-center"
                     style="position: absolute; top: 0; right: 0;">
                  <div class=" mt-1">
                    {{calculatePassedCount(item[1], [1, 2]).length}}/{{item[1].length}}
                  </div>

                </div>
              </a>
            </h5>
          </div>
          <div
            [(ngbCollapse)]="!gfg[key]"
            id="collapseOne"
            class="collapse show"
            aria-labelledby="headingOne"
            data-parent="#accordion"
          >
            <div class="card-body">
              <table class="table">
                <tbody>
                <tr *ngFor="let subLine of groupThree(item[1])">
                  <td scope="col" *ngFor="let detail of subLine" class="line">
                    <div class="d-flex justify-content-between">
                      {{detail.criteria.subject}}

                      <img
                        src="../../../../assets/images/inspection-detail/{{getImageName(detail)}}"
                        class=""
                        alt=""
                        width="15"
                        height="15"
                      />

                    </div>
                  </td>
                </tr>
                </tbody>
                <tbody>
                <tr>
                  <td></td>
                  <td></td>
                </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      <p class="text-center mt-5 mb-5">
        Note: All data contained within this page has not been formally
        verified, and therefore Tübitak is not responsible for the data
        accuracy. For informational purposes only.
      </p>
    </div>
  </section>
</main>

<footer class="mt-4">
  <img src="../../../../assets/images/inspection-detail/bottom-line.svg" class="w-100" alt=""/>
</footer>

