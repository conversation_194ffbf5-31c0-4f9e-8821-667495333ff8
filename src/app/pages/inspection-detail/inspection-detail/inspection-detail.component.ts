import { Component, OnInit } from '@angular/core';
import { EvaluationService } from '../../../core/services/evaluation.service';
import { ActivatedRoute, Router } from '@angular/router';
import { groupBy } from 'lodash';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { environment } from '../../../../environments/environment';

@Component({
  selector: 'app-inspection-detail',
  templateUrl: './inspection-detail.component.html',
  styleUrls: ['./inspection-detail.component.scss'],
  animations: [
    trigger('openClose', [
      state('open', style({
        transform: 'rotate(90deg)',
      })),
      state('closed', style({
        transform: 'rotate(0)',
      })),
      transition('* => *', [
        animate('0.2s'),
      ]),
    ])
  ],
})
export class InspectionDetailComponent implements OnInit {
  evalTeamDetails: any;
  teamId: number;
  groupedData: any;
  teamVehicleNumber: any;
  teamUniversityName: any;
  teamName: any;
  teamNumber: any;
  gfg: boolean[] = [];
  chartData: any[] = [];
  chartCount: any[] = [];
  chartOptions: any;
  chartColors: any[];
  colorMapping: any;
  teamStatus: string;
  chartType = 'doughnut';
  countData = {};
  teamPhoto: string;
  apiUrl = environment.apiUrl;

  constructor(
    private evaluationService: EvaluationService, private router: Router, private route: ActivatedRoute,
  ) { }

  ngOnInit(): void {
    this.chartOptions = {
      animation: {
        duration: 1000,
        easing: 'easeInOutQuad',
      },
      rotation: -Math.PI,
      cutoutPercentage: 75,
      circumference: Math.PI,
      responsive: true,
      legend: {
        display: false,
        position: 'right',
        fullWidth: false,
        reverse: true,
      },
    };
    this.colorMapping = {
      'Passed': '#4EAE32',
      'In Progress': '#D9D9D9',
      'Disqualified': '#1D1D1B',
    };
    this.route.params.subscribe(params => {
      this.teamId = params['id'];
      this.getEvaluation();
    });
  }

  fillChart() {
    switch (this.evalTeamDetails.status) {
      case 'success':
        this.teamStatus = 'Passed';
        break;
      case 'fail':
        this.teamStatus = 'Disqualified';
        break;
      case 'active':
      case 'closed':
        this.teamStatus = 'In Progress';
        break;
    }
    this.chartData = [];
    this.countData = [];
    this.groupedData.forEach((data) => {
      this.chartData.push(this.calculateStatus(data[1]));
    });
    this.chartData.forEach(status => {
      if (this.countData[status]) {
        this.countData[status]++;
      } else {
        this.countData[status] = 1;
      }
    });
    if (this.teamStatus === 'Passed') {
      this.chartCount = ['Passed'];
      this.chartData = [this.countData['Passed']];
    } else {
      this.chartCount = Object.keys(this.countData);
      this.chartData = Object.values(this.countData);
    }

    this.chartColors = [
      {
        backgroundColor: this.chartCount.map(label => this.colorMapping[label]),
      },
    ];
  }

  getEvaluation() {
    this.evaluationService.getPublicEvaluations(this.teamId).subscribe(evaluation => {
      this.evalTeamDetails = evaluation['data'];
      this.teamVehicleNumber = this.evalTeamDetails.team.vehicle_number;
      this.teamUniversityName = this.evalTeamDetails.team.university_name;
      this.teamName = this.evalTeamDetails.team.team_name;
      this.teamNumber = this.evalTeamDetails.team.team_id;
      this.groupedData = groupBy(this.evalTeamDetails.details, item => item.criteria.category);
      this.groupedData = Object.entries(this.groupedData);
      this.teamPhoto = this.evalTeamDetails?.team?.photo?.asset?.path;
      // console.log(this.groupedData);
      this.fillChart();
    });
  }

  collapseClick(key: number) {
    this.gfg[key] = !this.gfg[key];
  }

  groupThree(lineData: any) {
    const grouped = [];
    lineData.forEach((data, index) => {
      const i: number = Math.floor(index / 2);
      if (!grouped[i]) {
        grouped[i] = [];
      }
      grouped[i].push(data);
    });
    return grouped;
  }

  isDisq(group: any[]) {
    return this.calculatePassedCount(group, [3]).length;
  }

  calculateStatus(group: any[]) {
    if (this.calculatePassedCount(group, [3]).length) {
      return 'Disqualified';
    }
    if (this.calculatePassedCountWithoutRequired(group, [1, 2]).length == group.length) {
      return 'Passed';
    }
    if (this.calculatePassedCount(group, [0]).length > 0) {
      return 'In Progress';
    }
    return this.calculatePassedCount(group, [3]).length;
  }

  calculatePassed(group: any[]) {
    return this.calculatePassedCountWithoutRequired(group, [1, 2]).length == group.length;
  }

  calculatePassedCount(group: any[], comp1 = [1]) {
    return group.filter(item => comp1.includes(item.compatibility));
  }

  calculatePassedCountWithoutRequired(group: any[], comp1 = [1]) {
    return group.filter(item => !item.is_required || comp1.includes(item.compatibility));
  }


  getImageName(detail: any) {
    if (detail.compatibility === 0) {
      return 'circle.svg';
    }
    if (detail.compatibility === 1) {
      return 'check-lg.svg';
    }
    if (detail.compatibility === 2) {
      if (detail?.criteria.no === '7.6') {
        return 'x-lg.svg';
      }
      return 'check-lg.svg';
    }
    if (detail.compatibility === 3) {
      return 'x-lg.svg';
    }
  }
}
