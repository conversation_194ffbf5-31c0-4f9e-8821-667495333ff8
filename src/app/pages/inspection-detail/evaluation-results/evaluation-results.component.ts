import { Component, OnInit } from '@angular/core';
import { EvaluationService } from '../../../core/services/evaluation.service';
import {environment} from '../../../../environments/environment';
import { Router } from '@angular/router';

@Component({
  selector: 'app-evaluation-results',
  templateUrl: './evaluation-results.component.html',
  styleUrls: ['./evaluation-results.component.scss']
})
export class EvaluationResultsComponent implements OnInit {
  evals: any;
  constructor(private evaluationService: EvaluationService,private router: Router) { }

  apiUrl = environment.apiUrl;

  ngOnInit(): void {
    this.getEvalList();
  }
  getEvalList() {
    this.evaluationService.getPublicEvalList('').subscribe(data => {
      this.evals = data['data'];
      this.evals.sort((a, b) => a?.team?.vehicle_number - b?.team?.vehicle_number);
      this.evals.forEach((evalItem: any) => {
        if (evalItem?.status == 'success'){
          evalItem.progress = 100;
        }
      });
    });
  }
  goToDetail(teamId: string) {
    this.router.navigate(['/inspection-detail', teamId]);
  }
}
