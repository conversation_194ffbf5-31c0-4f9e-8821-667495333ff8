<link
  rel="stylesheet"
  href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css"
  integrity="sha384-xOolHFLEh07PJGoPkLv1IbcEPTNtaed2xpHsD9ESMhqIYd0nLMwNLD69Npy4HI+N"
  crossorigin="anonymous"
/>

<header>
  <img src="../../../../assets/images/inspection-detail/ec-line.png" class="w-100 p-0" alt=""/>
  <div class="container">
    <div class="row">
      <div class="col-md-12">
        <div class="d-flex justify-content-center">
          <img src="../../../../assets/images/inspection-detail/ec-logo.svg" alt=""/>
        </div>
        <div *ngIf="evals" class="green-title mt-4 mb-4 d-flex justify-content-center">
          <h4 class="green-title">Tübitak Efficiency Challenge</h4>
        </div>
      </div>
    </div>
  </div>
</header>
<div *ngIf="!evals" class="loading-container">
  <div class="spinner-border" role="status">
  </div>
</div>
<main>
  <nav class="navbar navbar-expand-lg navbar-light bg-light">
    <div class="container">
      <div class="navbar-collapse justify-content-center" id="navbarNav">
        <ul class="navbar-nav">
          <li
            class="nav-item"
          >
            <a class="nav-link" href="javascript:void(0)"></a>
          </li>
        </ul>
      </div>
    </div>
  </nav>
  <section>
    <div class="container">
      <ul class="nav nav-pills mb-3" id="pills-tab" role="tablist">
        <li class="nav-item" role="presentation">
          <button
            class="nav-link active"
            id="pills-home-tab"
            data-toggle="pill"
            data-target="#pills-home"
            type="button"
            role="tab"
            aria-controls="pills-home"
            aria-selected="true"
          >
          </button>
        </li>
      </ul>
      <div class="tab-content" id="pills-tabContent">
        <div
          class="tab-pane fade show active"
          id="pills-home"
          role="tabpanel"
          aria-labelledby="pills-home-tab"
        >
          <h4 class="text-center green-title pt-4 pb-4">
            Technical inspection results
          </h4>
          <table class="table table-striped result-table">
            <thead>
            <tr>
              <th scope="col">Vehicle No.</th>
              <th scope="col">Team Id</th>
              <th scope="col">Team Photo</th>
              <th scope="col">Institute & team name</th>
              <th scope="col">Status</th>
              <th scope="col">Progress</th>
            </tr>
            </thead>
            <tbody>
            <tr style="cursor: pointer;" *ngFor="let item of evals; let i = index"
                (click)="goToDetail(item?.team?.team_id)"
            >
              <th scope="row">{{ item?.team?.vehicle_number }}</th>
              <td>{{ item?.team?.team_id }}</td>
              <td style="text-align: right;">
                <img
                  *ngIf="item?.team?.photo?.asset?.path; else defaultPhoto"
                  [src]="apiUrl + 'storage/'+item?.team?.photo?.asset?.path"
                  alt="Team Photo"
                  class="avatar-sm"
                />
                <ng-template #defaultPhoto>
                  <img [src]="item?.team?.logo_url || 'assets/images/companies/img-1.png'"
                                     alt="" class="avatar-sm" />
                </ng-template>
              </td>
              <td>
                <a href="/#/inspection-detail/{{item?.team?.team_id}}" class="mb-0"><h6>{{ item?.team?.team_name }}</h6>
                </a>
                <p>
                  {{ item?.team?.university_name }}
                </p>
              </td>
              <td>
                {{ item?.status == 'success' ? 'Passed' : 'In Progress' }}
              </td>

              <td>
                <div class="progress">
                  <div class="progress-bar" role="progressbar" [attr.aria-valuenow]="item?.progress"
                       [style.width.%]="item?.progress" aria-valuemin="0"
                       aria-valuemax="100">
                  </div>
                  <span class="progress-text">%{{ item?.progress }}</span>
                </div>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
        <div
          class="tab-pane fade"
          id="pills-profile"
          role="tabpanel"
          aria-labelledby="pills-profile-tab"
        >
          ...
        </div>
        <div
          class="tab-pane fade"
          id="pills-contact"
          role="tabpanel"
          aria-labelledby="pills-contact-tab"
        >
          ...
        </div>
        <p class="text-center mt-5 mb-5">
          Note: All data contained within this page has not been formally
          verified, and therefore Tübitak is not responsible for the data
          accuracy. For informational purposes only.
        </p>
      </div>
    </div>
  </section>
  <div class="container">
    <div class="row"></div>
  </div>
</main>

<footer class="mt-4">
  <img src="../../../../assets/images/inspection-detail/bottom-line.svg" class="w-100" alt=""/>
</footer>
