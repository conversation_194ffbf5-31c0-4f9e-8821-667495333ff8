import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { InspectionDetailComponent } from './inspection-detail/inspection-detail.component';
import { InspectionDetailRoutingModule } from './inspection-detail-routing.module';
import { RaceModule } from '../race/race.module';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { RaceResultComponent } from './race-result/race-result.component';
import { ChartsModule } from 'ng2-charts';
import { EvaluationResultsComponent } from './evaluation-results/evaluation-results.component';


@NgModule({
  declarations: [
    InspectionDetailComponent,
    RaceResultComponent,
    EvaluationResultsComponent
  ],
  imports: [
    CommonModule,
    InspectionDetailRoutingModule,
    RaceModule,
    NgbModule,
    ChartsModule,
  ],
})
export class InspectionDetailModule {}
