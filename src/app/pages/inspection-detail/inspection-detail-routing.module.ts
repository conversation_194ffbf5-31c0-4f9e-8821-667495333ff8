import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { InspectionDetailComponent } from './inspection-detail/inspection-detail.component';
import { RaceResultComponent } from './race-result/race-result.component';
import { EvaluationResultsComponent } from './evaluation-results/evaluation-results.component';

export const routes: Routes = [
  {
    path: 'inspection-detail/:id',
    component: InspectionDetailComponent,
  },
  {
    path: 'race-result',
    component: RaceResultComponent,
  },
  {
    path: 'evaluation-results',
    component: EvaluationResultsComponent,
  }
];


@NgModule({
  imports: [RouterModule.forChild(routes)],

})
export class InspectionDetailRoutingModule {}
