import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { DefaultComponent } from './dashboards/default/default.component';
import { TeamComponent } from './team/team/team.component';
import { AppointmentComponent } from './appointment/appointment/appointment.component';
import { EvaluationComponent } from './evaluation/evaluation/evaluation.component';
import { RaceComponent } from './race/race/race.component';
import { ReportsComponent } from './reports/reports/reports.component';
import { CriteriasComponent } from './settings/criterias/criterias.component';
import { TechnicalDesignComponent } from './technical-design/technical-design/technical-design.component';
import { PasswordresetComponent } from '../account/auth/passwordreset/passwordreset.component';
import { ReportsDetailComponent } from './reports-detail/reports-detail.component';

const routes: Routes = [
  { path: '', redirectTo: 'dashboard' },
  { path: 'dashboard', component: DefaultComponent },
  { path: 'team', component: TeamComponent },
  { path: 'appointment', component: AppointmentComponent },
  { path: 'evaluation', component: EvaluationComponent },
  { path: 'race', component: RaceComponent },
  { path: 'reports', component: ReportsComponent },
  { path: 'reports-detail', component: ReportsDetailComponent },
  { path: 'criteria', component: CriteriasComponent },
  { path: 'technical-design', component: TechnicalDesignComponent },
  { path: 'reset-password', component: PasswordresetComponent },
  { path: 'public-reports', loadChildren: () => import('./public-reports/public-reports.module').then(m => m.PublicReportsModule) },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class PagesRoutingModule { }
