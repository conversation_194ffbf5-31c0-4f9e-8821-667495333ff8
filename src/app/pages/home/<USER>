html, body, app-root {
  height: 100%;
}
:host {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  height: 100vh;
}
header .green-title {
  color: #4eae32;
  //font-weight: 700;
  font-size: 24px;
}

// Removed old styles - replaced with new minimalist design
.main-center-area {
  flex: 1 0 auto;
  min-height: 50vh;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin: 0 auto;
  flex: 1;
  height: calc(100vh - 250px);
  padding: 2rem 1rem;
}

.welcome-container {
  width: 100%;
  max-width: 900px;
  margin: 0 auto;
}

.welcome-section {
  text-align: center;
  margin-bottom: 3rem;

  .welcome-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #4eae32, #34c38f);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    box-shadow: 0 8px 32px rgba(78, 174, 50, 0.3);

    i {
      font-size: 2rem;
      color: white;
    }
  }

  .welcome-title {
    font-size: 2.5rem;
    font-weight: 600;
    color: #2a3042;
    margin-bottom: 0.5rem;
    letter-spacing: -0.02em;
  }

  .welcome-subtitle {
    font-size: 1.1rem;
    color: #74788d;
    margin: 0;
    line-height: 1.6;
    max-width: 500px;
    margin: 0 auto;
  }
}

.action-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.action-card {
  background: white;
  border-radius: 1.5rem;
  padding: 2rem;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #4eae32, #34c38f);
  }

  .card-icon {
    width: 60px;
    height: 60px;
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;

    i {
      font-size: 1.5rem;
    }
  }

  .card-title {
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #2a3042;
  }

  .card-description {
    color: #74788d;
    margin-bottom: 1.5rem;
    line-height: 1.6;
  }

  .card-button {
    display: inline-flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;

    &:hover {
      text-decoration: none;
      transform: translateX(4px);
    }
  }
}

.primary-card {
  .card-icon {
    background: rgba(78, 174, 50, 0.1);
    color: #4eae32;
  }

  .card-button {
    background: #4eae32;
    color: white;

    &:hover {
      background: #388e1f;
      color: white;
    }
  }
}

.secondary-card {
  .card-icon {
    background: rgba(85, 110, 230, 0.1);
    color: #556ee6;
  }

  .card-button {
    background: rgba(85, 110, 230, 0.1);
    color: #556ee6;

    &:hover {
      background: #556ee6;
      color: white;
    }
  }
}

.quick-stats {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 1.5rem;
  padding: 1.5rem;
  border: 1px solid #e9ecef;

  .stat-item {
    display: flex;
    align-items: center;
    justify-content: center;

    .stat-icon {
      width: 50px;
      height: 50px;
      background: linear-gradient(135deg, #f1b44c, #f1734f);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 1rem;

      i {
        font-size: 1.2rem;
        color: white;
      }
    }

    .stat-content {
      display: flex;
      flex-direction: column;

      .stat-label {
        font-size: 0.9rem;
        color: #74788d;
        margin-bottom: 0.25rem;
      }

      .stat-value {
        font-size: 1.1rem;
        font-weight: 600;
        color: #2a3042;
      }
    }
  }
}
@media (max-width: 768px) {
  .main-center-area {
    padding: 1rem;
    height: auto;
    min-height: calc(100vh - 200px);
  }

  .welcome-section {
    margin-bottom: 2rem;

    .welcome-title {
      font-size: 2rem;
    }

    .welcome-subtitle {
      font-size: 1rem;
    }

    .welcome-icon {
      width: 60px;
      height: 60px;

      i {
        font-size: 1.5rem;
      }
    }
  }

  .action-cards {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  .action-card {
    padding: 1.5rem;

    .card-title {
      font-size: 1.2rem;
    }

    .card-icon {
      width: 50px;
      height: 50px;

      i {
        font-size: 1.2rem;
      }
    }
  }

  .quick-stats {
    padding: 1rem;

    .stat-item {
      .stat-icon {
        width: 40px;
        height: 40px;

        i {
          font-size: 1rem;
        }
      }

      .stat-content {
        .stat-label {
          font-size: 0.8rem;
        }

        .stat-value {
          font-size: 1rem;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .welcome-container {
    padding: 0 0.5rem;
  }

  .welcome-section {
    .welcome-title {
      font-size: 1.8rem;
    }
  }

  .action-card {
    padding: 1.2rem;

    .card-button {
      padding: 0.6rem 1.2rem;
      font-size: 0.9rem;
    }
  }
}
footer {
  flex-shrink: 0;
  width: 100%;
  margin-top: auto;
}
