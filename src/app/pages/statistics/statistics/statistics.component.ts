import { Component, OnInit } from '@angular/core';
import { NgxSpinnerService } from 'ngx-spinner';
import { Table } from 'primeng/table';
import { StatisticsService } from 'src/app/core/services/statistics.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-statistics',
  templateUrl: './statistics.component.html',
  styleUrls: ['./statistics.component.scss']
})
export class StatisticsComponent implements OnInit {

  compatibilitiesAnalysis= [0,0,0,0];
  criteriasAnalysis = [];
  breadCrumbItems: Array<{}>;
  allCategories = [];
  showCriteriaByCategory = [];
  selectedCategory: string;
  loading = true;

  constructor(private statisticsService: StatisticsService, private spinner: NgxSpinnerService ) { }

  ngOnInit(): void {
    this.getEvaluationAnalysis();

    this.breadCrumbItems = [{ label: 'Statistics', active: true }];
  }

  getEvaluationAnalysis() {
    this.statisticsService.getStatistics().subscribe(
      (response: any) => {
        response.compatibilities.map(comp => { comp.compatibility? false: comp.compatibility = 0})
        response.compatibilities.forEach(comp => {
          this.compatibilitiesAnalysis[comp.compatibility] += comp.eval_ids.split(',').length;
        });
        this.setAnalysisByCriteria(response.criterias);
      },
      (error) => {
        console.log(error);
      }
    );
  }

  setAnalysisByCriteria(criterias) {
    const categories = new Set<string>();
    criterias.map(criteria => {
      criteria.compatibilities? false: criteria.compatibilities = "0";
      criteria.compatibilities = criteria.compatibilities?.split(',').map(Number);
      criteria.subject = criteria.criteria.subject;
      criteria.content = criteria.criteria.content;
      criteria.total = criteria.compatibilities?.length;
      criteria.bos = criteria.compatibilities?.filter(item => item === 0).length;
      criteria.uygun = criteria.compatibilities?.filter(item => item === 1).length;
      criteria.kusurlu = criteria.compatibilities?.filter(item => item === 2).length;
      criteria.disk = criteria.compatibilities?.filter(item => item === 3).length;
      categories.add(criteria.criteria.category);
    })
    this.allCategories = Array.from(categories);
    this.selectedCategory = this.allCategories[0];
    this.criteriasAnalysis = criterias;
    this.onCategoryChange(this.selectedCategory);
  }

  calculateCustomerTotal(category) {
    let total = 0;

    if (this.criteriasAnalysis) {
        for (let criteria of this.criteriasAnalysis) {
            if (criteria.category === category) {
                total++;
            }
        }
    }

    return total;
}

  exportEvaluationAnalysis() {
    this.spinner.show();
    this.statisticsService.exportStatistics().subscribe(data => {
      console.log(data);
      this.spinner.hide();
      // alert('Exported');
      this.downLoadFile(data, 'application/ms-excel', 'EC_statistics.xlsx');
    }, error => {
      this.spinner.hide();
      this.position(error['message']);
    });
  }

  position(title) {
    Swal.fire({
      position: 'center',
      icon: 'error',
      title: title,
      timer: 1500,
      allowOutsideClick: false,
      showConfirmButton: true,
      showCancelButton: false,
    })
      .then((willDelete) => {
      });
  }

  downLoadFile(data: any, type: string, filename) {
    let blob = new Blob([data], { type: type });

    let url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.click();
  }

  onCategoryChange(category) {
    this.loading = true;
    this.showCriteriaByCategory =this.criteriasAnalysis
      .filter(item => item.criteria.category === category);
    setTimeout(() => {
      this.loading = false;
    }, 400);
  }

  clear(table: Table) {
    table.clear();
}

}
