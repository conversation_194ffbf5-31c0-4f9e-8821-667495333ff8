<div class="container-fluid">
  <!-- start page title -->
  <div class="row">
    <div class="col-12">
      <div class="text-center my-3">
        <a
          (click)="exportEvaluationAnalysis()"
          class="btn btn-success inner ml-3"
        >
          Export Statistics
        </a>
      </div>
    </div>
    <!-- end col-->
  </div>
  <app-page-title
    title="Statistics"
    [breadCrumbItems]="breadCrumbItems"
  ></app-page-title>

  <div class="card">
    <div class="card">
      <div class="card-header">
        <h4 class="card-title">Compatibilities</h4>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-lg-3 col-md-3 col-sm-6">
            <div class="card">
              <div class="card-header">
                <h4 class="card-title">
                  <i class="fas fa-chart-pie"></i>
                  Bos
                </h4>
              </div>
              <div class="card-body">
                {{ compatibilitiesAnalysis[0] }}
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-3 col-sm-6">
            <div class="card">
              <div class="card-header">
                <h4 class="card-title">
                  <i class="fas fa-chart-pie"></i>
                  Uygun
                </h4>
              </div>
              <div class="card-body">
                {{ compatibilitiesAnalysis[1] }}
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-3 col-sm-6">
            <div class="card">
              <div class="card-header">
                <h4 class="card-title">
                  <i class="fas fa-chart-pie"></i>
                  Kusurlu
                </h4>
              </div>
              <div class="card-body">
                {{ compatibilitiesAnalysis[2] }}
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-3 col-sm-6">
            <div class="card">
              <div class="card-header">
                <h4 class="card-title">
                  <i class="fas fa-chart-pie"></i>
                  Diskalifiye
                </h4>
              </div>
              <div class="card-body">
                {{ compatibilitiesAnalysis[3] }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="card">
    <div class="card">
      <div class="card-header">
        <h4 class="card-title">Criterias</h4>
      </div>
      <div class="card-body">
        <p-table
          #criterias
          [value]="showCriteriaByCategory"
          [paginator]="true"
          [rows]="10"
          [loading]="loading"
          [showCurrentPageReport]="true"
          [scrollable]="true" scrollHeight="400px"
          currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
          [rowsPerPageOptions]="[10, 25, 50]"
          [globalFilterFields]="['criteria.subject', 'criteria.content']"
          >
          <ng-template pTemplate="caption">
            <div class="flex">
              <label class="mr-2"> Category: </label>
              <p-dropdown [options]="allCategories" (onChange)="onCategoryChange($event.value)"
              [(ngModel)]="selectedCategory" [styleClass]="'col-3'"
              [filter]="true">
                <ng-template pTemplate="selectedItem">
                    <div class="country-item country-item-value"  *ngIf="selectedCategory">
                      <div>{{selectedCategory}}</div>
                    </div>
                </ng-template>
                <ng-template let-category pTemplate="item">
                    <div class="country-item">
                      <div>{{category}}</div>
                    </div>
                </ng-template>
            </p-dropdown>
            <button
            pButton
            label="Clear"
            class="p-button-outlined ml-2"
            icon="pi pi-filter-slash"
            (click)="clear(criterias); globalFilter.value = null"
          ></button>
          <span class="p-input-icon-left ml-auto" style="float: right">
            <i class="pi pi-search"></i>
            <input
              #globalFilter
              pInputText
              type="text"
              (input)="
                criterias.filterGlobal($event.target.value, 'contains')
              "
              placeholder="Search criteria"
            />
          </span>
            </div>
          </ng-template>
          <ng-template pTemplate="header">
            <tr>
              <th pSortableColumn="criteria.subject" class="criteria">Criteria <p-sortIcon field="criteria.subject"></p-sortIcon></th>
              <th pSortableColumn="total" class="columns">Total <p-sortIcon field="total"></p-sortIcon></th>
              <th pSortableColumn="bos" class="columns">Empty <p-sortIcon field="bos"></p-sortIcon></th>
              <th pSortableColumn="uygun" class="columns">Approve <p-sortIcon field="uygun"></p-sortIcon></th>
              <th pSortableColumn="kusurlu" class="columns">Defective <p-sortIcon field="kusurlu"></p-sortIcon></th>
              <th pSortableColumn="disk" class="columns">Disqualified <p-sortIcon field="disk"></p-sortIcon></th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-criteria>
            <tr>
              <td class="criteria">{{ criteria.subject }}
                <h6> {{ criteria.content }}</h6>
              </td>
              <td class="columns">{{ criteria.total }}</td>
              <td class="columns">{{ criteria.bos }}</td>
              <td class="columns">{{ criteria.uygun }}</td>
              <td class="columns">{{ criteria.kusurlu }}</td>
              <td class="columns">{{ criteria.disk }}</td>
            </tr>
          </ng-template>
        </p-table>
      </div>
    </div>
  </div>
</div>
<ngx-spinner type="ball-clip-rotate-multiple" size="medium"> </ngx-spinner>