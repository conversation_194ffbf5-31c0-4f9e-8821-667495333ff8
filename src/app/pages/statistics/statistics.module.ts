import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { StatisticsRoutingModule } from './statistics-routing.module';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { PaginatorModule } from 'primeng/paginator';
import { UIModule } from '../../shared/ui/ui.module';
import { StatisticsComponent } from './statistics/statistics.component';
import { InputTextModule } from 'primeng/inputtext';
import { DropdownModule } from 'primeng/dropdown';
import { NgxSpinnerModule } from 'ngx-spinner';


@NgModule({
  declarations: [StatisticsComponent],
  imports: [
    CommonModule,
    StatisticsRoutingModule,
    TableModule,
    ButtonModule,
    InputTextModule,
    DropdownModule,
    UIModule,
    PaginatorModule,
    NgxSpinnerModule
  ]
})
export class StatisticsModule { }
