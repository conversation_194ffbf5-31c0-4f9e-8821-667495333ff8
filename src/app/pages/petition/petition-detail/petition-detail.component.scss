.img-custom {
  width: 100%;
  max-height: 630px;
  object-fit: contain;
}

:host ::ng-deep .carousel-control-next, ::ng-deep .carousel-control-prev{
  position: absolute;
    width: 50px;
    height: 50px;
    background-color: black;
    opacity: .5;
    border-radius: 10px;
    color: black;
    top: 50%;
    transform: translate(0, -50%);
}

@media (min-width: 0) {
  .g-mr-15 {
      margin-right: 1.07143rem !important;
  }
}
@media (min-width: 0){
  .g-mt-3 {
      margin-top: 0.21429rem !important;
  }
}

.g-height-50 {
  height: 50px;
}

.g-width-50 {
  width: 50px !important;
}

@media (min-width: 0){
  .g-pa-30 {
      padding: 2.14286rem !important;
  }
}

.g-bg-secondary {
  background-color: #fafafa !important;
}

.u-shadow-v18 {
  box-shadow: 0 5px 10px -6px rgba(0, 0, 0, 0.15);
}

.g-color-gray-dark-v4 {
  color: #777 !important;
}

.g-font-size-12 {
  font-size: 0.85714rem !important;
}

.g-font-size-10 {
  font-size: 0.70rem !important;
}

.media-comment {
  margin-top:20px
}

.like-btn {
  width: 65px;
  display: inline-flex;
  height: 38px;
  background-color: #34c38f;
  align-items: center;
  justify-content: space-evenly;
  border: 1px solid #ededed;
  border-radius: 9px;
  color:#FFF !important;
}

.like-btn-dis {
  background-color: #f46a6a !important;
}

.delete-btn {
  margin-left: auto;
}

.delete-btn-icon {
    font-size: 20px;
    color: #f46a6a;
    margin-top: 0.5rem;
  }

::ng-deep .carousel-item{
  display:none !important;
}

::ng-deep .carousel-item.active{
  display:block !important;
}

::ng-deep .full-screen-petition-modal .modal-dialog { 
  width: 100%;
  max-width: none;
  align-items: center;
  margin: 0 0 0 0;
}

::ng-deep .full-screen-petition-modal .modal-content { 
  background: transparent;
  border: none;
}

::ng-deep .full-screen-petition-modal .modal-header {
  border: none;
  margin-right: 1rem;
}

::ng-deep .full-screen-petition-modal .modal-body {
  margin: auto;
}

.full-screen-petition-image {
  max-width: 100%;
  max-height: 90vh;
}

.modal-close-icon {
  color: white;
  font-size: 24px;
  opacity: 1 !important;
}