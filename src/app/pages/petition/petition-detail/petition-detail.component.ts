import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { NgxSpinnerService } from 'ngx-spinner';
import { PetitionService } from 'src/app/core/services/petition.service';
import { LoginService } from 'src/app/core/services/login.service';
import { environment } from 'src/environments/environment';
import Swal from 'sweetalert2';
import { User } from 'src/app/core/models/auth.models';
import { AuthfakeauthenticationService } from '../../../core/services/authfake.service';

@Component({
  selector: 'app-petition-detail',
  templateUrl: './petition-detail.component.html',
  styleUrls: ['./petition-detail.component.scss']
})
export class PetitionDetailComponent implements OnInit {

  petitionId: number;
  petitionDetail;
  apiUrl = environment.apiUrl;
  breadCrumbItems: Array<{}>
  commentTextArea = ''
  clickedImagePath = '';
  currentUser: User;
  voted: boolean = false;

  constructor(
    private route: ActivatedRoute,
    private petitionService: PetitionService,
    private loginService: AuthfakeauthenticationService,
    private modalService: NgbModal,
    private spinner: NgxSpinnerService,
    private router: Router
  ) { }

  ngOnInit(): void {
    this.currentUser = this.loginService.currentUserValue.user;
    this.breadCrumbItems = [{ label: 'Petitions' }, { label: 'Petition Detail', active: true }];
    this.route.params.subscribe(params => {
      this.petitionId = params['id'];
      this.getPetitionDetail();
    });
  }

  getPetitionDetail() {
    this.spinner.show();
    this.petitionService.getPetition(this.petitionId).subscribe((response: any) => {
      this.petitionDetail = response.data;
      this.voted = this.petitionDetail.like == 1 || this.petitionDetail.hesistant == 1 || this.petitionDetail.dislike == 1;
      this.spinner.hide();
    }, error => {
      this.spinner.hide();
      this.position('Error', error['message'], 'OK', 'error');
    });
  }

  getPath(path: string) {
    return `${this.apiUrl}storage/${path}`;
  }

  commentLike(commentId: number){
    this.petitionService.commentLike(commentId).subscribe((response: any) => {
      this.setCommentLikeAndDislike(response.data);
    });
  }

  commentDislike(commentId: number) {
    this.petitionService.commentDislike(commentId).subscribe((response: any) => {
      this.setCommentLikeAndDislike(response.data);
    });
  }

  setCommentLikeAndDislike(_comment) {
    const commentIndex = this.petitionDetail?.comments.findIndex(comment => comment.id == _comment.id);
    this.petitionDetail.comments[commentIndex].like = _comment.like;
    this.petitionDetail.comments[commentIndex].dislike = _comment.dislike;
    //this.petitionDetail.comments = [...this.petitionDetail._comments]
  }

  removeComment(comment) {
    Swal.fire({
      title: 'Are you sure to delete this comment?',
      text: 'It will delete this comment.',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#34c38f',
      cancelButtonColor: '#f46a6a',
      confirmButtonText: 'Yes, delete.',
    }).then((result) => {
      if (result.value) {
        this.commentDelete(comment);
      }
    });
  }

  commentDelete(comment){
    if(this.currentUser.id === comment.commented.id){
      this.spinner.show();
      this.petitionService.commentDelete(comment.id).subscribe((response: any) => {
        this.spinner.hide();
        this.petitionDetail.comments = this.petitionDetail.comments.filter(c => c.id !== comment.id);
        //this.petitionDetail.comments = [...this.petitionDetail.comments];
      }, error =>{
        this.spinner.hide();
        this.position('Error', error['message'], 'OK', 'error');
      });
    }else {
      this.position('Error', 'You can only delete yourself comment', 'OK', 'error');
    }
  }

  petitionApprove() {
    this.petitionService.petitionApprove(this.petitionDetail.id).subscribe((response: any) => {
      this.setPetitionRejectAndApprove(response.data);
    });
  }

  petitionReject() {
    this.petitionService.petitionReject(this.petitionDetail.id).subscribe((response: any) => {
      this.setPetitionRejectAndApprove(response.data);
    });
  }

  petitionHesitant() {
    this.petitionService.petitionHesitant(this.petitionDetail.id).subscribe((response: any) => {
      this.setPetitionRejectAndApprove(response.data);
    });
  }

  setPetitionRejectAndApprove(updatedPetition) {
    this.voted = true;
    this.petitionDetail.like = updatedPetition.like;
    this.petitionDetail.dislike = updatedPetition.dislike;
    this.petitionDetail.hesitant = updatedPetition.hesitant;
  }

  addCommentToPetition() {
    this.spinner.show();
    this.petitionService.addCommentToPetition(this.petitionId, this.commentTextArea).subscribe((response: any) => {
      this.spinner.hide();
      const newComment = {
        commented: response.data.user,
        id: response.data.id,
        created_at: response.data.created_at,
        comment: response.data.comment,
        like: 0,
        dislike: 0
      };
      this.petitionDetail.comments.push(newComment);
      this.commentTextArea = '';
    });
  }

  openImageModal(modal, imagePath){
    this.clickedImagePath = this.getPath(imagePath);
    this.modalService.open(modal, { windowClass: 'full-screen-petition-modal' });
  }

  closePetitionConfirm() {
    Swal.fire({
      title: 'Are you sure to close this petition?',
      text: 'It will close this petition.',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#34c38f',
      cancelButtonColor: '#f46a6a',
      confirmButtonText: 'Yes, Close.',
    }).then((result) => {
      if (result.value) {
        this.closePetition();
      }
    });
  }

  closePetition(){
    this.spinner.show();
    const updatedPetition = {
      id: this.petitionDetail.id,
      team_id: this.petitionDetail.team_id,
      session_id: this.petitionDetail.session_id,
      active: 0
    }
    this.petitionService.updatePetition(this.petitionDetail.id, updatedPetition).subscribe((response: any) =>{
      this.spinner.hide();
      this.router.navigate(['petitions']);
    }, error => {
      this.position('Error', error['message'], 'OK', 'error');
    })
  }

  ifCheckSuperAdmin(){
    const currentUser = JSON.parse(localStorage.getItem('currentUser'));
    if (currentUser['user'] && currentUser['user']['role'] && currentUser['user']['role'].includes('superadmin')) {
      return true;
    }
    return false;
  }

  position(title, text, confirmText, icon, dismissAll = true) {
    Swal.fire({
      title: title,
      html: text,
      icon: icon,
      confirmButtonColor: '#34c38f',
      cancelButtonColor: '#f46a6a',
      confirmButtonText: confirmText,
    }).then(result => {
      if (result.value && dismissAll) {
        this.modalService.dismissAll();
      }
    });
  }
}
