import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { PetitionComponent } from './petition/petition.component';
import { PetitionDetailComponent } from './petition-detail/petition-detail.component'

const routes: Routes = [
    {
      path: 'petitions',
      component: PetitionComponent
    },
    {
      path: 'petition/detail/:id',
      component: PetitionDetailComponent
    }
]
@NgModule({
    imports: [RouterModule.forChild(routes)],
    
  })
  export class PetitionRoutingModule { }