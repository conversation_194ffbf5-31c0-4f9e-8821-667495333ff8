import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { PetitionComponent } from './petition/petition.component'
import { PetitionRoutingModule } from './petition-routing.module'
import { NgxSpinnerModule } from "ngx-spinner";
import { NgxDatatableModule } from "@swimlane/ngx-datatable";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { SharedModule } from "primeng/api";
import { UIModule } from "src/app/shared/ui/ui.module";
import { PetitionDetailComponent } from './petition-detail/petition-detail.component';
import { RouterModule } from "@angular/router";
import { NgbCarouselModule } from "@ng-bootstrap/ng-bootstrap";
import { TranslateModule } from "@ngx-translate/core";
import { TableModule } from "primeng/table";
import { InputTextModule } from "primeng/inputtext";
import { ButtonModule } from "primeng/button";

@NgModule({
    declarations: [PetitionComponent, PetitionDetailComponent],
    imports: [
        CommonModule,
        PetitionRoutingModule,
        NgxSpinnerModule,
        NgxDatatableModule,
        FormsModule,
        ReactiveFormsModule,
        SharedModule,
        UIModule,
        RouterModule,
        NgbCarouselModule,
        TranslateModule,
        TableModule,
        InputTextModule,
        ButtonModule
    ]
})

export class PetitionModule {}