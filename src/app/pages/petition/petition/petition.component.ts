import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { NgxSpinnerService } from 'ngx-spinner';
import { TeamsService } from 'src/app/core/services/teams.service';
import Swal from 'sweetalert2';
import { PetitionService } from '../../../core/services/petition.service';
import { AssetService } from '../../../core/services/asset.service';
import { Observable } from 'rxjs';
import { RaceService } from 'src/app/core/services/race.service';
import { finalize, map } from 'rxjs/operators';
import { Table } from 'primeng/table';

@Component({
  selector: 'app-petition',
  templateUrl: './petition.component.html',
  styleUrls: ['./petition.component.scss']
})
export class PetitionComponent implements OnInit {

  petitionList: any[] = [];
  breadCrumbItems: Array<{}>
  loading: boolean = false;
  reorderable: boolean = true;
  title = []; //team select
  petitionForm: FormGroup;
  petitionFiles = []; //yüklenen dosyalar
  finishedUpload = true; //dosya yüklemesi bitmeden buton aktifleşmesin
  sessions$: Observable<any>;
  onlyActivePetition = false;
  filterButtonLabel = "table._show_only_active";
  instutitions = [
    'Tübitak',
    'Tosfed'
  ];

  subjects = [
    'Centilmenlik',
    'Tur Sayısı',
    'Ödül/Ceza',
    'Dinamik sürüş testi',
    'Fren testi',
    'Motor',
    'Motor sürücüsü',
    'Batarya yönetim sistemi',
    'Yerleşik şarj birimi',
    'Enerji yönetim sistemi',
    'Batarya paketleme',
    'Elektronik diferansiyel uygulaması',
    'Telemetri',
    'Araç kontrol sistemi',
    'İzolasyon izleme cihazı',
    'Direksiyon sistemi',
    'Kapı mekanizması',
    'Fren sistemi',
    'Mekanik detaylar',
    'Hidrojen sistemi',
    'Diğer'
  ];
  sessionValues = [
    '1.Gün Teknik kontrolleri',
    '2.Gün Teknik kontrolleri',
    '3.Gün Teknik kontrolleri',
    '1.final yarışı 1.oturum',
    '1.final yarışı 2.oturum',
    '2.final yarışı 1.oturum',
    '2.final yarışı 2.oturum',
  ];

  constructor(
    private petitionService: PetitionService,
    private spinner: NgxSpinnerService,
    private modalService: NgbModal,
    private teamService: TeamsService,
    private formBuilder: FormBuilder,
    private assetService: AssetService,
    private raceService: RaceService
  ) {
  }

  ngOnInit(): void {
    this.getTeams();
    this.getSession();
    this.breadCrumbItems = [{ label: 'Petitions' }, { label: 'Petition List', active: true }];
    this.getAllPetitions();
    this.petitionForm = this.formBuilder.group({
      team_id: ['', Validators.required],
      content: ['', Validators.required],
      session_name: ['', Validators.required],
      target: ['', Validators.required],
      attachments: [],
      type: ['', Validators.required]
    });
  }

  getTeams() {
    this.teamService.getTeams({ limit: 300 }).subscribe((teams: any) => {
      teams.data.forEach(teamsData => {
        this.title.push({
          id: teamsData.id,
          vehicle_number: teamsData.vehicle_number,
          team_name: `${teamsData.university_name} : ${teamsData.team_name} - ${teamsData.vehicle_category} `,
        });
      });

    });
  }

  getSession() {
    this.sessions$ = this.raceService.getAllRaceSession().pipe(map((response: any) => response.data));
  }

  getAllPetitions(){
    this.loading = true;
    this.spinner.show();
    this.petitionService.getAllPetitions().pipe(finalize(() => this.loading = false)).subscribe((response: any)=> {
      this.spinner.hide();
      this.petitionList = response.data;
    });
  }

  removePetition(petitionId) {
    Swal.fire({
      title: 'Are you sure to delete this Petition?',
      text: 'It will delete this pptition.',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#34c38f',
      cancelButtonColor: '#f46a6a',
      confirmButtonText: 'Yes, delete.',
    }).then((result) => {
      if (result.value) {
        this.deletePetition(petitionId);
      }
    });
  }

  deletePetition(petitionId: number) {
    this.spinner.show();
    this.petitionService.deletePetition(petitionId).subscribe(response => {
      this.spinner.hide();
      this.position('Petition Successfully Deleted', 'Petition Deleted', 'OK', 'success');
      this.getAllPetitions();
    }, error => {
      this.spinner.hide();
      this.position('Error', error['message'], 'OK', 'error', false);
    });
  }

  ifCheckSuperAdmin() {
    const currentUser = JSON.parse(localStorage.getItem('currentUser'));
    if (currentUser['user'] && currentUser['user']['role'] && (currentUser['user']['role'].includes('superadmin') || currentUser['user']['role'].includes('guide')) ) {
      return true;
    }
    return false;
  }

  openAddModal(addPetitionModal: any) {
    this.petitionForm.reset();
    this.modalService.open(addPetitionModal, { centered: true, windowClass: 'modal-holder' });
  }

  savePetitionFiles(files: any) {
    this.finishedUpload = false;
    const formData = new FormData();
    formData.append('file', files.item(0));
    this.assetService.upload(formData).subscribe((response: any) => {
      this.petitionFiles.push({
        asset_id: response.id,
        type: 'petition'
      });
      this.finishedUpload = true;
    });
  }

  savePetition() {
    const addPetition = this.petitionForm.value;
    addPetition.attachments = this.petitionFiles;
    if (this.petitionForm.valid && this.petitionFiles.length > 0) {
      this.petitionService.addPetition(addPetition).subscribe((response: any) => {
        this.position('Added Successfully', 'Petition Added', 'OK', 'success');
        this.getAllPetitions();
        this.petitionFiles = [];
      });
    } else {
      this.position('Error', 'Invalid Form', 'Ok', 'error', false);
    }
  }

  position(title, text, confirmText, icon, dismissAll = true) {
    Swal.fire({
      title: title,
      html: text,
      icon: icon,
      confirmButtonColor: '#34c38f',
      cancelButtonColor: '#f46a6a',
      confirmButtonText: confirmText,
    }).then(result => {
      if (result.value && dismissAll) {
        this.modalService.dismissAll();
      }
    });
  }

  clear(table: Table){
    table.clear();
  }
}
