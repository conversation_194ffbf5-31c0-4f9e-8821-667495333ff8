<div class="container-fluid">
  <!-- start page title -->
  <div class="row">
    <div class="col-12">
      <div
        class="text-center my-3"
        style="margin-left: -30px; margin-right: -30px"
      >
        <a
          *ngIf="ifCheckSuperAdmin()"
          (click)="openAddModal(addPetitionModal)"
          class="btn btn-success inner mr-1 col-2"
        >
          {{'petition._add_petition' |translate}}
        </a>
      </div>
    </div>
    <!-- end col-->
  </div>
  <app-page-title
    [title]="'petition._list_header' |translate"
    [breadCrumbItems]="breadCrumbItems"
  ></app-page-title>
  <!-- end page title -->
  <div class="row">
    <div class="col-12">
      <p-table
        #petitions
        [value]="petitionList"
        [scrollable]="true"
        scrollHeight="400px"
        scrollDirection="both"
        styleClass="p-datatable-sm"
        [loading]="loading"
        [rowHover]="true"
        [rows]="10"
        [showCurrentPageReport]="true"
        [paginator]="true"
        currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
        [globalFilterFields]="[
          'team.team_name',
          'session.name',
          'session.race.name',
          'target',
          'type',
          'content'
        ]"
      >
        <ng-template pTemplate="caption">
          <div class="flex">
            <button
              pButton
              [label]="'table._clear_filter' |translate"
              class="p-button-outlined"
              icon="pi pi-filter-slash"
              (click)="clear(petitions)"
            ></button>
            <span class="p-input-icon-left float-right search">
              <i class="pi pi-search"></i>
              <input
                pInputText
                type="text"
                (input)="
                  petitions.filterGlobal($event.target.value, 'contains')
                "
                [placeholder]="'table._search_placeholder' |translate"
              />
            </span>
          </div>
        </ng-template>
        <ng-template pTemplate="header">
          <tr>
            <th style="width: 50px">Id</th>
            <th style="width: 150px">
              {{ 'member.form.team_name' | translate }}
            </th>
            <th style="width: 150px">
              {{ 'petition._session_name' | translate }}
            </th>
            <th style="width: 100px">
              {{ 'petition._instutition' | translate }}
            </th>
            <th style="width: 180px">{{ 'petition._subject' | translate }}</th>
            <th style="width: 280px">{{ 'petition._content' | translate }}</th>
            <th pSortableColumn="like" style="width: 120px">
              {{ 'petition._approved' | translate
              }}<p-sortIcon field="like"></p-sortIcon>
            </th>
            <th pSortableColumn="hesitant" style="width: 120px">
              {{ 'petition._hesitant' | translate
              }}<p-sortIcon field="hesitant"></p-sortIcon>
            </th>
            <th pSortableColumn="dislike" style="width: 120px">
              {{ 'petition._rejected' | translate
              }}<p-sortIcon field="dislike"></p-sortIcon>
            </th>
            <th style="width: 100px">{{ 'petition._status' | translate }}</th>
            <th style="width: 120px">{{ '_created_at' | translate }}</th>
            <th *ngIf="ifCheckSuperAdmin()" style="width: 100px">
              {{ 'petition._delete' | translate }}
            </th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-petition let-rowIndex="rowIndex">
          <tr>
            <td style="width: 50px">
              <a
                href="javascript: void(0);"
                [routerLink]="['/petition/detail/', petition.id]"
                >{{ petition?.id }}</a
              >
            </td>
            <td style="width: 150px">
              <a
                href="javascript: void(0);"
                [routerLink]="['/petition/detail/', petition.id]"
                >{{ petition?.team?.team_name }}</a
              >
            </td>
            <td style="width: 150px">{{ petition?.session_name }}</td>
            <td style="width: 100px">{{ petition?.target }}</td>
            <td style="width: 180px">{{ petition?.type }}</td>
            <td style="width: 280px">
              <p style="padding-right: 5px; font-size: 12px">
                {{
                  petition.content?.length > 100
                    ? petition.content.substring(0, 99) + "..."
                    : petition.content
                }}
              </p>
            </td>
            <td style="width: 120px">
              <div class="input-group mb-3">
                <div class="input-group-prepend">
                  <span class="input-group-text" id="basic-addon1"
                    ><i class="bx bx-up-arrow-alt"></i
                  ></span>
                </div>
                <input
                  type="text"
                  style="max-width: 50px"
                  readonly
                  class="form-control"
                  [(ngModel)]="petition.like"
                />
              </div>
            </td>
            <td style="width: 120px">
              <div class="input-group mb-3">
                <div class="input-group-prepend">
                  <span class="input-group-text" id="basic-addon1"
                    ><i class="bx bx-up-arrow-alt"></i
                  ></span>
                </div>
                <input
                  type="text"
                  style="max-width: 50px"
                  readonly
                  class="form-control"
                  [(ngModel)]="petition.hesitant"
                />
              </div>
            </td>
            <td style="width: 120px">
              <div class="input-group mb-3">
                <div class="input-group-prepend">
                  <span class="input-group-text" id="basic-addon1"
                    ><i class="bx bx-up-arrow-alt"></i
                  ></span>
                </div>
                <input
                  type="text"
                  style="max-width: 50px"
                  readonly
                  class="form-control"
                  [(ngModel)]="petition.dislike"
                />
              </div>
            </td>
            <td style="width: 100px">
              <h6 *ngIf="petition?.active === 0" style="color: red">
                {{ "petition._closed" | translate }}
              </h6>
              <h6 *ngIf="petition?.active === 1" style="color: green">
                {{ "petition._active" | translate }}
              </h6>
            </td>
            <td style="width: 120px">
              {{ petition.created_at | date: "yyyy-MM-dd H:mm" }}
            </td>
            <td style="width: 100px" *ngIf="ifCheckSuperAdmin()">
              <a
                href="javascript: void(0);"
                (click)="removePetition(petition.id)"
              >
                <i class="bx bxs-trash"></i>
                {{ 'petition._delete' | translate }}
              </a>
            </td>
          </tr>
        </ng-template>
        <ng-template pTemplate="footer"> </ng-template>
      </p-table>
    </div>
  </div>

  <ngx-spinner type="ball-clip-rotate-multiple" size="medium"> </ngx-spinner>

  <ng-template #addPetitionModal let-modal>
    <div class="modal-header">
      <h5 class="modal-title mt-0">
        {{'petition._add_petition' |translate}}
      </h5>
      <button
        type="button"
        class="close"
        (click)="modal.dismiss('Cross click')"
        aria-hidden="true"
      >
        ×
      </button>
    </div>

    <div class="modal-body">
      <div class="col-12">
        <form [formGroup]="petitionForm">
          <div class="form-group">
            <label class="control-label">{{
              'member.form.team_name' |translate
            }}</label
            ><select
              class="form-control"
              name="title"
              formControlName="team_id"
            >
              <option value="">{{ '_select_team' | translate}}</option>
              <option *ngFor="let option of title" [value]="option.id">
                {{ option.vehicle_number }} - {{ option.team_name }}
              </option>
            </select>
          </div>
          <div class="form-group">
            <label class="control-label">{{
              'petition._session_name' | translate
            }}</label
            ><select
              class="form-control"
              name="title"
              formControlName="session_name"
            >
              <option value="">
                {{ 'petition._select_session' | translate }}
              </option>
              <option *ngFor="let option of sessionValues" [value]="option">
                {{ option }}
            </select>
          </div>

          <div class="form-group">
            <label class="control-label">{{
              'petition._instutition' | translate
            }}</label
            ><select class="form-control" name="title" formControlName="target">
              <option value="">{{ 'petition._select' | translate }}</option>
              <option
                *ngFor="let instutition of instutitions"
                [value]="instutition"
              >
                {{ instutition }}
              </option>
            </select>
          </div>

          <div class="form-group">
            <label class="control-label">{{
              'petition._subject' | translate
            }}</label
            ><select class="form-control" name="title" formControlName="type">
              <option value="">{{ 'petition._select' | translate }}</option>
              <option *ngFor="let subject of subjects" [value]="subject">
                {{ subject }}
              </option>
            </select>
          </div>
          <div class="form-group">
            <textarea
              class="form-control"
              type="text"
              formControlName="content"
              [placeholder]="'petition._content' | translate"
              cols="30"
              rows="5"
            ></textarea>
          </div>
          <div class="custom-file">
            <input
              [disabled]="!finishedUpload"
              type="file"
              #story
              accept="image/*"
              class="custom-file-input"
              id="customFile"
              (change)="savePetitionFiles(story.files)"
            />
            <span
              class="spinner-border spinner-border-sm file-loading-spinner"
              [hidden]="finishedUpload"
              role="status"
              aria-hidden="true"
            ></span>
            <label class="custom-file-label" for="customFile">{{
              '_select_file' | translate
            }}</label>
          </div>
        </form>
        <div class="text-center mt-4">
          <button
            type="button"
            (click)="savePetition()"
            class="btn btn-primary"
            [disabled]="!finishedUpload"
          >
            {{ 'petition._add_petition' | translate }}
          </button>
        </div>
      </div>
    </div>
  </ng-template>
</div>
