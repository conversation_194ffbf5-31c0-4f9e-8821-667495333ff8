{"HEADER": {"SEARCH": "Search...", "MEGA_MENU": "Mega Menu", "NOTIFICATIONS": {"TITLE": "Notifications", "VIEW_ALL": "View All", "LOAD_MORE": "Load More..", "FIRST": {"TITLE": "Your order is placed", "TEXT": "If several languages coalesce the grammar", "TIME": "3 min ago"}, "SECOND": {"TITLE": "<PERSON>", "TEXT": "It will seem like simplified English.", "TIME": "1 hours ago"}, "THIRD": {"TITLE": "Your item is shipped", "TEXT": "If several languages coalesce the grammar", "TIME": "3 min ago"}, "FOUR": {"TITLE": "<PERSON><PERSON>", "TEXT": "As a skeptical Cambridge friend of mine occidental.", "TIME": "1 hours ago"}}, "SETTINGS": "Settings", "UI": {"TITLE": "UI Components", "LIGHTBOX": "LightBox", "RANGE_SLIDER": "Range Slider", "SWEET_ALERT": "<PERSON> Alert", "RATING": "Rating", "FORMS": "Forms", "TABLES": "Tables", "CHARTS": "Charts"}, "APPLICATIONS": {"TITLE": "Applications", "ECOMMERCE": "Ecommerce", "CALENDAR": "Calendar", "EMAIL": "Email", "PROJECTS": "Projects", "TASKS": "Tasks", "CONTACTS": "Contacts"}, "EXTRA_PAGES": {"TITLE": "Extra Pages", "LIGHT_SIDEBAR": "Light Sidebar", "COMPACT_SIDEBAR": "Compact Sidebar", "HORIZONTAL_LAYOUT": "Horizontal layout", "MAINTENANCE": "Maintenance", "COMING SOON": "Coming Soon", "TIMELINE": "Timeline", "FAQ": "FAQs"}, "LOGIN": {"PROFILE": "Profile", "SETTINGS": "Settings", "LOGOUT": "Logout"}}, "MENUITEMS": {"MENU": {"TEXT": "<PERSON><PERSON>"}, "DASHBOARDS": {"TEXT": "Dashboards", "BADGE": "03", "LIST": {"DEFAULT": "<PERSON><PERSON><PERSON>", "SAAS": "Saas", "CRYPTO": "Crypto"}}, "TEAMS": {"TEXT": "Teams"}, "TECHNICALDESIGN": {"TEXT": "Technical Design"}, "APPOINTMENT": {"TEXT": "Appointment"}, "EVALUATION": {"TEXT": "Evaluation"}, "RACE": {"TEXT": "Race"}, "REPORTS": {"TEXT": "Race Results"}, "REPORTS-DETAIL": {"TEXT": "Reports"}, "STATISTICS": {"TEXT": "Statistics"}, "PETITION": {"TEXT": "Petition"}, "EVALUATION-RESULTS": {"TEXT": "Evaluation Results"}, "CONSUMPTION": {"TEXT": "Consumption"}, "LAYOUTS": {"TEXT": "Layouts", "LIST": {"HORIZONTAL": "Horizontal", "LIGHTSIDEBAR": "Light Sidebar", "COMPACTSIDEBAR": "Compact Sidebar", "ICONSIDEBAR": "Icons Sidebar", "BOXED": "Boxed Layout", "COLOREDSIDEBAR": "Colored Sidebar", "VERTICAL": "Vertical", "LIGHTTOPBAR": "Light Topbar", "COLOREDHEADER": "Colored Header"}}, "APPS": {"TEXT": "Apps"}, "CALENDAR": {"TEXT": "Calendar"}, "CHAT": {"TEXT": "Cha<PERSON>", "BADGE": "New"}, "ECOMMERCE": {"TEXT": "Ecommerce", "LIST": {"PRODUCTS": "Products", "PRODUCTDETAIL": "Product Detail", "ORDERS": "Orders", "CUSTOMERS": "Customers", "CART": "<PERSON><PERSON>", "CHECKOUT": "Checkout", "SHOPS": "Shops", "ADDPRODUCT": "Add Product"}}, "CRYPTO": {"TEXT": "Crypto", "LIST": {"WALLET": "Wallet", "BUY/SELL": "Buy/sell", "EXCHANGE": "Exchange", "LENDING": "Lending", "ORDERS": "Orders", "KYCAPPLICATION": "KYC Application", "ICOLANDING": "ICO Landing"}}, "EMAIL": {"TEXT": "Email", "LIST": {"INBOX": "Inbox", "READEMAIL": "Read Email"}}, "INVOICES": {"TEXT": "Invoices", "LIST": {"INVOICELIST": "Invoice List", "INVOICEDETAIL": "Invoice Detail"}}, "PROJECTS": {"TEXT": "Projects", "LIST": {"GRID": "Projects Grid", "PROJECTLIST": "Projects List", "OVERVIEW": "Project Overview", "CREATE": "Create New"}}, "TASKS": {"TEXT": "Tasks", "LIST": {"TASKLIST": "Task List", "KANBAN": "Kanban Board", "CREATETASK": "Create Task"}}, "CONTACTS": {"TEXT": "Contacts", "LIST": {"USERGRID": "User Grid", "USERLIST": "User List", "PROFILE": "Profile"}}, "PAGES": {"TEXT": "Pages"}, "ADMIN": {"TEXT": "Admin"}, "MANAGEMENT": {"TEXT": "Management"}, "SETTINGS": {"TEXT": "Settings", "LIST": {"Technical": "Technical Criterias", "Seasons": "Seasons"}}, "AUTHORIZATION": {"TEXT": "Authorization", "LIST": {"LOGIN": "<PERSON><PERSON>", "REGISTER": "Register", "RECOVERPWD": "Recover Password", "LOCKSCREEN": "Lock screen"}}, "UTILITY": {"TEXT": "Utility", "LIST": {"STARTER": "Starter <PERSON>", "MAINTENANCE": "Maintenance", "TIMELINE": "Timeline", "FAQS": "FAQs", "PRICING": "Pricing", "ERROR404": "Error 404", "ERROR500": "Error 500"}}, "COMPONENTS": {"TEXT": "Components"}, "UIELEMENTS": {"TEXT": "UI Elements", "LIST": {"ALERTS": "<PERSON><PERSON><PERSON>", "BUTTONS": "Buttons", "CARDS": "Cards", "CAROUSEL": "Carousel", "DROPDOWNS": "Dropdowns", "GRID": "Grid", "IMAGES": "Images", "MODALS": "Modals", "RANGESLIDER": "Range Slider", "PROGRESSBAR": "Progress Bars", "SWEETALERT": "<PERSON> Alert", "TABS": "Tabs & Accordions", "TYPOGRAPHY": "Typography", "VIDEO": "Video", "GENERAL": "General", "COLORS": "Colors", "CROPPER": "Image Cropper"}}, "FORMS": {"TEXT": "Forms", "BADGE": "8", "LIST": {"ELEMENTS": "Form Elements", "VALIDATION": "Form Validation", "ADVANCED": "Form Advanced", "EDITOR": "Form Editor", "FILEUPLOAD": "Form File Upload", "REPEATER": "Form Repeater", "WIZARD": "Form Wizard", "MASK": "Form Mask"}}, "TABLES": {"TEXT": "Tables", "LIST": {"BASIC": "Basic Tables", "ADVANCED": "Advanced Table"}}, "CHARTS": {"TEXT": "Charts", "LIST": {"APEX": "Apex Chart", "CHARTJS": "Chartjs Chart", "CHARTIST": "Chartist Chart", "ECHART": "E Chart"}}, "ICONS": {"TEXT": "Icons", "LIST": {"BOXICONS": "Boxicons", "MATERIALDESIGN": "Material Design", "DRIPICONS": "Dripicons", "FONTAWESOME": "Font awesome"}}, "MAPS": {"TEXT": "Maps", "LIST": {"GOOGLEMAP": "Google Maps"}}, "MULTILEVEL": {"TEXT": "Multi Level", "LIST": {"LEVEL1": {"1": "Level 1.1", "2": "Level 1.2", "LEVEL2": {"1": "Level 2.1", "2": "Level 2.2"}}}}}, "member": {"form": {"first_name": "First Name", "last_name": "Last Name", "email": "Email", "team_id": "Team Id", "role_in_team": "Role", "identity_number": "Identity Number", "passport_number": "Passport Number", "phone_number": "Phone Number", "birthday": "Birthday", "gender": "Gender", "in_area": "Presence on race field", "parent_name": "Parent Name", "parent_phone": "Parent Phone", "uniform_size": "T-shirt Size", "hes_code": "HES Code", "profile_picture": "Profile Picture", "team_member_title": "Team Members", "team_member_page": "Team Overview", "vehicle_name": "Vehicle Name and Vehicle Category", "required_adult": "required if member not adult", "change_team": "Change", "team_name": "Team Name", "required_hes_code": "Enter a HES code that valid until at least 30 September 2021 compatible with the ID number", "team_name_restriction": "A minimum of 4 characters and a maximum of 11 characters can be used in the uniforms to be used in the field. The forms will be given anonymously to the teams that do not meet these conditions.", "male": "Male", "female": "Female"}, "team_roles": {"member": "Member", "captain": "Captain", "driver": "Driver", "reserveDriver": "Reserve Driver", "academicAdvisor": "Academic Advisor", "lise_academicAdvisor": "Advisor", "curator": "Curator"}, "_add_member": "Add Member", "_update_member": "Update Member", "_hide_uniform_table": "Hide t-shirt size table", "_show_uniform_table": "Show t-shirt size table", "_not_tc_citizen": "I'm not T.C. citizen", "_check_hes_code": "Check HES Codes", "_export_qr_code": "Download QR Codes"}, "team": {"form": {"_random_vehicle_number": "Random Vehicle Number", "_team_name": "Team Name", "_school_type": "Team Type", "_university_name": "University Name", "_high_school_name": "University Name", "_vehicle_name": "Vehicle Name", "_vehicle_category": "Vehicle Category", "_city_name": "City Name", "_vehicle_number": "Vehicle Number", "_number_of_team_members": "Number of Team Members"}, "_create_team": "Create Team", "_save_team": "Save Team", "_team_details": "Team Details", "_edit_team": "Edit Team", "_change_team_name": "Change Team Name", "_edit": "Edit", "_approve_now": "Approve Now", "_forms_team_files": "Forms and Member Files", "_check_my_team": "Check My Team", "_check_team": "Check Team", "_pre_registration_period_expired": "The pre-registration period has expired."}, "petition": {"_list_header": "Petitions", "_header": "Petition", "_petition_detail": "Petition Detail", "_content": "Content", "_approve": "Approve", "_reject": "Reject", "_approved": "Approved", "_rejected": "Rejected", "_hesitant": "Hesitant", "_instutition": "Instutition", "_subject": "Subject", "_select": "Select", "_comments": "Comments", "_add_petition": "Add Petition", "_delete": "Delete", "_add_new_comment": "Add New Comment", "_add_comment": "Add Comment", "_session_name": "Session Name", "_select_session": "Select Session", "_close_petition": "Close Petition", "_status": "Status", "_closed": "Closed", "_active": "Active", "_race_name": "Race Name"}, "table": {"_search_placeholder": "Search", "_clear_filter": "Clear Filter", "_show_only_active": "Show Only Active", "_show_all": "Show All"}, "_created_at": "Created At", "_updated_at": "Updated At", "_member": "Add Member", "_teams": "Teams", "_team_overview": "Team Overview", "_update": "Update", "_in_area": "In Area", "_select_file": "Select Upload File", "_select_team": "Select Team", "reset_password": {"_update_password": "Update Password", "_password": "Password", "_password_confirmation": "Password Confirmation", "_type_new_password": "Type New Password", "_type_new_password_confirmation": "Type New Password Confirmation"}, "hes_code": {"RISKLESS": "Safe", "RISKY": "Risky", "hescodenotfound": "Invalid", "hescodehasbeenexpired": "Expired"}, "_team_validation": {"member_error": "There are errors in the team and member information", "valid": "Team and members information is valid"}}