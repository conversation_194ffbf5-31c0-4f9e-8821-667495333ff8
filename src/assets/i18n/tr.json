{"HEADER": {"SEARCH": "Search...", "MEGA_MENU": "Mega Menu", "NOTIFICATIONS": {"TITLE": "Notifications", "VIEW_ALL": "View All", "LOAD_MORE": "Load More..", "FIRST": {"TITLE": "Your order is placed", "TEXT": "If several languages coalesce the grammar", "TIME": "3 min ago"}, "SECOND": {"TITLE": "<PERSON>", "TEXT": "It will seem like simplified English.", "TIME": "1 hours ago"}, "THIRD": {"TITLE": "Your item is shipped", "TEXT": "If several languages coalesce the grammar", "TIME": "3 min ago"}, "FOUR": {"TITLE": "<PERSON><PERSON>", "TEXT": "As a skeptical Cambridge friend of mine occidental.", "TIME": "1 hours ago"}}, "SETTINGS": "Settings", "UI": {"TITLE": "UI Components", "LIGHTBOX": "LightBox", "RANGE_SLIDER": "Range Slider", "SWEET_ALERT": "<PERSON> Alert", "RATING": "Rating", "FORMS": "Forms", "TABLES": "Tables", "CHARTS": "Charts"}, "APPLICATIONS": {"TITLE": "Applications", "ECOMMERCE": "Ecommerce", "CALENDAR": "Calendar", "EMAIL": "Email", "PROJECTS": "Projects", "TASKS": "Tasks", "CONTACTS": "Contacts"}, "EXTRA_PAGES": {"TITLE": "Extra Pages", "LIGHT_SIDEBAR": "Light Sidebar", "COMPACT_SIDEBAR": "Compact Sidebar", "HORIZONTAL_LAYOUT": "Horizontal layout", "MAINTENANCE": "Maintenance", "COMING SOON": "Coming Soon", "TIMELINE": "Timeline", "FAQ": "FAQs"}, "LOGIN": {"PROFILE": "Profil", "SETTINGS": "Settings", "LOGOUT": "Çıkış"}}, "MENUITEMS": {"MENU": {"TEXT": "<PERSON><PERSON>"}, "DASHBOARDS": {"TEXT": "Dashboards", "BADGE": "03", "LIST": {"DEFAULT": "<PERSON><PERSON><PERSON>", "SAAS": "Saas", "CRYPTO": "Crypto"}}, "TEAMS": {"TEXT": "Teams"}, "TECHNICALDESIGN": {"TEXT": "Technical Design"}, "APPOINTMENT": {"TEXT": "Appointment"}, "EVALUATION": {"TEXT": "Evaluation"}, "RACE": {"TEXT": "Race"}, "REPORTS": {"TEXT": "<PERSON><PERSON><PERSON>ş Sonuçları"}, "REPORTS-DETAIL": {"TEXT": "Reports"}, "STATISTICS": {"TEXT": "Istatistik"}, "PETITION": {"TEXT": "Dilekçe"}, "EVALUATION-RESULTS": {"TEXT": "Değerlendirme Sonuçları"}, "CONSUMPTION": {"TEXT": "<PERSON><PERSON><PERSON><PERSON>"}, "LAYOUTS": {"TEXT": "Layouts", "LIST": {"HORIZONTAL": "Horizontal", "LIGHTSIDEBAR": "Light Sidebar", "COMPACTSIDEBAR": "Compact Sidebar", "ICONSIDEBAR": "Icons Sidebar", "BOXED": "Boxed Layout", "COLOREDSIDEBAR": "Colored Sidebar", "VERTICAL": "Vertical", "LIGHTTOPBAR": "Light Topbar", "COLOREDHEADER": "Colored Header"}}, "APPS": {"TEXT": "Apps"}, "CALENDAR": {"TEXT": "Calendar"}, "CHAT": {"TEXT": "Cha<PERSON>", "BADGE": "New"}, "ECOMMERCE": {"TEXT": "Ecommerce", "LIST": {"PRODUCTS": "Products", "PRODUCTDETAIL": "Product Detail", "ORDERS": "Orders", "CUSTOMERS": "Customers", "CART": "<PERSON><PERSON>", "CHECKOUT": "Checkout", "SHOPS": "Shops", "ADDPRODUCT": "Add Product"}}, "CRYPTO": {"TEXT": "Crypto", "LIST": {"WALLET": "Wallet", "BUY/SELL": "Buy/sell", "EXCHANGE": "Exchange", "LENDING": "Lending", "ORDERS": "Orders", "KYCAPPLICATION": "KYC Application", "ICOLANDING": "ICO Landing"}}, "EMAIL": {"TEXT": "Email", "LIST": {"INBOX": "Inbox", "READEMAIL": "Read Email"}}, "INVOICES": {"TEXT": "Invoices", "LIST": {"INVOICELIST": "Invoice List", "INVOICEDETAIL": "Invoice Detail"}}, "PROJECTS": {"TEXT": "Projects", "LIST": {"GRID": "Projects Grid", "PROJECTLIST": "Projects List", "OVERVIEW": "Project Overview", "CREATE": "Create New"}}, "TASKS": {"TEXT": "Tasks", "LIST": {"TASKLIST": "Task List", "KANBAN": "Kanban Board", "CREATETASK": "Create Task"}}, "CONTACTS": {"TEXT": "Contacts", "LIST": {"USERGRID": "User Grid", "USERLIST": "User List", "PROFILE": "Profile"}}, "PAGES": {"TEXT": "Pages"}, "ADMIN": {"TEXT": "Admin"}, "MANAGEMENT": {"TEXT": "Management"}, "SETTINGS": {"TEXT": "Settings", "LIST": {"Technical": "Technical Criterias", "Seasons": "Seasons"}}, "AUTHORIZATION": {"TEXT": "Authorization", "LIST": {"LOGIN": "<PERSON><PERSON>", "REGISTER": "Register", "RECOVERPWD": "Recover Password", "LOCKSCREEN": "Lock screen"}}, "UTILITY": {"TEXT": "Utility", "LIST": {"STARTER": "Starter <PERSON>", "MAINTENANCE": "Maintenance", "TIMELINE": "Timeline", "FAQS": "FAQs", "PRICING": "Pricing", "ERROR404": "Error 404", "ERROR500": "Error 500"}}, "COMPONENTS": {"TEXT": "Components"}, "UIELEMENTS": {"TEXT": "UI Elements", "LIST": {"ALERTS": "<PERSON><PERSON><PERSON>", "BUTTONS": "Buttons", "CARDS": "Cards", "CAROUSEL": "Carousel", "DROPDOWNS": "Dropdowns", "GRID": "Grid", "IMAGES": "Images", "MODALS": "Modals", "RANGESLIDER": "Range Slider", "PROGRESSBAR": "Progress Bars", "SWEETALERT": "<PERSON> Alert", "TABS": "Tabs & Accordions", "TYPOGRAPHY": "Typography", "VIDEO": "Video", "GENERAL": "General", "COLORS": "Colors", "CROPPER": "Image Cropper"}}, "FORMS": {"TEXT": "Forms", "BADGE": "8", "LIST": {"ELEMENTS": "Form Elements", "VALIDATION": "Form Validation", "ADVANCED": "Form Advanced", "EDITOR": "Form Editor", "FILEUPLOAD": "Form File Upload", "REPEATER": "Form Repeater", "WIZARD": "Form Wizard", "MASK": "Form Mask"}}, "TABLES": {"TEXT": "Tables", "LIST": {"BASIC": "Basic Tables", "ADVANCED": "Advanced Table"}}, "CHARTS": {"TEXT": "Charts", "LIST": {"APEX": "Apex Chart", "CHARTJS": "Chartjs Chart", "CHARTIST": "Chartist Chart", "ECHART": "E Chart"}}, "ICONS": {"TEXT": "Icons", "LIST": {"BOXICONS": "Boxicons", "MATERIALDESIGN": "Material Design", "DRIPICONS": "Dripicons", "FONTAWESOME": "Font awesome"}}, "MAPS": {"TEXT": "Maps", "LIST": {"GOOGLEMAP": "Google Maps"}}, "MULTILEVEL": {"TEXT": "Multi Level", "LIST": {"LEVEL1": {"1": "Level 1.1", "2": "Level 1.2", "LEVEL2": {"1": "Level 2.1", "2": "Level 2.2"}}}}}, "member": {"form": {"first_name": "Ad<PERSON>", "last_name": "Soyadı", "email": "Email", "team_id": "Takım ID", "role_in_team": "<PERSON><PERSON><PERSON><PERSON>", "identity_number": "TC Kimlik NO", "passport_number": "Pasaport Numarası", "phone_number": "Telefon NO", "birthday": "<PERSON><PERSON><PERSON>", "gender": "Cinsiyet", "in_area": "<PERSON><PERSON><PERSON><PERSON>", "parent_name": "Ebeveyn <PERSON>", "parent_phone": "Ebeveyn Tel NO", "uniform_size": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hes_code": "HES Kodu", "profile_picture": "<PERSON><PERSON>", "team_member_title": "Takım Ü<PERSON>leri", "team_member_page": "Takım <PERSON>ı", "vehicle_name": "Araç <PERSON>ı ve <PERSON>ç <PERSON>gor<PERSON>", "required_adult": "Üye 18 yaşından küçükse ekleyiniz.", "change_team": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "team_name": "Takım Adı", "required_hes_code": "HES kodunun en az 30 Eylül 2021 tarihine kadar geçerli bir kod olması gerekmektedir. T.C. kimlik numarası HES kodu uyumu Sağlık Bakanlığı sisteminden kontrol edilecektir", "team_name_restriction": "Alanda kullanılacak formalarda takım isimleri yer alacaktır. Takım isminin formaya basılabilmesi için en az 4 karakter en fazla 11 karakter olması gerekmektedir. Takım adı bu şartı sağlamayan takımlara isimsiz forma verilecektir.", "male": "<PERSON><PERSON><PERSON>", "female": "Kadın"}, "team_roles": {"member": "Üye", "captain": "<PERSON><PERSON><PERSON>", "driver": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reserveDriver": "<PERSON><PERSON>", "academicAdvisor": "Akademik Danışman", "lise_academicAdvisor": "Danışman Öğretmen", "curator": "<PERSON><PERSON><PERSON>"}, "_add_member": "<PERSON><PERSON>", "_update_member": "Üye Güncelle", "_hide_uniform_table": "Tişört beden tablosunu gizle", "_show_uniform_table": "Tişört beden tablosunu göster", "_not_tc_citizen": "T.C. vatandaşı değilim", "_check_hes_code": "HES Kodlarını Kontrol Et", "_export_qr_code": "QR Kodları İndir"}, "team": {"form": {"_random_vehicle_number": "<PERSON><PERSON><PERSON><PERSON>", "_team_name": "Takım Adı", "_university_name": "Üniversite Adı", "_vehicle_name": "<PERSON><PERSON>", "_vehicle_category": "<PERSON><PERSON>", "_city_name": "Şehir <PERSON>", "_vehicle_number": "<PERSON><PERSON>", "_number_of_team_members": "Takım Üye Sayısı"}, "_create_team": "Takım O<PERSON>ştur", "_save_team": "Takımı Kaydet", "_team_details": "Takım <PERSON>ı", "_edit_team": "Takımı Düzenle", "_change_team_name": "Takım İ<PERSON>", "_edit": "<PERSON><PERSON><PERSON><PERSON>", "_approve_now": "<PERSON><PERSON><PERSON>", "_forms_team_files": "Takım Dosyaları", "_check_my_team": "Takım Bilgilerini Kontrol Et", "_check_team": "Takım Bilgilerini Kontrol Et", "_pre_registration_period_expired": "<PERSON><PERSON> kayıt süresi sona ermiştir."}, "petition": {"_list_header": "Dilekçeler", "_header": "Dilekçe", "_petition_detail": "Dilekçe Detay", "_content": "İçerik", "_approve": "Kabul", "_reject": "Red", "_approved": "Onaylanmış", "_rejected": "Reddedilmiş", "_hesitant": "Kararsız", "_instutition": "İtiraz <PERSON>ii", "_subject": "<PERSON><PERSON>", "_select": "<PERSON><PERSON><PERSON><PERSON>", "_comments": "<PERSON><PERSON><PERSON>", "_add_petition": "Dilekçe Ekle", "_delete": "Sil", "_add_new_comment": "<PERSON><PERSON>", "_add_comment": "<PERSON><PERSON><PERSON>", "_session_name": "Oturum Adı", "_select_session": "Oturum Seçin", "_close_petition": "Dilekçeyi Kapat", "_status": "Durum", "_closed": "Kapatılmış", "_active": "Aktif", "_race_name": "Ya<PERSON><PERSON>ş Adı"}, "table": {"_search_placeholder": "<PERSON><PERSON>", "_clear_filter": "<PERSON><PERSON><PERSON><PERSON>", "_show_only_active": "Aktifleri Göster", "_show_all": "Tümünü <PERSON>ö<PERSON>"}, "_created_at": "Oluştruma Tarihi", "_updated_at": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_member": "<PERSON><PERSON>", "_teams": "Takımlar", "_team_overview": "Takım <PERSON>", "_update": "<PERSON><PERSON><PERSON><PERSON>", "_in_area": "Alanda", "_select_file": "Yüklenecek Dosyayı Seçiniz", "_select_team": "Takım <PERSON>", "reset_password": {"_update_password": "<PERSON><PERSON><PERSON>", "_password": "Şifre", "_password_confirmation": "<PERSON><PERSON><PERSON>", "_type_new_password": "<PERSON>ni Şif<PERSON>", "_type_new_password_confirmation": "<PERSON><PERSON>"}, "hes_code": {"RISKLESS": "Risksiz", "RISKY": "<PERSON><PERSON>", "hescodenotfound": "Geçersiz", "hescodehasbeenexpired": "Zaman Aşımı"}, "_team_validation": {"member_error": "Ön kayıt bilgilerinde hatalar var", "valid": "Takım ve üye bilgileri geçerli"}}