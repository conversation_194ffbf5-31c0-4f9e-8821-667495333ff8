/* You can add global styles to this file, and also import other style files */
.modal-holder {
  animation-name: example;
  animation-duration: 0.3s;
}

@keyframes example {
  0% {
    transform: scale(0.5)
  }
  100% {
    transform: scale(1)
  }
}

.carousel-item {
  transition: opacity 0.7s ease !important;
  position: absolute !important;
  display: block !important;
  opacity: 0;
}

.carousel-item.active {
  position: relative !important;
  opacity: 1;
}

.row-color-active a {
  color: rgb(33, 150, 243);
}

.row-color-active {
  color: rgb(33, 150, 243);
}

.row-color-close a {
  color: rgb(255, 152, 0);
}

.row-color-close {
  color: rgb(255, 152, 0);
}

.row-color-failed a {
  color: rgb(244 66 54);
}

.row-color-failed {
  color: rgb(244 66 54);
}

.row-color-success a {
  color: #648848;
}

.row-color-success {
  color: #648848;
}


.electromobile-color {
  color: rgb(33, 150, 243);
}

.hydromobile-color {
  color: #648848;
}


.inboxContainer {
  width: 100%;
  height: 30vh;
  border: 1px solid #adadad;
  margin: auto;
  overflow: auto;
}
/*
bootstrap table theme
*/
.ngx-datatable.bootstrap {
  box-shadow: none;
  font-size: 14px;
}

.ngx-datatable.bootstrap .datatable-header {
  height: unset !important;
}

.ngx-datatable.bootstrap .datatable-header .datatable-header-cell {
  vertical-align: bottom;
  padding: 0.75rem;
  border-bottom: 1px solid #d1d4d7;
}

.ngx-datatable.bootstrap .datatable-header .datatable-header-cell .datatable-header-cell-label {
  line-height: 24px;
}

.ngx-datatable.bootstrap .datatable-body .datatable-body-row {
  padding: 0.75rem;
  vertical-align: top;
  border-top: 1px solid #d1d4d7;
}

.ngx-datatable.bootstrap .datatable-body .datatable-body-row.datatable-row-even {
  background-color: rgba(0, 0, 0, 0.05);
}

.ngx-datatable.bootstrap .datatable-body .datatable-body-row.active {
  background-color: #1483ff;
  color: #FFF;
}

.ngx-datatable.bootstrap .datatable-body .datatable-body-row .datatable-body-cell {
  text-align: left;
  vertical-align: top;
}

.ngx-datatable.bootstrap .datatable-footer {
  background: #2b81f2;
  color: #ededed;
  margin-top: -1px;
}

.ngx-datatable.bootstrap .datatable-footer .page-count {
  line-height: 50px;
  height: 50px;
  padding: 0 1.2rem;
}

.ngx-datatable.bootstrap .datatable-footer .datatable-pager {
  margin: 0 10px;
  vertical-align: top;
}

.ngx-datatable.bootstrap .datatable-footer .datatable-pager ul li {
  margin: 10px 0px;
}

.ngx-datatable.bootstrap .datatable-footer .datatable-pager ul li:not(.disabled).active a, .ngx-datatable.bootstrap .datatable-footer .datatable-pager ul li:not(.disabled):hover a {
  background-color: #545454;
  font-weight: bold;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  padding-bottom: 400px !important;

}
.spinner-border {
  color: #4eae32 !important;
}

.ngx-datatable.bootstrap .datatable-footer .datatable-pager a {
  height: 22px;
  min-width: 24px;
  line-height: 22px;
  padding: 0;
  border-radius: 3px;
  margin: 0 3px;
  text-align: center;
  vertical-align: top;
  text-decoration: none;
  vertical-align: bottom;
  color: #ededed;
}

.ngx-datatable.bootstrap .datatable-footer .datatable-pager .datatable-icon-left,
.ngx-datatable.bootstrap .datatable-footer .datatable-pager .datatable-icon-skip,
.ngx-datatable.bootstrap .datatable-footer .datatable-pager .datatable-icon-right,
.ngx-datatable.bootstrap .datatable-footer .datatable-pager .datatable-icon-prev {
  font-size: 18px;
  line-height: 27px;
  padding: 0 3px;
}

.ngx-datatable.bootstrap .datatable-summary-row .datatable-body-row .datatable-body-cell {
  font-weight: bold;
}

