{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"skote": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/skote", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "aot": true, "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss", "src/assets/scss/bootstrap.scss", "src/assets/scss/app.scss", "src/assets/scss/icons.scss", "node_modules/primeng/resources/themes/saga-blue/theme.css", "node_modules/primeng/resources/primeng.min.css", "node_modules/primeicons/primeicons.css"], "scripts": []}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "3mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}]}, "ec": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.lise.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "3mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}]}, "dev": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "3mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}]}, "local": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.local.ts"}], "optimization": false, "outputHashing": "none", "sourceMap": true, "extractCss": true, "namedChunks": true, "extractLicenses": false, "vendorChunk": true, "buildOptimizer": false, "budgets": [{"type": "initial", "maximumWarning": "3mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "skote:build"}, "configurations": {"production": {"browserTarget": "skote:build:production"}, "ec": {"browserTarget": "skote:build:ec"}, "dev": {"browserTarget": "skote:build:dev"}, "local": {"browserTarget": "skote:build:local", "hmr": true, "sourceMap": true}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "skote:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["tsconfig.app.json", "tsconfig.spec.json", "e2e/tsconfig.json"], "exclude": ["**/node_modules/**"]}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "skote:serve"}, "configurations": {"production": {"devServerTarget": "skote:serve:production"}, "ec": {"devServerTarget": "skote:serve:ec"}, "dev": {"devServerTarget": "skote:serve:dev"}, "local": {"devServerTarget": "skote:serve:dev"}}}}}}, "defaultProject": "skote", "cli": {"analytics": false}}