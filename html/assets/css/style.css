* {
  padding: 0;
  margin: 0;
  outline: none;
  box-sizing: border-box;
  font-family: var(--font-family);
}
:root {
  --font-family: "Poppins", Arial, sans-serif;
  --green-color: #4eae32;
}
.chart-container {
  padding: 1vw;
}
p {
  font-size: 0.8rem !important;
  font-family: var(--font-family);
}

a {
  color: #000;
  text-decoration: none;
  font-size: 0.9rem !important;
  background-color: transparent;
}
.card-header {
  padding: 0.75rem 0.75rem;
}
.btn-accordion {
  width: 100%;
  display: flex;
}
.btn-link {
  color: #000;
}
.card-body {
  padding: 0;
}
#accordion thead {
  background: #f8f8f8;
}
.sub-title {
  color: #828282;
}
.table td,
.table th {
  border: 1px solid #dee2e6;
}
header img {
  margin-top: -4px;
}
.card {
  border: transparent 1px solid;
}
.card-header {
  background-color: transparent;
  border: 1px var(--green-color) solid;
}
div#headingOne {
  background: var(--green-color);
}
div#headingTwo {
  background: var(--green-color);
}
div#headingThree {
  background: var(--green-color);
}
div#headingFour {
  background-color: #ff3b3b;
  color: #fff;
}
#headingFour h6 {
  color: #fff;
}
#headingFour p {
  color: #fff;
}
div#headingFive {
  background: #f2f2f2;
  border: 1px solid transparent;
}
.btn-link:hover {
  color: #000;
  text-decoration: none;
}
.btn.focus,
.btn:focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem transparent;
}
.btn-link.focus,
.btn-link:focus {
  text-decoration: none;
}
.number-box {
  background: var(--green-color);
  padding: 10px;
  border-radius: 10px 10px 0 0;
  color: #fff;
}
.number {
  font-size: 2rem !important;
}

.green-title {
  color: var(--green-color);
}
.gray-line {
  background: #f8f8f8;
}
.gray-line li {
  color: var(--green-color) !important;
  list-style: none;
}
.nav-link {
  color: var(--green-color) !important;
}
.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: #fff;
  border-bottom: 2px solid #1d1d1b;
  background-color: transparent;
}
.nav-pills .nav-link {
  border-radius: 0;
}
.table thead th {
  border-bottom: 2px solid transparent !important;
}

.table td,
.table th {
  border: 1px solid transparent !important;
}
.table td,
.table th {
  border-bottom: 2px solid transparent !important;
}
.result-table th {
  font-weight: 500;
  font-size: 1rem;
}
.text-decoration-underline {
  text-decoration: underline;
}
.progress {
  height: 1.5rem;
  margin-top: 11px;
}
.progress-bar {
  background-color: var(--green-color);
}
canvas.myDonutChart {
  width: 80px !important;
  height: 40px !important;
}
canvas#myChart2 {
  width: 265px !important;
  height: 133px !important;
}
