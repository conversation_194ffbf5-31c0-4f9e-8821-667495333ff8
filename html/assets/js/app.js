Chart.defaults.global.legend.position = "bottom";
Chart.defaults.global.defaultFontSize = 12;

const ctx = document.getElementById("myChart2");
const myChart = new Chart(ctx, {
  type: "doughnut",
  data: {
    labels: [
      "Application Details",
      "Interview",
      "Information Validation",
      "Offer Letter",
      "Final Checks",
    ],
    datasets: [
      {
        label: "# of Votes",
        data: [80, 10, 10, 20, 10],
        backgroundColor: ["#4EAE32", "#1D1D1B", "#D9D9D9"],
        borderWidth: 0,
      },
    ],
  },
  options: {
    legend: {
      display: false,
    },
    cutoutPercentage: 75,
    rotation: 1 * Math.PI,
    circumference: 1 * Math.PI,
  },
});
const data = {
  labels: [],
  datasets: [
    {
      label: "My First Dataset",
      data: [16, 41, 12, 20, 10],
      backgroundColor: ["#4EAE32", "#000000", "#D9D9D9", "#4EAE32", "#D9D9D9"],
      hoverOffset: 4,
    },
  ],
};
