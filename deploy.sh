#!/usr/bin/env bash

#UPSTREAM=${1:-'@{u}'}
#LOCAL=$(git rev-parse @)
#REMOTE=$(git rev-parse "$UPSTREAM")
#BASE=$(git merge-base @ "$UPSTREAM")
#
#
#
#if [ $LOCAL = $REMOTE ]; then
#    echo "Up-to-date"
#elif [ $LOCAL = $BASE ]; then
#    echo "Need to pull"
#    git pull origin master
#    echo "Build"
#    docker-compose up webbuilder
#    echo "Build DONE"
#elif [ $REMOTE = $BASE ]; then
#    echo "Need to push"
#else
#    echo "Diverged"
#fi
echo '===================================================================================='
date

git fetch
HEADHASH=$(git rev-parse HEAD)
UPSTREAMHASH=$(git rev-parse master@{upstream})

if [ "$HEADHASH" != "$UPSTREAMHASH" ]
then
 echo "Need to pull"
 git pull origin master
 echo "Build"
 /usr/local/bin/docker-compose up webbuilder
 echo "Build DONE"
 exit 0
else
 echo 'no'
fi

#docker-compose up webbuilder

