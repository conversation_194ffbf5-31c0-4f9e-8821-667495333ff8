{"info": {"_postman_id": "2690d954-b52b-4386-ac70-1707c39c6f6f", "name": "Efficiency Challenge", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "user", "item": [{"name": "user.update", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n        \"first_name\": \"<PERSON>\",\n        \"last_name\": \"Admin\",\n        \"email\": \"admin@localhost\",\n        \"username\": \"admin\",\n        \"picture_id\": null,\n        \"details\": {\n            \"gender\": \"male\",\n            \"birthday\": \"1991-12-30\",\n            \"country\": null,\n            \"timezone\": \"UTC\",\n            \"description\": null,\n            \"language\": null\n        }\n    }", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/user", "host": ["{{base_url}}"], "path": ["user"]}}, "response": [{"name": "user.update.full", "originalRequest": {"method": "PATCH", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n        \"first_name\": \"<PERSON>\",\n        \"last_name\": \"Admin\",\n        \"email\": \"admin@localhost\",\n        \"username\": \"admin\",\n        \"picture_id\": null,\n        \"details\": {\n            \"gender\": \"male\",\n            \"birthday\": \"1991-12-30\",\n            \"country\": null,\n            \"timezone\": \"UTC\",\n            \"description\": null,\n            \"language\": null\n        }\n    }", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/user", "host": ["{{base_url}}"], "path": ["user"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "localhost:8000"}, {"key": "Date", "value": "<PERSON><PERSON>, 21 Jul 2020 10:50:42 GMT"}, {"key": "Date", "value": "<PERSON><PERSON>, 21 Jul 2020 10:50:42 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/7.4.8"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "ETag", "value": "\"c85f6aa93d4b8b4b2a2b351b95eff0c8d9e3a89e\""}], "cookie": [], "body": "{\n    \"data\": {\n        \"id\": 1,\n        \"first_name\": \"<PERSON>\",\n        \"last_name\": \"Ad<PERSON>\",\n        \"email\": \"admin@localhost\",\n        \"username\": \"admin\",\n        \"email_verified_at\": \"2020-07-21T10:39:07.000000Z\",\n        \"created_at\": \"2020-07-21T10:39:07.000000Z\",\n        \"updated_at\": \"2020-07-21T10:39:07.000000Z\",\n        \"deleted_at\": null,\n        \"picture_url\": null,\n        \"role\": \"superadmin\",\n        \"profile_completed\": true,\n        \"details\": {\n            \"gender\": \"male\",\n            \"birthday\": \"1991-12-30\",\n            \"country\": null,\n            \"timezone\": \"UTC\",\n            \"description\": null,\n            \"language\": null,\n            \"age\": 28\n        }\n    }\n}"}]}, {"name": "user.get", "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/user", "host": ["{{base_url}}"], "path": ["user"]}}, "response": [{"name": "user.get", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/user", "host": ["{{base_url}}"], "path": ["user"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "localhost:8000"}, {"key": "Date", "value": "<PERSON><PERSON>, 21 Jul 2020 10:51:19 GMT"}, {"key": "Date", "value": "<PERSON><PERSON>, 21 Jul 2020 10:51:19 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/7.4.8"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "ETag", "value": "\"c85f6aa93d4b8b4b2a2b351b95eff0c8d9e3a89e\""}], "cookie": [], "body": "{\n    \"data\": {\n        \"id\": 1,\n        \"first_name\": \"<PERSON>\",\n        \"last_name\": \"Ad<PERSON>\",\n        \"email\": \"admin@localhost\",\n        \"username\": \"admin\",\n        \"email_verified_at\": \"2020-07-21T10:39:07.000000Z\",\n        \"created_at\": \"2020-07-21T10:39:07.000000Z\",\n        \"updated_at\": \"2020-07-21T10:39:07.000000Z\",\n        \"deleted_at\": null,\n        \"picture_url\": null,\n        \"role\": \"superadmin\",\n        \"profile_completed\": true,\n        \"details\": {\n            \"gender\": \"male\",\n            \"birthday\": \"1991-12-30\",\n            \"country\": null,\n            \"timezone\": \"UTC\",\n            \"description\": null,\n            \"language\": null,\n            \"age\": 28\n        }\n    }\n}"}]}, {"name": "user.delete", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/user", "host": ["{{base_url}}"], "path": ["user"]}}, "response": []}]}, {"name": "auth", "item": [{"name": "register", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"first_name\": \"tuncay\",\n    \"last_name\": \"akden<PERSON>\",\n    \"password\": \"pass1234\"\n}"}, "url": {"raw": "{{base_url}}/register", "host": ["{{base_url}}"], "path": ["register"]}}, "response": [{"name": "register", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"first_name\": \"tuncay\",\n    \"last_name\": \"akden<PERSON>\",\n    \"password\": \"pass1234\"\n}"}, "url": {"raw": "{{base_url}}/register", "host": ["{{base_url}}"], "path": ["register"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "localhost:8000"}, {"key": "Date", "value": "<PERSON><PERSON>, 21 Jul 2020 10:51:41 GMT"}, {"key": "Date", "value": "<PERSON><PERSON>, 21 Jul 2020 10:51:41 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/7.4.8"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "ETag", "value": "\"e20dd3983c2769a3f43d9b5ae18eaa25b170bd45\""}], "cookie": [], "body": "{\n    \"token\": \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOlwvXC9sb2NhbGhvc3Q6ODAwMFwvcmVnaXN0ZXIiLCJpYXQiOjE1OTUzMjg3MDEsImV4cCI6MTU5ODkyODcwMSwibmJmIjoxNTk1MzI4NzAxLCJqdGkiOiI1ZllrbDBPN0xLOXllamRhIiwic3ViIjo3LCJwcnYiOiIyM2JkNWM4OTQ5ZjYwMGFkYjM5ZTcwMWM0MDA4NzJkYjdhNTk3NmY3In0.mUXPqskwug3ko9NIrNK2aDFDs5YXkyQbkJf243eFc48\",\n    \"token_type\": \"bearer\",\n    \"expires_in\": 3600000,\n    \"user\": {\n        \"id\": 7,\n        \"profile_completed\": true\n    }\n}"}]}, {"name": "login", "event": [{"listen": "test", "script": {"exec": ["var jsonData = JSON.parse(responseBody);", "postman.setEnvironmentVariable(\"token\", jsonData.token);"], "type": "text/javascript"}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "email", "value": "admin@localhost", "type": "text"}, {"key": "password", "value": "Pass123?.", "type": "text"}]}, "url": {"raw": "{{base_url}}/login", "host": ["{{base_url}}"], "path": ["login"]}}, "response": [{"name": "login", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "email", "value": "admin@localhost", "type": "text"}, {"key": "password", "value": "Pass123?.", "type": "text"}]}, "url": {"raw": "{{base_url}}/login", "host": ["{{base_url}}"], "path": ["login"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "localhost:8000"}, {"key": "Date", "value": "<PERSON><PERSON>, 21 Jul 2020 10:54:00 GMT"}, {"key": "Date", "value": "<PERSON><PERSON>, 21 Jul 2020 10:54:00 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/7.4.8"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "ETag", "value": "\"fcfce0fcb8fc34d0e9e591d7dd3748cd7eaefb46\""}], "cookie": [], "body": "{\n    \"token\": \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOlwvXC9sb2NhbGhvc3Q6ODAwMFwvbG9naW4iLCJpYXQiOjE1OTUzMjg4NDAsImV4cCI6MTU5ODkyODg0MCwibmJmIjoxNTk1MzI4ODQwLCJqdGkiOiI3Q050SmdVVThERk5VS041Iiwic3ViIjoxLCJwcnYiOiIyM2JkNWM4OTQ5ZjYwMGFkYjM5ZTcwMWM0MDA4NzJkYjdhNTk3NmY3In0.ePMo2Kwf7wu6JWu9MdkaWEH8AyDzL3vCpTHZpdYX8WE\",\n    \"token_type\": \"bearer\",\n    \"expires_in\": 3600000,\n    \"user\": {\n        \"id\": 1,\n        \"profile_completed\": true\n    }\n}"}]}, {"name": "verify", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}], "url": {"raw": "{{base_url}}/verify/id", "host": ["{{base_url}}"], "path": ["verify", "id"]}}, "response": []}, {"name": "resend", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"email\":\"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/email/resend", "host": ["{{base_url}}"], "path": ["email", "resend"]}}, "response": []}, {"name": "forgot", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n\"email\":\"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/password/forgot", "host": ["{{base_url}}"], "path": ["password", "forgot"]}}, "response": []}, {"name": "reset", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    //\"email\": \"<EMAIL>\",\n    \"password\": \"Asd12345\",\n    \"password_confirmation\": \"Asd12345\",\n    //\"token\": \"b1ae5d00fc406eaac157a0ce1b95700c596e0fe2344e44ece9e017ea29d21a84\"\n}"}, "url": {"raw": "{{base_url}}/password/reset?token=28601dd239ab7e20e4a8cc3ce2e7f2e68f320207c288c507a83442215d6d0f78&email=akdeniztuncay44%40gmail.com", "host": ["{{base_url}}"], "path": ["password", "reset"], "query": [{"key": "token", "value": "28601dd239ab7e20e4a8cc3ce2e7f2e68f320207c288c507a83442215d6d0f78"}, {"key": "email", "value": "akdeniztuncay44%40gmail.com"}]}}, "response": []}]}, {"name": "admin", "item": [{"name": "user", "item": [{"name": "admin.user.get", "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/admin/user/1", "host": ["{{base_url}}"], "path": ["admin", "user", "1"]}}, "response": []}, {"name": "admin.user.list", "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/admin/user", "host": ["{{base_url}}"], "path": ["admin", "user"]}}, "response": []}, {"name": "admin.user.update", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n        \"first_name\": \"<PERSON>\",\n        \"last_name\": \"Admin\",\n        \"email\": \"admin@localhost\",\n        \"username\": \"admin\",\n        \"picture_id\": null,\n        \"role\": [\"superadmin\",\"DDK_Safety_Hardware\",\"DDK_Electrical_Safety\"],\n        \"details\": {\n            \"gender\": \"male\",\n            \"birthday\": \"1991-12-30\",\n            \"country\": null,\n            \"timezone\": \"UTC\",\n            \"description\": null,\n            \"language\": null\n        }\n    }"}, "url": {"raw": "{{base_url}}/admin/user/1", "host": ["{{base_url}}"], "path": ["admin", "user", "1"]}}, "response": []}, {"name": "admin.user.create", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n        \"first_name\": \"test\",\n        \"last_name\": \"user\",\n        \"email\": \"<EMAIL>\",\n        \"picture_id\": null,\n        \"role\": [\"user\"],\n        \"password\": \"secret\",\n        \"password_confirmation\": \"secret\",\n        \"details\": {\n            \"gender\": \"male\",\n            \"birthday\": \"1991-12-30\",\n            \"country\": null,\n            \"timezone\": \"UTC\",\n            \"description\": null,\n            \"language\": null\n        }\n}"}, "url": {"raw": "{{base_url}}/admin/user", "host": ["{{base_url}}"], "path": ["admin", "user"]}}, "response": []}, {"name": "admin.user.delete", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/admin/user/1", "host": ["{{base_url}}"], "path": ["admin", "user", "1"]}}, "response": []}]}, {"name": "settings", "item": [{"name": "admin.settings", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/settings", "host": ["{{base_url}}"], "path": ["admin", "settings"]}}, "response": []}, {"name": "admin.settings set", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"appointment_time\": 20,\n    \"appointment_slot_limit\": 3,\n    \"promotions\": [\n        \"Bileklik\",\n        \"Organizasyon Klavuzu\",\n        \"Sertifika\",\n        \"T-shirt\",\n        \"Çanta\",\n        \"Etiketler\"\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/admin/settings", "host": ["{{base_url}}"], "path": ["admin", "settings"]}}, "response": []}]}, {"name": "criteria", "item": [{"name": "criteria.get", "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/admin/criteria/1", "host": ["{{base_url}}"], "path": ["admin", "criteria", "1"]}}, "response": []}, {"name": "criteria.list", "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/admin/criteria?limit=200", "host": ["{{base_url}}"], "path": ["admin", "criteria"], "query": [{"key": "limit", "value": "200"}]}}, "response": [{"name": "promotion.list", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/promotion", "host": ["{{base_url}}"], "path": ["promotion"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "localhost:8000"}, {"key": "Date", "value": "Fri, 14 Aug 2020 13:08:26 GMT"}, {"key": "Date", "value": "Fri, 14 Aug 2020 13:08:26 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/7.4.8"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "ETag", "value": "\"264563af7a4293685981f535715e259b3e01b55a\""}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"data\": [\n        {\n            \"id\": 1,\n            \"name\": \"Book\",\n            \"team_id\": 1,\n            \"quantity\": null,\n            \"created_at\": \"2020-08-14T13:05:35.000000Z\",\n            \"updated_at\": \"2020-08-14T13:05:35.000000Z\"\n        },\n        {\n            \"id\": 2,\n            \"name\": \"Book\",\n            \"team_id\": 1,\n            \"quantity\": null,\n            \"created_at\": \"2020-08-14T13:05:44.000000Z\",\n            \"updated_at\": \"2020-08-14T13:05:44.000000Z\"\n        },\n        {\n            \"id\": 3,\n            \"name\": \"Book\",\n            \"team_id\": 1,\n            \"quantity\": null,\n            \"created_at\": \"2020-08-14T13:08:17.000000Z\",\n            \"updated_at\": \"2020-08-14T13:08:17.000000Z\"\n        },\n        {\n            \"id\": 4,\n            \"name\": \"Book\",\n            \"team_id\": 1,\n            \"quantity\": null,\n            \"created_at\": \"2020-08-14T13:08:20.000000Z\",\n            \"updated_at\": \"2020-08-14T13:08:20.000000Z\"\n        }\n    ],\n    \"meta\": {\n        \"pagination\": {\n            \"total\": 4,\n            \"count\": 4,\n            \"per_page\": 30,\n            \"current_page\": 1,\n            \"total_pages\": 1,\n            \"links\": {}\n        }\n    }\n}"}, {"name": "promotion.list.filtered", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/promotion?team_id=1", "host": ["{{base_url}}"], "path": ["promotion"], "query": [{"key": "team_id", "value": "1"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "localhost:8000"}, {"key": "Date", "value": "Fri, 14 Aug 2020 13:08:07 GMT"}, {"key": "Date", "value": "Fri, 14 Aug 2020 13:08:07 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/7.4.8"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "ETag", "value": "\"63de83e8ba9b11232c9e3a1bb543cf94c7470fba\""}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"data\": [\n        {\n            \"id\": 1,\n            \"name\": \"Book\",\n            \"team_id\": 1,\n            \"quantity\": null,\n            \"created_at\": \"2020-08-14T13:05:35.000000Z\",\n            \"updated_at\": \"2020-08-14T13:05:35.000000Z\"\n        },\n        {\n            \"id\": 2,\n            \"name\": \"Book\",\n            \"team_id\": 1,\n            \"quantity\": null,\n            \"created_at\": \"2020-08-14T13:05:44.000000Z\",\n            \"updated_at\": \"2020-08-14T13:05:44.000000Z\"\n        }\n    ],\n    \"meta\": {\n        \"pagination\": {\n            \"total\": 2,\n            \"count\": 2,\n            \"per_page\": 30,\n            \"current_page\": 1,\n            \"total_pages\": 1,\n            \"links\": {}\n        }\n    }\n}"}]}, {"name": "criteria.create", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"category\": \"Physical Specification\",\n  \"subject\": \"Vehicle height\",\n  \"rules\": [\n    {\n      \"content\": \"At least 1 m above ground level\",\n      \"isRequired\": true,\n      \"allowValue\": false\n    }\n  ],\n  \"type\" :null\n}"}, "url": {"raw": "{{base_url}}/admin/criteria", "host": ["{{base_url}}"], "path": ["admin", "criteria"]}}, "response": []}, {"name": "criteria.create.multi", "request": {"method": "PUT", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "[{\n  \"category\": \"Physical Specification\",\n  \"subject\": \"Vehicle height\",\n  \"rules\": [\n    {\n      \"content\": \"At least 1 m above ground level\",\n      \"isRequired\": true,\n      \"allowValue\": false\n    }\n  ],\n  \"type\" :null\n}]"}, "url": {"raw": "{{base_url}}/admin/criteria", "host": ["{{base_url}}"], "path": ["admin", "criteria"]}}, "response": []}, {"name": "criterai.update", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\" :1\n}"}, "url": {"raw": "{{base_url}}/admin/criteria/1", "host": ["{{base_url}}"], "path": ["admin", "criteria", "1"]}}, "response": []}, {"name": "criteria.delete", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/admin/criteria/1", "host": ["{{base_url}}"], "path": ["admin", "criteria", "1"]}}, "response": []}]}, {"name": "admin.clear", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/clear", "host": ["{{base_url}}"], "path": ["admin", "clear"]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}, {"name": "team", "item": [{"name": "team.get", "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/team/1", "host": ["{{base_url}}"], "path": ["team", "1"]}}, "response": [{"name": "team.get", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/team/1", "host": ["{{base_url}}"], "path": ["team", "1"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "localhost:8000"}, {"key": "Date", "value": "<PERSON><PERSON>, 11 Aug 2020 09:51:02 GMT"}, {"key": "Date", "value": "<PERSON><PERSON>, 11 Aug 2020 09:51:02 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/7.4.8"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "ETag", "value": "\"d162a4917d3cb3c4069422120742d11d71072344\""}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"data\": {\n        \"id\": 1,\n        \"team_name\": \"Yaman Team\",\n        \"university_name\": \"Adıyaman Üniversitesi\",\n        \"vehicle_name\": \"CENDERE\",\n        \"city_name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n        \"team_leader\": \"<PERSON> GÖKSU\",\n        \"team_leader_phone\": \"5415840200\",\n        \"team_leader_email\": \"<EMAIL>\",\n        \"consultant_name\": \"Assist. Prof. <PERSON>\",\n        \"consultant_phone\": \"5056260276\",\n        \"consultant_email\": \"<EMAIL>\",\n        \"curator_name\": \"ABUZER DOGAN\",\n        \"curator_phone\": \"5301838802\",\n        \"curator_email\": \"<EMAIL>\",\n        \"driver_name\": \"AHMET ÇAĞRI TEKİN\",\n        \"driver_phone\": \"\",\n        \"driver_email\": \"<EMAIL>\",\n        \"second_driver_name\": \"İBRAHİM HALİL SOYSAL\",\n        \"second_driver_phone\": \"5075978222\",\n        \"second_driver_email\": \"<EMAIL>\",\n        \"vehicle_category\": null,\n        \"vehicle_number\": 0,\n        \"team_member_count\": 0,\n        \"created_at\": \"2020-08-11T09:28:47.000000Z\",\n        \"updated_at\": \"2020-08-11T09:28:47.000000Z\",\n        \"files\": [\n            {\n                \"id\": 1,\n                \"asset_id\": 1,\n                \"type\": null,\n                \"mediable_type\": \"App\\\\Models\\\\Team\",\n                \"mediable_id\": 1,\n                \"created_at\": \"2020-08-11T09:28:58.000000Z\",\n                \"updated_at\": \"2020-08-11T09:28:58.000000Z\",\n                \"deleted_at\": null,\n                \"asset\": {\n                    \"id\": 1,\n                    \"user_id\": 1,\n                    \"type\": 2,\n                    \"path\": \"assets/2020/08/11/ef2d5532b267899466d677381033db05.xlsx\",\n                    \"thumbnail\": null\n                }\n            },\n            {\n                \"id\": 2,\n                \"asset_id\": 1,\n                \"type\": \"basvuru dilekcesi\",\n                \"mediable_type\": \"App\\\\Models\\\\Team\",\n                \"mediable_id\": 1,\n                \"created_at\": \"2020-08-11T09:36:34.000000Z\",\n                \"updated_at\": \"2020-08-11T09:36:34.000000Z\",\n                \"deleted_at\": null,\n                \"asset\": {\n                    \"id\": 1,\n                    \"user_id\": 1,\n                    \"type\": 2,\n                    \"path\": \"assets/2020/08/11/ef2d5532b267899466d677381033db05.xlsx\",\n                    \"thumbnail\": null\n                }\n            }\n        ]\n    }\n}"}]}, {"name": "team.list", "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/team", "host": ["{{base_url}}"], "path": ["team"], "query": [{"key": "sticker", "value": "0", "disabled": true}, {"key": "completed", "value": "1", "disabled": true}]}}, "response": [{"name": "team.list.sort", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/team?limit=2&page=1&sort[]=city_name", "host": ["{{base_url}}"], "path": ["team"], "query": [{"key": "limit", "value": "2"}, {"key": "page", "value": "1"}, {"key": "sort[]", "value": "city_name"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "localhost:8000"}, {"key": "Date", "value": "Thu, 13 Aug 2020 08:18:13 GMT"}, {"key": "Date", "value": "Thu, 13 Aug 2020 08:18:13 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/7.4.8"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "ETag", "value": "\"fc0e22e1e608210355e3165819c9c9488ec8f926\""}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"data\": [\n        {\n            \"id\": 63,\n            \"team_name\": \"BeeM\",\n            \"university_name\": \"Yozgat Bozok Üniversitesi\",\n            \"vehicle_name\": \"Nova\",\n            \"city_name\": \"Yoz<PERSON>\",\n            \"vehicle_number\": null,\n            \"team_member_count\": 0,\n            \"team_leader\": \"<PERSON><PERSON> ERYILMAZ\",\n            \"team_leader_phone\": \"5360738471\",\n            \"team_leader_email\": \"<EMAIL>\",\n            \"consultant_name\": \"Assist. Prof. <PERSON><PERSON>N\",\n            \"consultant_phone\": \"5326605758\",\n            \"consultant_email\": \"<EMAIL>\",\n            \"curator_name\": \"\",\n            \"curator_phone\": \"\",\n            \"curator_email\": \"\",\n            \"driver_name\": \"<PERSON><PERSON>\",\n            \"driver_phone\": \"\",\n            \"driver_email\": \"<EMAIL>\",\n            \"second_driver_name\": \"Enes SERTKAYA\",\n            \"second_driver_phone\": \"5549055201\",\n            \"second_driver_email\": \"<EMAIL>\",\n            \"vehicle_category\": null,\n            \"created_at\": \"2020-08-13T08:18:13.000000Z\",\n            \"updated_at\": \"2020-08-13T08:18:13.000000Z\"\n        },\n        {\n            \"id\": 38,\n            \"team_name\": \"Enerji Teknolojileri Topluluğu\",\n            \"university_name\": \"Karadeniz TeknikÜniversitesi\",\n            \"vehicle_name\": \"Sirius\",\n            \"city_name\": \"Trabzon\",\n            \"vehicle_number\": null,\n            \"team_member_count\": 0,\n            \"team_leader\": \"Ozan AĞCA\",\n            \"team_leader_phone\": \"5364501312\",\n            \"team_leader_email\": \"<EMAIL>\",\n            \"consultant_name\": \"Assoc. Prof. Yasin ALEMDAĞ\",\n            \"consultant_phone\": \"5327226795\",\n            \"consultant_email\": \"<EMAIL>\",\n            \"curator_name\": \"\",\n            \"curator_phone\": \"\",\n            \"curator_email\": \"\",\n            \"driver_name\": \"Ozan Ağca\",\n            \"driver_phone\": \"\",\n            \"driver_email\": \"<EMAIL>\",\n            \"second_driver_name\": \"\",\n            \"second_driver_phone\": \"\",\n            \"second_driver_email\": \"\",\n            \"vehicle_category\": null,\n            \"created_at\": \"2020-08-13T08:18:12.000000Z\",\n            \"updated_at\": \"2020-08-13T08:18:12.000000Z\"\n        }\n    ],\n    \"meta\": {\n        \"pagination\": {\n            \"total\": 63,\n            \"count\": 2,\n            \"per_page\": 2,\n            \"current_page\": 1,\n            \"total_pages\": 32,\n            \"links\": {\n                \"next\": \"http://localhost:8000/team?page=2\"\n            }\n        }\n    }\n}"}]}, {"name": "team.import", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file_name", "type": "file", "src": "/Users/<USER>/Downloads/EC_Devam Eden Takımlar Listesi_.xlsx"}]}, "url": {"raw": "{{base_url}}/team/import", "host": ["{{base_url}}"], "path": ["team", "import"]}}, "response": [{"name": "admin.app.list.eg1", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/admin/app", "host": ["{{base_url}}"], "path": ["admin", "app"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "localhost:8000"}, {"key": "Date", "value": "Sat, 29 Feb 2020 20:10:38 GMT"}, {"key": "Date", "value": "Sat, 29 Feb 2020 20:10:38 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/7.2.26"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "ETag", "value": "\"56e4ccb376d7e8ba56555df84a5bfc279934c179\""}], "cookie": [], "body": "{\n    \"data\": [\n        {\n            \"id\": 1,\n            \"name\": \"Test8\",\n            \"description\": null,\n            \"owner\": null,\n            \"bundle\": null,\n            \"extras\": null,\n            \"created_at\": \"2020-02-29 19:50:13\",\n            \"updated_at\": \"2020-02-29 19:50:13\",\n            \"logoUrl\": \"http://localhost:8000/storage/assets/2019/08/26/3524942cd12533e0d7f353edcefab187.jpg\"\n        },\n        {\n            \"id\": 2,\n            \"name\": \"string\",\n            \"description\": \"string\",\n            \"owner\": \"string\",\n            \"bundle\": \"string\",\n            \"extras\": \"string\",\n            \"created_at\": \"2020-02-29 20:09:22\",\n            \"updated_at\": \"2020-02-29 20:09:22\",\n            \"logoUrl\": \"http://localhost:8000/storage/assets/2019/08/26/3524942cd12533e0d7f353edcefab187.jpg\"\n        }\n    ]\n}"}]}, {"name": "team.export", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/team-export", "host": ["{{base_url}}"], "path": ["team-export"]}}, "response": [{"name": "admin.app.list.eg1", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/admin/app", "host": ["{{base_url}}"], "path": ["admin", "app"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "localhost:8000"}, {"key": "Date", "value": "Sat, 29 Feb 2020 20:10:38 GMT"}, {"key": "Date", "value": "Sat, 29 Feb 2020 20:10:38 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/7.2.26"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "ETag", "value": "\"56e4ccb376d7e8ba56555df84a5bfc279934c179\""}], "cookie": [], "body": "{\n    \"data\": [\n        {\n            \"id\": 1,\n            \"name\": \"Test8\",\n            \"description\": null,\n            \"owner\": null,\n            \"bundle\": null,\n            \"extras\": null,\n            \"created_at\": \"2020-02-29 19:50:13\",\n            \"updated_at\": \"2020-02-29 19:50:13\",\n            \"logoUrl\": \"http://localhost:8000/storage/assets/2019/08/26/3524942cd12533e0d7f353edcefab187.jpg\"\n        },\n        {\n            \"id\": 2,\n            \"name\": \"string\",\n            \"description\": \"string\",\n            \"owner\": \"string\",\n            \"bundle\": \"string\",\n            \"extras\": \"string\",\n            \"created_at\": \"2020-02-29 20:09:22\",\n            \"updated_at\": \"2020-02-29 20:09:22\",\n            \"logoUrl\": \"http://localhost:8000/storage/assets/2019/08/26/3524942cd12533e0d7f353edcefab187.jpg\"\n        }\n    ]\n}"}]}, {"name": "team.create", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"team_name\": \"Cukurova Electromobile Team 2\",\n  \"university_name\": \"Çukurova Üniversitesi\",\n  \"vehicle_name\": \"1,5 Adana\",\n  \"city_name\": \"Ada<PERSON>\",\n  \"team_leader\": \"<PERSON><PERSON> ÇIRAK\",\n  \"team_leader_phone\": \"5078467097\",\n  \"team_leader_email\": \"<EMAIL>\",\n  \"consultant_name\": \"Assoc. Prof. Mu<PERSON>\",\n  \"consultant_phone\": \"5356517110\",\n  \"consultant_email\": \"<EMAIL>\",\n  \"curator_name\": \"Halit ERİŞ\",\n  \"curator_phone\": \"5056283232\",\n  \"curator_email\": \"<EMAIL>\",\n  \"driver_name\": \"Umutcan OLMUŞ\",\n  \"driver_phone\": \"50550050550\",\n  \"driver_email\": \"<EMAIL>\",\n  \"second_driver_name\": \"<PERSON><PERSON> TAN\",\n  \"second_driver_phone\": \"5050173735\",\n  \"second_driver_email\": \"<EMAIL>\",\n  \"vehicle_category\": \"electric\",\n  \"vehicle_number\": 0,\n  \"team_member_count\": 0,\n  \"logo_id\" : 1\n}"}, "url": {"raw": "{{base_url}}/team", "host": ["{{base_url}}"], "path": ["team"]}}, "response": [{"name": "team.create", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"team_name\": \"Cukurova Electromobile Team 2\",\n  \"university_name\": \"Çukurova Üniversitesi\",\n  \"vehicle_name\": \"1,5 Adana\",\n  \"city_name\": \"Ada<PERSON>\",\n  \"team_leader\": \"<PERSON><PERSON> ÇIRAK\",\n  \"team_leader_phone\": \"5078467097\",\n  \"team_leader_email\": \"<EMAIL>\",\n  \"consultant_name\": \"Assoc. Prof. Mu<PERSON>\",\n  \"consultant_phone\": \"5356517110\",\n  \"consultant_email\": \"<EMAIL>\",\n  \"curator_name\": \"Halit ERİŞ\",\n  \"curator_phone\": \"5056283232\",\n  \"curator_email\": \"<EMAIL>\",\n  \"driver_name\": \"Umutcan OLMUŞ\",\n  \"driver_phone\": \"50550050550\",\n  \"driver_email\": \"<EMAIL>\",\n  \"second_driver_name\": \"<PERSON><PERSON> TAN\",\n  \"second_driver_phone\": \"5050173735\",\n  \"second_driver_email\": \"<EMAIL>\",\n  \"vehicle_category\": \"electric\",\n  \"vehicle_number\": 0,\n  \"team_member_count\": 0\n}"}, "url": {"raw": "{{base_url}}/team", "host": ["{{base_url}}"], "path": ["team"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "localhost:8000"}, {"key": "Date", "value": "<PERSON><PERSON>, 21 Jul 2020 11:06:22 GMT"}, {"key": "Date", "value": "<PERSON><PERSON>, 21 Jul 2020 11:06:22 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/7.4.8"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "ETag", "value": "\"666489b7ed5b2b2705d22db969b659d5c60d415e\""}], "cookie": [], "body": "{\n    \"data\": {\n        \"id\": 64,\n        \"team_name\": \"Cukurova Electromobile Team 2\",\n        \"university_name\": \"Çukurova Üniversitesi\",\n        \"vehicle_name\": \"1,5 Adana\",\n        \"city_name\": \"Adana\",\n        \"team_leader\": \"<PERSON><PERSON>\",\n        \"team_leader_phone\": \"5078467097\",\n        \"team_leader_email\": \"<EMAIL>\",\n        \"consultant_name\": \"Assoc. Prof. Mu<PERSON>\",\n        \"consultant_phone\": \"5356517110\",\n        \"consultant_email\": \"<EMAIL>\",\n        \"curator_name\": \"Halit ERİŞ\",\n        \"curator_phone\": \"5056283232\",\n        \"curator_email\": \"<EMAIL>\",\n        \"driver_name\": \"Umutcan OLMUŞ\",\n        \"driver_phone\": \"50550050550\",\n        \"driver_email\": \"<EMAIL>\",\n        \"second_driver_name\": \"<PERSON><PERSON>\",\n        \"second_driver_phone\": \"5050173735\",\n        \"second_driver_email\": \"<EMAIL>\",\n        \"vehicle_category\": \"electric\",\n        \"vehicle_number\": 0,\n        \"team_member_count\": 0,\n        \"created_at\": \"2020-07-21T11:06:22.000000Z\",\n        \"updated_at\": \"2020-07-21T11:06:22.000000Z\"\n    }\n}"}]}, {"name": "team.file.attach", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"asset_id\":1,\n  \"type\" : \"basvuru dilekcesi\"\n}"}, "url": {"raw": "{{base_url}}/team/1/attach", "host": ["{{base_url}}"], "path": ["team", "1", "attach"]}}, "response": [{"name": "team.create", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"team_name\": \"Cukurova Electromobile Team 2\",\n  \"university_name\": \"Çukurova Üniversitesi\",\n  \"vehicle_name\": \"1,5 Adana\",\n  \"city_name\": \"Ada<PERSON>\",\n  \"team_leader\": \"<PERSON><PERSON> ÇIRAK\",\n  \"team_leader_phone\": \"5078467097\",\n  \"team_leader_email\": \"<EMAIL>\",\n  \"consultant_name\": \"Assoc. Prof. Mu<PERSON>\",\n  \"consultant_phone\": \"5356517110\",\n  \"consultant_email\": \"<EMAIL>\",\n  \"curator_name\": \"Halit ERİŞ\",\n  \"curator_phone\": \"5056283232\",\n  \"curator_email\": \"<EMAIL>\",\n  \"driver_name\": \"Umutcan OLMUŞ\",\n  \"driver_phone\": \"50550050550\",\n  \"driver_email\": \"<EMAIL>\",\n  \"second_driver_name\": \"<PERSON><PERSON> TAN\",\n  \"second_driver_phone\": \"5050173735\",\n  \"second_driver_email\": \"<EMAIL>\",\n  \"vehicle_category\": \"electric\",\n  \"vehicle_number\": 0,\n  \"team_member_count\": 0\n}"}, "url": {"raw": "{{base_url}}/team", "host": ["{{base_url}}"], "path": ["team"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "localhost:8000"}, {"key": "Date", "value": "<PERSON><PERSON>, 21 Jul 2020 11:06:22 GMT"}, {"key": "Date", "value": "<PERSON><PERSON>, 21 Jul 2020 11:06:22 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/7.4.8"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "ETag", "value": "\"666489b7ed5b2b2705d22db969b659d5c60d415e\""}], "cookie": [], "body": "{\n    \"data\": {\n        \"id\": 64,\n        \"team_name\": \"Cukurova Electromobile Team 2\",\n        \"university_name\": \"Çukurova Üniversitesi\",\n        \"vehicle_name\": \"1,5 Adana\",\n        \"city_name\": \"Adana\",\n        \"team_leader\": \"<PERSON><PERSON>\",\n        \"team_leader_phone\": \"5078467097\",\n        \"team_leader_email\": \"<EMAIL>\",\n        \"consultant_name\": \"Assoc. Prof. Mu<PERSON>\",\n        \"consultant_phone\": \"5356517110\",\n        \"consultant_email\": \"<EMAIL>\",\n        \"curator_name\": \"Halit ERİŞ\",\n        \"curator_phone\": \"5056283232\",\n        \"curator_email\": \"<EMAIL>\",\n        \"driver_name\": \"Umutcan OLMUŞ\",\n        \"driver_phone\": \"50550050550\",\n        \"driver_email\": \"<EMAIL>\",\n        \"second_driver_name\": \"<PERSON><PERSON>\",\n        \"second_driver_phone\": \"5050173735\",\n        \"second_driver_email\": \"<EMAIL>\",\n        \"vehicle_category\": \"electric\",\n        \"vehicle_number\": 0,\n        \"team_member_count\": 0,\n        \"created_at\": \"2020-07-21T11:06:22.000000Z\",\n        \"updated_at\": \"2020-07-21T11:06:22.000000Z\"\n    }\n}"}]}, {"name": "team.approve", "request": {"method": "PUT", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{base_url}}/team/1/approve", "host": ["{{base_url}}"], "path": ["team", "1", "approve"]}}, "response": [{"name": "team.create", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"team_name\": \"Cukurova Electromobile Team 2\",\n  \"university_name\": \"Çukurova Üniversitesi\",\n  \"vehicle_name\": \"1,5 Adana\",\n  \"city_name\": \"Ada<PERSON>\",\n  \"team_leader\": \"<PERSON><PERSON> ÇIRAK\",\n  \"team_leader_phone\": \"5078467097\",\n  \"team_leader_email\": \"<EMAIL>\",\n  \"consultant_name\": \"Assoc. Prof. Mu<PERSON>\",\n  \"consultant_phone\": \"5356517110\",\n  \"consultant_email\": \"<EMAIL>\",\n  \"curator_name\": \"Halit ERİŞ\",\n  \"curator_phone\": \"5056283232\",\n  \"curator_email\": \"<EMAIL>\",\n  \"driver_name\": \"Umutcan OLMUŞ\",\n  \"driver_phone\": \"50550050550\",\n  \"driver_email\": \"<EMAIL>\",\n  \"second_driver_name\": \"<PERSON><PERSON> TAN\",\n  \"second_driver_phone\": \"5050173735\",\n  \"second_driver_email\": \"<EMAIL>\",\n  \"vehicle_category\": \"electric\",\n  \"vehicle_number\": 0,\n  \"team_member_count\": 0\n}"}, "url": {"raw": "{{base_url}}/team", "host": ["{{base_url}}"], "path": ["team"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "localhost:8000"}, {"key": "Date", "value": "<PERSON><PERSON>, 21 Jul 2020 11:06:22 GMT"}, {"key": "Date", "value": "<PERSON><PERSON>, 21 Jul 2020 11:06:22 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/7.4.8"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "ETag", "value": "\"666489b7ed5b2b2705d22db969b659d5c60d415e\""}], "cookie": [], "body": "{\n    \"data\": {\n        \"id\": 64,\n        \"team_name\": \"Cukurova Electromobile Team 2\",\n        \"university_name\": \"Çukurova Üniversitesi\",\n        \"vehicle_name\": \"1,5 Adana\",\n        \"city_name\": \"Adana\",\n        \"team_leader\": \"<PERSON><PERSON>\",\n        \"team_leader_phone\": \"5078467097\",\n        \"team_leader_email\": \"<EMAIL>\",\n        \"consultant_name\": \"Assoc. Prof. Mu<PERSON>\",\n        \"consultant_phone\": \"5356517110\",\n        \"consultant_email\": \"<EMAIL>\",\n        \"curator_name\": \"Halit ERİŞ\",\n        \"curator_phone\": \"5056283232\",\n        \"curator_email\": \"<EMAIL>\",\n        \"driver_name\": \"Umutcan OLMUŞ\",\n        \"driver_phone\": \"50550050550\",\n        \"driver_email\": \"<EMAIL>\",\n        \"second_driver_name\": \"<PERSON><PERSON>\",\n        \"second_driver_phone\": \"5050173735\",\n        \"second_driver_email\": \"<EMAIL>\",\n        \"vehicle_category\": \"electric\",\n        \"vehicle_number\": 0,\n        \"team_member_count\": 0,\n        \"created_at\": \"2020-07-21T11:06:22.000000Z\",\n        \"updated_at\": \"2020-07-21T11:06:22.000000Z\"\n    }\n}"}]}, {"name": "team.approve.all.teams", "request": {"method": "PUT", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/approve-all-teams", "host": ["{{base_url}}"], "path": ["approve-all-teams"]}}, "response": [{"name": "team.create", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"team_name\": \"Cukurova Electromobile Team 2\",\n  \"university_name\": \"Çukurova Üniversitesi\",\n  \"vehicle_name\": \"1,5 Adana\",\n  \"city_name\": \"Ada<PERSON>\",\n  \"team_leader\": \"<PERSON><PERSON> ÇIRAK\",\n  \"team_leader_phone\": \"5078467097\",\n  \"team_leader_email\": \"<EMAIL>\",\n  \"consultant_name\": \"Assoc. Prof. Mu<PERSON>\",\n  \"consultant_phone\": \"5356517110\",\n  \"consultant_email\": \"<EMAIL>\",\n  \"curator_name\": \"Halit ERİŞ\",\n  \"curator_phone\": \"5056283232\",\n  \"curator_email\": \"<EMAIL>\",\n  \"driver_name\": \"Umutcan OLMUŞ\",\n  \"driver_phone\": \"50550050550\",\n  \"driver_email\": \"<EMAIL>\",\n  \"second_driver_name\": \"<PERSON><PERSON> TAN\",\n  \"second_driver_phone\": \"5050173735\",\n  \"second_driver_email\": \"<EMAIL>\",\n  \"vehicle_category\": \"electric\",\n  \"vehicle_number\": 0,\n  \"team_member_count\": 0\n}"}, "url": {"raw": "{{base_url}}/team", "host": ["{{base_url}}"], "path": ["team"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "localhost:8000"}, {"key": "Date", "value": "<PERSON><PERSON>, 21 Jul 2020 11:06:22 GMT"}, {"key": "Date", "value": "<PERSON><PERSON>, 21 Jul 2020 11:06:22 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/7.4.8"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "ETag", "value": "\"666489b7ed5b2b2705d22db969b659d5c60d415e\""}], "cookie": [], "body": "{\n    \"data\": {\n        \"id\": 64,\n        \"team_name\": \"Cukurova Electromobile Team 2\",\n        \"university_name\": \"Çukurova Üniversitesi\",\n        \"vehicle_name\": \"1,5 Adana\",\n        \"city_name\": \"Adana\",\n        \"team_leader\": \"<PERSON><PERSON>\",\n        \"team_leader_phone\": \"5078467097\",\n        \"team_leader_email\": \"<EMAIL>\",\n        \"consultant_name\": \"Assoc. Prof. Mu<PERSON>\",\n        \"consultant_phone\": \"5356517110\",\n        \"consultant_email\": \"<EMAIL>\",\n        \"curator_name\": \"Halit ERİŞ\",\n        \"curator_phone\": \"5056283232\",\n        \"curator_email\": \"<EMAIL>\",\n        \"driver_name\": \"Umutcan OLMUŞ\",\n        \"driver_phone\": \"50550050550\",\n        \"driver_email\": \"<EMAIL>\",\n        \"second_driver_name\": \"<PERSON><PERSON>\",\n        \"second_driver_phone\": \"5050173735\",\n        \"second_driver_email\": \"<EMAIL>\",\n        \"vehicle_category\": \"electric\",\n        \"vehicle_number\": 0,\n        \"team_member_count\": 0,\n        \"created_at\": \"2020-07-21T11:06:22.000000Z\",\n        \"updated_at\": \"2020-07-21T11:06:22.000000Z\"\n    }\n}"}]}, {"name": "team.update", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"vehicle_number\" :10,\n  \"driver_phone\" : \"545454545454\",\n  \"vehicle_category\" :\"electromobile\"\n}"}, "url": {"raw": "{{base_url}}/team/1", "host": ["{{base_url}}"], "path": ["team", "1"]}}, "response": [{"name": "team.update", "originalRequest": {"method": "PATCH", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"team_name\": \"Cukurova Electromobile Team\",\n  \"university_name\": \"Çukurova Üniversitesi\",\n  \"vehicle_name\": \"1,5 Adana\",\n  \"city_name\": \"Adana\",\n  \"team_leader\": \"<PERSON><PERSON> ÇIR<PERSON>K\",\n  \"team_leader_phone\": \"5078467097\",\n  \"team_leader_email\": \"<EMAIL>\",\n  \"consultant_name\": \"Assoc. Prof. Mu<PERSON>\",\n  \"consultant_phone\": \"5356517110\",\n  \"consultant_email\": \"<EMAIL>\",\n  \"curator_name\": \"Halit ERİŞ\",\n  \"curator_phone\": \"5056283232\",\n  \"curator_email\": \"<EMAIL>\",\n  \"driver_name\": \"Umutcan OLMUŞ\",\n  \"driver_phone\": \"\",\n  \"driver_email\": \"<EMAIL>\",\n  \"second_driver_name\": \"<PERSON><PERSON>\",\n  \"second_driver_phone\": \"5050173735\",\n  \"second_driver_email\": \"<EMAIL>\",\n  \"vehicle_category\": null,\n  \"vehicle_number\": 0,\n  \"team_member_count\": 0\n}"}, "url": {"raw": "{{base_url}}/team/12", "host": ["{{base_url}}"], "path": ["team", "12"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "localhost:8000"}, {"key": "Date", "value": "<PERSON><PERSON>, 21 Jul 2020 11:03:55 GMT"}, {"key": "Date", "value": "<PERSON><PERSON>, 21 Jul 2020 11:03:55 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/7.4.8"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "ETag", "value": "\"b6fe7752920e56e8e3f9792742f96dc2d1d11cae\""}], "cookie": [], "body": "{\n    \"data\": {\n        \"id\": 12,\n        \"team_name\": \"Cukurova Electromobile Team\",\n        \"university_name\": \"Çukurova Üniversitesi\",\n        \"vehicle_name\": \"1,5 Adana\",\n        \"city_name\": \"Adana\",\n        \"team_leader\": \"<PERSON><PERSON>\",\n        \"team_leader_phone\": \"5078467097\",\n        \"team_leader_email\": \"<EMAIL>\",\n        \"consultant_name\": \"Assoc. Prof. Murat <PERSON>\",\n        \"consultant_phone\": \"5356517110\",\n        \"consultant_email\": \"<EMAIL>\",\n        \"curator_name\": \"Halit ERİŞ\",\n        \"curator_phone\": \"5056283232\",\n        \"curator_email\": \"<EMAIL>\",\n        \"driver_name\": \"Umutcan OLMUŞ\",\n        \"driver_phone\": \"\",\n        \"driver_email\": \"<EMAIL>\",\n        \"second_driver_name\": \"<PERSON><PERSON> T<PERSON>\",\n        \"second_driver_phone\": \"5050173735\",\n        \"second_driver_email\": \"<EMAIL>\",\n        \"vehicle_category\": null,\n        \"vehicle_number\": 0,\n        \"team_member_count\": 0,\n        \"created_at\": \"2020-07-21T11:03:04.000000Z\",\n        \"updated_at\": \"2020-07-21T11:03:04.000000Z\"\n    }\n}"}]}, {"name": "team.delete", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/team/1", "host": ["{{base_url}}"], "path": ["team", "1"]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}, {"name": "appointment", "item": [{"name": "appointment.get", "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/appointment/1", "host": ["{{base_url}}"], "path": ["appointment", "1"]}}, "response": []}, {"name": "appointment.list", "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/appointment?limit=100", "host": ["{{base_url}}"], "path": ["appointment"], "query": [{"key": "limit", "value": "100"}]}}, "response": []}, {"name": "appointment.create", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"team_id\" : 4,\n  \"start_time\": \"2020-07-08 12:00\",\n  \"comments\": \"tst\"\n }"}, "url": {"raw": "{{base_url}}/appointment", "host": ["{{base_url}}"], "path": ["appointment"]}}, "response": []}, {"name": "appointment.create.auto", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"start_time\": \"2020-07-19 12:00\"\n}"}, "url": {"raw": "{{base_url}}/appointment/auto", "host": ["{{base_url}}"], "path": ["appointment", "auto"]}}, "response": []}, {"name": "appointment.update", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\":\"joined\",\n  \"comments\": \"tst\"\n}"}, "url": {"raw": "{{base_url}}/appointment/1", "host": ["{{base_url}}"], "path": ["appointment", "1"]}}, "response": []}, {"name": "appointment.join/notjoin", "request": {"method": "PUT", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"joined\" : true\n}"}, "url": {"raw": "{{base_url}}/appointment/1/join", "host": ["{{base_url}}"], "path": ["appointment", "1", "join"]}}, "response": []}, {"name": "appointment.delete", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/appointment/1", "host": ["{{base_url}}"], "path": ["appointment", "1"]}}, "response": []}]}, {"name": "evaluation", "item": [{"name": "eval.get", "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/evaluation/1", "host": ["{{base_url}}"], "path": ["evaluation", "1"]}}, "response": []}, {"name": "eval.list", "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/evaluation", "host": ["{{base_url}}"], "path": ["evaluation"]}}, "response": []}, {"name": "eval.create", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"team_id\": 2,\n  \"eval_date\" : \"2020-07-20\"\n}"}, "url": {"raw": "{{base_url}}/evaluation", "host": ["{{base_url}}"], "path": ["evaluation"]}}, "response": []}, {"name": "eval.update", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"team_id\": 1,\n  \"eval_date\" : \"2020-07-20\",\n  \"status\":\"active\"\n}"}, "url": {"raw": "{{base_url}}/evaluation/1", "host": ["{{base_url}}"], "path": ["evaluation", "1"]}}, "response": []}, {"name": "eval.reopen", "request": {"method": "PUT", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{base_url}}/evaluation/1/reopen", "host": ["{{base_url}}"], "path": ["evaluation", "1", "reopen"]}}, "response": []}, {"name": "eval.close", "request": {"method": "PUT", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{base_url}}/evaluation/1/close", "host": ["{{base_url}}"], "path": ["evaluation", "1", "close"]}}, "response": []}, {"name": "eval.check-sticker", "request": {"method": "PUT", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{base_url}}/evaluation/1/check", "host": ["{{base_url}}"], "path": ["evaluation", "1", "check"]}}, "response": [{"name": "eval.check-sticker", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{base_url}}/evaluation/1/check", "host": ["{{base_url}}"], "path": ["evaluation", "1", "check"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "localhost:8000"}, {"key": "Date", "value": "Sun, 23 Aug 2020 09:58:12 GMT"}, {"key": "Date", "value": "Sun, 23 Aug 2020 09:58:12 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/7.4.8"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "ETag", "value": "\"1e24fcf81badc30736d2589dc7b87a48c47c87b9\""}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"passed\": false,\n    \"required\": [\n        {\n            \"id\": 6,\n            \"evaluation_id\": 1,\n            \"team_id\": 1,\n            \"category\": \"Physical Specification\",\n            \"subject\": \"Vehicle body\",\n            \"condition\": \"From the top view: no open regions, the wheels inside the body\",\n            \"value\": null,\n            \"allow_value\": 0,\n            \"required\": 1,\n            \"compatibility\": null,\n            \"penalty\": null,\n            \"notes\": null,\n            \"updated_by\": null,\n            \"created_at\": null,\n            \"updated_at\": null\n        },\n        {\n            \"id\": 7,\n            \"evaluation_id\": 1,\n            \"team_id\": 1,\n            \"category\": \"Physical Specification\",\n            \"subject\": \"Vehicle body\",\n            \"condition\": \"Fragile windows / sharp ends / protruding edges etc.\",\n            \"value\": null,\n            \"allow_value\": 0,\n            \"required\": 1,\n            \"compatibility\": null,\n            \"penalty\": null,\n            \"notes\": null,\n            \"updated_by\": null,\n            \"created_at\": null,\n            \"updated_at\": null\n        },\n        {\n            \"id\": 8,\n            \"evaluation_id\": 1,\n            \"team_id\": 1,\n            \"category\": \"Physical Specification\",\n            \"subject\": \"Door\",\n            \"condition\": \"50 cm × 80 cm frame can pass through the door\",\n            \"value\": null,\n            \"allow_value\": 0,\n            \"required\": 1,\n            \"compatibility\": null,\n            \"penalty\": null,\n            \"notes\": null,\n            \"updated_by\": null,\n            \"created_at\": null,\n            \"updated_at\": null\n        },\n        {\n            \"id\": 9,\n            \"evaluation_id\": 1,\n            \"team_id\": 1,\n            \"category\": \"Physical Specification\",\n            \"subject\": \"Door mechanism\",\n            \"condition\": \"Fixed to the body with a safe connecting element\",\n            \"value\": null,\n            \"allow_value\": 0,\n            \"required\": 1,\n            \"compatibility\": null,\n            \"penalty\": null,\n            \"notes\": null,\n            \"updated_by\": null,\n            \"created_at\": null,\n            \"updated_at\": null\n        },\n        {\n            \"id\": 10,\n            \"evaluation_id\": 1,\n            \"team_id\": 1,\n            \"category\": \"Physical Specification\",\n            \"subject\": \"Door mechanism\",\n            \"condition\": \"Door mechanism can be closed without any manual intervention\",\n            \"value\": null,\n            \"allow_value\": 0,\n            \"required\": 1,\n            \"compatibility\": null,\n            \"penalty\": null,\n            \"notes\": null,\n            \"updated_by\": null,\n            \"created_at\": null,\n            \"updated_at\": null\n        },\n        {\n            \"id\": 11,\n            \"evaluation_id\": 1,\n            \"team_id\": 1,\n            \"category\": \"Physical Specification\",\n            \"subject\": \"Door mechanism\",\n            \"condition\": \"Can be opened from outside/no possibility of unintended opening\",\n            \"value\": null,\n            \"allow_value\": 0,\n            \"required\": 1,\n            \"compatibility\": null,\n            \"penalty\": null,\n            \"notes\": null,\n            \"updated_by\": null,\n            \"created_at\": null,\n            \"updated_at\": null\n        },\n        {\n            \"id\": 12,\n            \"evaluation_id\": 1,\n            \"team_id\": 1,\n            \"category\": \"Physical Specification\",\n            \"subject\": \"Wheel width\",\n            \"condition\": \"A minimum of 70 mm\",\n            \"value\": null,\n            \"allow_value\": 1,\n            \"required\": 1,\n            \"compatibility\": null,\n            \"penalty\": null,\n            \"notes\": null,\n            \"updated_by\": null,\n            \"created_at\": null,\n            \"updated_at\": null\n        },\n        {\n            \"id\": 13,\n            \"evaluation_id\": 1,\n            \"team_id\": 1,\n            \"category\": \"Physical Specification\",\n            \"subject\": \"Flag\",\n            \"condition\": \"A minimum of 20 × 30 cm\",\n            \"value\": null,\n            \"allow_value\": 0,\n            \"required\": 1,\n            \"compatibility\": null,\n            \"penalty\": null,\n            \"notes\": null,\n            \"updated_by\": null,\n            \"created_at\": null,\n            \"updated_at\": null\n        }\n    ]\n}"}]}, {"name": "eval.sticker", "request": {"method": "PUT", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{base_url}}/evaluation/1/sticker", "host": ["{{base_url}}"], "path": ["evaluation", "1", "sticker"]}}, "response": [{"name": "eval.sticker", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{base_url}}/evaluation/1/sticker", "host": ["{{base_url}}"], "path": ["evaluation", "1", "sticker"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "localhost:8000"}, {"key": "Date", "value": "Sun, 23 Aug 2020 09:56:16 GMT"}, {"key": "Date", "value": "Sun, 23 Aug 2020 09:56:16 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/7.4.8"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "ETag", "value": "\"5aab9c347013c29d4ccdffb1a8b74bc1151729e6\""}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"check\": {\n        \"passed\": false,\n        \"required\": [\n            {\n                \"id\": 6,\n                \"evaluation_id\": 1,\n                \"team_id\": 1,\n                \"category\": \"Physical Specification\",\n                \"subject\": \"Vehicle body\",\n                \"condition\": \"From the top view: no open regions, the wheels inside the body\",\n                \"value\": null,\n                \"allow_value\": 0,\n                \"required\": 1,\n                \"compatibility\": null,\n                \"penalty\": null,\n                \"notes\": null,\n                \"updated_by\": null,\n                \"created_at\": null,\n                \"updated_at\": null\n            },\n            {\n                \"id\": 7,\n                \"evaluation_id\": 1,\n                \"team_id\": 1,\n                \"category\": \"Physical Specification\",\n                \"subject\": \"Vehicle body\",\n                \"condition\": \"Fragile windows / sharp ends / protruding edges etc.\",\n                \"value\": null,\n                \"allow_value\": 0,\n                \"required\": 1,\n                \"compatibility\": null,\n                \"penalty\": null,\n                \"notes\": null,\n                \"updated_by\": null,\n                \"created_at\": null,\n                \"updated_at\": null\n            },\n            {\n                \"id\": 8,\n                \"evaluation_id\": 1,\n                \"team_id\": 1,\n                \"category\": \"Physical Specification\",\n                \"subject\": \"Door\",\n                \"condition\": \"50 cm × 80 cm frame can pass through the door\",\n                \"value\": null,\n                \"allow_value\": 0,\n                \"required\": 1,\n                \"compatibility\": null,\n                \"penalty\": null,\n                \"notes\": null,\n                \"updated_by\": null,\n                \"created_at\": null,\n                \"updated_at\": null\n            },\n            {\n                \"id\": 9,\n                \"evaluation_id\": 1,\n                \"team_id\": 1,\n                \"category\": \"Physical Specification\",\n                \"subject\": \"Door mechanism\",\n                \"condition\": \"Fixed to the body with a safe connecting element\",\n                \"value\": null,\n                \"allow_value\": 0,\n                \"required\": 1,\n                \"compatibility\": null,\n                \"penalty\": null,\n                \"notes\": null,\n                \"updated_by\": null,\n                \"created_at\": null,\n                \"updated_at\": null\n            },\n            {\n                \"id\": 10,\n                \"evaluation_id\": 1,\n                \"team_id\": 1,\n                \"category\": \"Physical Specification\",\n                \"subject\": \"Door mechanism\",\n                \"condition\": \"Door mechanism can be closed without any manual intervention\",\n                \"value\": null,\n                \"allow_value\": 0,\n                \"required\": 1,\n                \"compatibility\": null,\n                \"penalty\": null,\n                \"notes\": null,\n                \"updated_by\": null,\n                \"created_at\": null,\n                \"updated_at\": null\n            },\n            {\n                \"id\": 11,\n                \"evaluation_id\": 1,\n                \"team_id\": 1,\n                \"category\": \"Physical Specification\",\n                \"subject\": \"Door mechanism\",\n                \"condition\": \"Can be opened from outside/no possibility of unintended opening\",\n                \"value\": null,\n                \"allow_value\": 0,\n                \"required\": 1,\n                \"compatibility\": null,\n                \"penalty\": null,\n                \"notes\": null,\n                \"updated_by\": null,\n                \"created_at\": null,\n                \"updated_at\": null\n            },\n            {\n                \"id\": 12,\n                \"evaluation_id\": 1,\n                \"team_id\": 1,\n                \"category\": \"Physical Specification\",\n                \"subject\": \"Wheel width\",\n                \"condition\": \"A minimum of 70 mm\",\n                \"value\": null,\n                \"allow_value\": 1,\n                \"required\": 1,\n                \"compatibility\": null,\n                \"penalty\": null,\n                \"notes\": null,\n                \"updated_by\": null,\n                \"created_at\": null,\n                \"updated_at\": null\n            },\n            {\n                \"id\": 13,\n                \"evaluation_id\": 1,\n                \"team_id\": 1,\n                \"category\": \"Physical Specification\",\n                \"subject\": \"Flag\",\n                \"condition\": \"A minimum of 20 × 30 cm\",\n                \"value\": null,\n                \"allow_value\": 0,\n                \"required\": 1,\n                \"compatibility\": null,\n                \"penalty\": null,\n                \"notes\": null,\n                \"updated_by\": null,\n                \"created_at\": null,\n                \"updated_at\": null\n            }\n        ]\n    }\n}"}]}, {"name": "eval.export", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{base_url}}/evaluation/1/export", "host": ["{{base_url}}"], "path": ["evaluation", "1", "export"]}}, "response": [{"name": "eval.sticker", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{base_url}}/evaluation/1/sticker", "host": ["{{base_url}}"], "path": ["evaluation", "1", "sticker"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "localhost:8000"}, {"key": "Date", "value": "Sun, 23 Aug 2020 09:56:16 GMT"}, {"key": "Date", "value": "Sun, 23 Aug 2020 09:56:16 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/7.4.8"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "ETag", "value": "\"5aab9c347013c29d4ccdffb1a8b74bc1151729e6\""}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"check\": {\n        \"passed\": false,\n        \"required\": [\n            {\n                \"id\": 6,\n                \"evaluation_id\": 1,\n                \"team_id\": 1,\n                \"category\": \"Physical Specification\",\n                \"subject\": \"Vehicle body\",\n                \"condition\": \"From the top view: no open regions, the wheels inside the body\",\n                \"value\": null,\n                \"allow_value\": 0,\n                \"required\": 1,\n                \"compatibility\": null,\n                \"penalty\": null,\n                \"notes\": null,\n                \"updated_by\": null,\n                \"created_at\": null,\n                \"updated_at\": null\n            },\n            {\n                \"id\": 7,\n                \"evaluation_id\": 1,\n                \"team_id\": 1,\n                \"category\": \"Physical Specification\",\n                \"subject\": \"Vehicle body\",\n                \"condition\": \"Fragile windows / sharp ends / protruding edges etc.\",\n                \"value\": null,\n                \"allow_value\": 0,\n                \"required\": 1,\n                \"compatibility\": null,\n                \"penalty\": null,\n                \"notes\": null,\n                \"updated_by\": null,\n                \"created_at\": null,\n                \"updated_at\": null\n            },\n            {\n                \"id\": 8,\n                \"evaluation_id\": 1,\n                \"team_id\": 1,\n                \"category\": \"Physical Specification\",\n                \"subject\": \"Door\",\n                \"condition\": \"50 cm × 80 cm frame can pass through the door\",\n                \"value\": null,\n                \"allow_value\": 0,\n                \"required\": 1,\n                \"compatibility\": null,\n                \"penalty\": null,\n                \"notes\": null,\n                \"updated_by\": null,\n                \"created_at\": null,\n                \"updated_at\": null\n            },\n            {\n                \"id\": 9,\n                \"evaluation_id\": 1,\n                \"team_id\": 1,\n                \"category\": \"Physical Specification\",\n                \"subject\": \"Door mechanism\",\n                \"condition\": \"Fixed to the body with a safe connecting element\",\n                \"value\": null,\n                \"allow_value\": 0,\n                \"required\": 1,\n                \"compatibility\": null,\n                \"penalty\": null,\n                \"notes\": null,\n                \"updated_by\": null,\n                \"created_at\": null,\n                \"updated_at\": null\n            },\n            {\n                \"id\": 10,\n                \"evaluation_id\": 1,\n                \"team_id\": 1,\n                \"category\": \"Physical Specification\",\n                \"subject\": \"Door mechanism\",\n                \"condition\": \"Door mechanism can be closed without any manual intervention\",\n                \"value\": null,\n                \"allow_value\": 0,\n                \"required\": 1,\n                \"compatibility\": null,\n                \"penalty\": null,\n                \"notes\": null,\n                \"updated_by\": null,\n                \"created_at\": null,\n                \"updated_at\": null\n            },\n            {\n                \"id\": 11,\n                \"evaluation_id\": 1,\n                \"team_id\": 1,\n                \"category\": \"Physical Specification\",\n                \"subject\": \"Door mechanism\",\n                \"condition\": \"Can be opened from outside/no possibility of unintended opening\",\n                \"value\": null,\n                \"allow_value\": 0,\n                \"required\": 1,\n                \"compatibility\": null,\n                \"penalty\": null,\n                \"notes\": null,\n                \"updated_by\": null,\n                \"created_at\": null,\n                \"updated_at\": null\n            },\n            {\n                \"id\": 12,\n                \"evaluation_id\": 1,\n                \"team_id\": 1,\n                \"category\": \"Physical Specification\",\n                \"subject\": \"Wheel width\",\n                \"condition\": \"A minimum of 70 mm\",\n                \"value\": null,\n                \"allow_value\": 1,\n                \"required\": 1,\n                \"compatibility\": null,\n                \"penalty\": null,\n                \"notes\": null,\n                \"updated_by\": null,\n                \"created_at\": null,\n                \"updated_at\": null\n            },\n            {\n                \"id\": 13,\n                \"evaluation_id\": 1,\n                \"team_id\": 1,\n                \"category\": \"Physical Specification\",\n                \"subject\": \"Flag\",\n                \"condition\": \"A minimum of 20 × 30 cm\",\n                \"value\": null,\n                \"allow_value\": 0,\n                \"required\": 1,\n                \"compatibility\": null,\n                \"penalty\": null,\n                \"notes\": null,\n                \"updated_by\": null,\n                \"created_at\": null,\n                \"updated_at\": null\n            }\n        ]\n    }\n}"}]}, {"name": "eval.delete", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/evall/1", "host": ["{{base_url}}"], "path": ["evall", "1"]}}, "response": []}, {"name": "eval.detail.update", "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\n    \"value\": 20,\n    \"compatibility\": 1,\n    \"notes\":\"notes here\",\n    \"penalty\":20\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/evaluation-detail/1", "host": ["{{base_url}}"], "path": ["evaluation-detail", "1"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOlwvXC9hcGkudHViaXRhay5yYXN0bW9iaWxlLmNvbVwvbG9naW4iLCJpYXQiOjE1OTg1MzE0NTYsImV4cCI6MTYwMjEzMTQ1NiwibmJmIjoxNTk4NTMxNDU2LCJqdGkiOiJYakk5ZXVjSkhoQWVLZklJIiwic3ViIjoxNSwicHJ2IjoiMjNiZDVjODk0OWY2MDBhZGIzOWU3MDFjNDAwODcyZGI3YTU5NzZmNyJ9.odATL7E4FvBDwGXj9nUxNkpcnkMOYmh3RG1hxcFtGX0", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}, {"name": "race", "item": [{"name": "race.get", "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/race/1", "host": ["{{base_url}}"], "path": ["race", "1"]}}, "response": [{"name": "race.get", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/race/1", "host": ["{{base_url}}"], "path": ["race", "1"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "localhost:8000"}, {"key": "Date", "value": "Thu, 23 Jul 2020 09:53:24 GMT"}, {"key": "Date", "value": "Thu, 23 Jul 2020 09:53:24 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/7.4.8"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "ETag", "value": "\"953b4363b9fa7bbc6ef0b1c1b4c984f48ca30c9b\""}], "cookie": [], "body": "{\n    \"data\": {\n        \"id\": 1,\n        \"name\": \"first race\",\n        \"description\": \"first race\",\n        \"type\": \"electromobile\",\n        \"start_date\": \"2020-07-23\",\n        \"created_at\": \"2020-07-23T09:52:21.000000Z\",\n        \"updated_at\": \"2020-07-23T09:52:59.000000Z\",\n        \"sessions\": []\n    }\n}"}]}, {"name": "race.list", "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/race", "host": ["{{base_url}}"], "path": ["race"]}}, "response": [{"name": "race.list", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/race", "host": ["{{base_url}}"], "path": ["race"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "localhost:8000"}, {"key": "Date", "value": "Thu, 23 Jul 2020 09:53:47 GMT"}, {"key": "Date", "value": "Thu, 23 Jul 2020 09:53:47 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/7.4.8"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "ETag", "value": "\"24b211a4869f9b3e875dfdf16fcdbe55810ce9bc\""}], "cookie": [], "body": "{\n    \"data\": [\n        {\n            \"id\": 1,\n            \"name\": \"first race\",\n            \"description\": \"first race\",\n            \"type\": \"electromobile\",\n            \"start_date\": \"2020-07-23\",\n            \"created_at\": \"2020-07-23T09:52:21.000000Z\",\n            \"updated_at\": \"2020-07-23T09:52:59.000000Z\"\n        },\n        {\n            \"id\": 2,\n            \"name\": \"first race\",\n            \"description\": \"first race\",\n            \"type\": \"electromobile\",\n            \"start_date\": \"2020-07-23\",\n            \"created_at\": \"2020-07-23T09:52:47.000000Z\",\n            \"updated_at\": \"2020-07-23T09:52:47.000000Z\"\n        }\n    ],\n    \"meta\": {\n        \"pagination\": {\n            \"total\": 2,\n            \"count\": 2,\n            \"per_page\": 30,\n            \"current_page\": 1,\n            \"total_pages\": 1,\n            \"links\": {}\n        }\n    }\n}"}]}, {"name": "race.create", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\" : \"first race\",\n  \"description\" : \"first race\",\n  \"start_date\" : \"2020-07-23\"\n}"}, "url": {"raw": "{{base_url}}/race", "host": ["{{base_url}}"], "path": ["race"]}}, "response": [{"name": "race.create", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\" : \"first race\",\n  \"description\" : \"first race\",\n  \"type\" : \"electromobile\",\n  \"start_date\" : \"2020-07-23\"\n}"}, "url": {"raw": "{{base_url}}/race", "host": ["{{base_url}}"], "path": ["race"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "localhost:8000"}, {"key": "Date", "value": "Thu, 23 Jul 2020 09:52:47 GMT"}, {"key": "Date", "value": "Thu, 23 Jul 2020 09:52:47 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/7.4.8"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "ETag", "value": "\"8b85d6a3da9c7ee297cf14319b84e3252499a8b0\""}], "cookie": [], "body": "{\n    \"data\": {\n        \"id\": 2,\n        \"name\": \"first race\",\n        \"description\": \"first race\",\n        \"type\": \"electromobile\",\n        \"start_date\": \"2020-07-23\",\n        \"created_at\": \"2020-07-23T09:52:47.000000Z\",\n        \"updated_at\": \"2020-07-23T09:52:47.000000Z\",\n        \"sessions\": []\n    }\n}"}]}, {"name": "race.update", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\" : \"first race\",\n  \"description\" : \"first race\",\n  \"start_date\" : \"2020-07-23\"\n}"}, "url": {"raw": "{{base_url}}/race/1", "host": ["{{base_url}}"], "path": ["race", "1"]}}, "response": [{"name": "race.update", "originalRequest": {"method": "PATCH", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\" : \"first race\",\n  \"description\" : \"first race\",\n  \"type\" : \"electromobile\",\n  \"start_date\" : \"2020-07-23\"\n}"}, "url": {"raw": "{{base_url}}/race/1", "host": ["{{base_url}}"], "path": ["race", "1"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "localhost:8000"}, {"key": "Date", "value": "Thu, 23 Jul 2020 09:52:59 GMT"}, {"key": "Date", "value": "Thu, 23 Jul 2020 09:52:59 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/7.4.8"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "ETag", "value": "\"953b4363b9fa7bbc6ef0b1c1b4c984f48ca30c9b\""}], "cookie": [], "body": "{\n    \"data\": {\n        \"id\": 1,\n        \"name\": \"first race\",\n        \"description\": \"first race\",\n        \"type\": \"electromobile\",\n        \"start_date\": \"2020-07-23\",\n        \"created_at\": \"2020-07-23T09:52:21.000000Z\",\n        \"updated_at\": \"2020-07-23T09:52:59.000000Z\",\n        \"sessions\": []\n    }\n}"}]}, {"name": "race.end", "request": {"method": "PUT", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{base_url}}/race/1/end", "host": ["{{base_url}}"], "path": ["race", "1", "end"]}}, "response": [{"name": "race.update", "originalRequest": {"method": "PATCH", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\" : \"first race\",\n  \"description\" : \"first race\",\n  \"type\" : \"electromobile\",\n  \"start_date\" : \"2020-07-23\"\n}"}, "url": {"raw": "{{base_url}}/race/1", "host": ["{{base_url}}"], "path": ["race", "1"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "localhost:8000"}, {"key": "Date", "value": "Thu, 23 Jul 2020 09:52:59 GMT"}, {"key": "Date", "value": "Thu, 23 Jul 2020 09:52:59 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/7.4.8"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "ETag", "value": "\"953b4363b9fa7bbc6ef0b1c1b4c984f48ca30c9b\""}], "cookie": [], "body": "{\n    \"data\": {\n        \"id\": 1,\n        \"name\": \"first race\",\n        \"description\": \"first race\",\n        \"type\": \"electromobile\",\n        \"start_date\": \"2020-07-23\",\n        \"created_at\": \"2020-07-23T09:52:21.000000Z\",\n        \"updated_at\": \"2020-07-23T09:52:59.000000Z\",\n        \"sessions\": []\n    }\n}"}]}, {"name": "race.delete", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/race/1", "host": ["{{base_url}}"], "path": ["race", "1"]}}, "response": []}]}, {"name": "race-session", "item": [{"name": "race-session.get", "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/race-session/1", "host": ["{{base_url}}"], "path": ["race-session", "1"]}}, "response": []}, {"name": "race-session.list", "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/race-session", "host": ["{{base_url}}"], "path": ["race-session"]}}, "response": []}, {"name": "race-session.create", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\" : \"first sesssion\",\n  \"race_id\" : 1,\n  \"type\" : \"electromobile\",\n  \"description\" : \"first race\",\n  \"start_time\" : \"2020-07-23 12:00\"\n}"}, "url": {"raw": "{{base_url}}/race-session", "host": ["{{base_url}}"], "path": ["race-session"]}}, "response": []}, {"name": "race-session.add-team", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"teams\" :[1,2,3]\n}"}, "url": {"raw": "{{base_url}}/race-session/1/add-team", "host": ["{{base_url}}"], "path": ["race-session", "1", "add-team"]}}, "response": []}, {"name": "race-session.export-session-team", "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/race-session/1/export-session-team", "host": ["{{base_url}}"], "path": ["race-session", "1", "export-session-team"]}}, "response": []}, {"name": "race-session.add-all-team", "request": {"method": "PUT", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/race-session/1/add-all-teams", "host": ["{{base_url}}"], "path": ["race-session", "1", "add-all-teams"]}}, "response": []}, {"name": "race-session.update", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\" : \"first sessiodn\",\n  \"race_id\" : 1,\n  \"type\" : \"electromobile\",\n  \"description\" : \"first race\",\n  \"start_time\" : \"2020-07-23 12:00\"\n}"}, "url": {"raw": "{{base_url}}/race-session/1", "host": ["{{base_url}}"], "path": ["race-session", "1"]}}, "response": []}, {"name": "race-session.delete", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/race-session/1", "host": ["{{base_url}}"], "path": ["race-session", "1"]}}, "response": []}]}, {"name": "session-team", "item": [{"name": "session-team.get", "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/session-team/1", "host": ["{{base_url}}"], "path": ["session-team", "1"]}}, "response": []}, {"name": "export-session-team", "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/export-session-team/1", "host": ["{{base_url}}"], "path": ["export-session-team", "1"]}}, "response": []}, {"name": "session-team.list", "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/session-team", "host": ["{{base_url}}"], "path": ["session-team"]}}, "response": []}, {"name": "session-team.create", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"team_id\": 4,\n    \"session_id\": 2,\n    \"start_point\": 11,\n    \"laps\": 30,\n    \"valid_laps\": 30,\n    \"initial_energy_cons\": 0,\n    \"last_energy_cons\": 731,\n    \"initial_hydrogen_cons\": 0,\n    \"last_hydrogen_cons\": 0,\n    \"penalty\": -50,\n    \"final\": 0,\n    \"score\": 0\n}"}, "url": {"raw": "{{base_url}}/session-team", "host": ["{{base_url}}"], "path": ["session-team"]}}, "response": []}, {"name": "session-team.update", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"start_point\": 11,\n    \"laps\": 30,\n    \"valid_laps\": 30,\n    \"initial_energy_cons\": 3,\n    \"last_energy_cons\": 454,\n    \"initial_hydrogen_cons\": 874.8,\n    \"last_hydrogen_cons\": 1019,\n    \"penalty\": -50,\n    \"final\": 0,\n    \"score\": 0,\n    \"status\":\"DSQ\",\n    \"race_time\" : \"00:20:01\"\n}"}, "url": {"raw": "{{base_url}}/session-team/1", "host": ["{{base_url}}"], "path": ["session-team", "1"]}}, "response": [{"name": "session-team.update", "originalRequest": {"method": "PATCH", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"start_point\": 11,\n    \"laps\": 31,\n    \"valid_laps\": 30,\n    \"initial_energy_cons\": 0,\n    \"last_energy_cons\": 731,\n    \"initial_hydrogen_cons\": 0,\n    \"last_hydrogen_cons\": 0,\n    \"penalty\": -50,\n    \"final\": 0,\n    \"score\": 0\n}"}, "url": {"raw": "{{base_url}}/session-team/2", "host": ["{{base_url}}"], "path": ["session-team", "2"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "localhost:8000"}, {"key": "Date", "value": "Sun, 16 Aug 2020 20:33:37 GMT"}, {"key": "Date", "value": "Sun, 16 Aug 2020 20:33:37 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/7.4.8"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "ETag", "value": "\"a6095d889b25be420ef86975138603babe553ef2\""}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"data\": {\n        \"id\": 2,\n        \"team_id\": 2,\n        \"session_id\": 1,\n        \"start_point\": \"11\",\n        \"laps\": 31,\n        \"valid_laps\": 30,\n        \"initial_energy_cons\": 0,\n        \"last_energy_cons\": 731,\n        \"total_cons\": 731,\n        \"initial_hydrogen_cons\": 0,\n        \"last_hydrogen_cons\": 0,\n        \"total_hydrogen_cons\": 0,\n        \"penalty\": -50,\n        \"final\": 681,\n        \"score\": 3819,\n        \"created_at\": \"2020-08-16T20:11:01.000000Z\",\n        \"updated_at\": \"2020-08-16T20:33:37.000000Z\"\n    }\n}"}]}, {"name": "session-team.delete", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/session-team/1", "host": ["{{base_url}}"], "path": ["session-team", "1"]}}, "response": []}]}, {"name": "promotion", "item": [{"name": "promotion.get", "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/promotion/1", "host": ["{{base_url}}"], "path": ["promotion", "1"]}}, "response": []}, {"name": "promotion.list", "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/promotion", "host": ["{{base_url}}"], "path": ["promotion"]}}, "response": [{"name": "promotion.list.filtered", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/promotion?team_id=1", "host": ["{{base_url}}"], "path": ["promotion"], "query": [{"key": "team_id", "value": "1"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "localhost:8000"}, {"key": "Date", "value": "Fri, 14 Aug 2020 13:08:07 GMT"}, {"key": "Date", "value": "Fri, 14 Aug 2020 13:08:07 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/7.4.8"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "ETag", "value": "\"63de83e8ba9b11232c9e3a1bb543cf94c7470fba\""}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"data\": [\n        {\n            \"id\": 1,\n            \"name\": \"Book\",\n            \"team_id\": 1,\n            \"quantity\": null,\n            \"created_at\": \"2020-08-14T13:05:35.000000Z\",\n            \"updated_at\": \"2020-08-14T13:05:35.000000Z\"\n        },\n        {\n            \"id\": 2,\n            \"name\": \"Book\",\n            \"team_id\": 1,\n            \"quantity\": null,\n            \"created_at\": \"2020-08-14T13:05:44.000000Z\",\n            \"updated_at\": \"2020-08-14T13:05:44.000000Z\"\n        }\n    ],\n    \"meta\": {\n        \"pagination\": {\n            \"total\": 2,\n            \"count\": 2,\n            \"per_page\": 30,\n            \"current_page\": 1,\n            \"total_pages\": 1,\n            \"links\": {}\n        }\n    }\n}"}, {"name": "promotion.list", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/promotion", "host": ["{{base_url}}"], "path": ["promotion"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "localhost:8000"}, {"key": "Date", "value": "Fri, 14 Aug 2020 13:08:26 GMT"}, {"key": "Date", "value": "Fri, 14 Aug 2020 13:08:26 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/7.4.8"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "ETag", "value": "\"264563af7a4293685981f535715e259b3e01b55a\""}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"data\": [\n        {\n            \"id\": 1,\n            \"name\": \"Book\",\n            \"team_id\": 1,\n            \"quantity\": null,\n            \"created_at\": \"2020-08-14T13:05:35.000000Z\",\n            \"updated_at\": \"2020-08-14T13:05:35.000000Z\"\n        },\n        {\n            \"id\": 2,\n            \"name\": \"Book\",\n            \"team_id\": 1,\n            \"quantity\": null,\n            \"created_at\": \"2020-08-14T13:05:44.000000Z\",\n            \"updated_at\": \"2020-08-14T13:05:44.000000Z\"\n        },\n        {\n            \"id\": 3,\n            \"name\": \"Book\",\n            \"team_id\": 1,\n            \"quantity\": null,\n            \"created_at\": \"2020-08-14T13:08:17.000000Z\",\n            \"updated_at\": \"2020-08-14T13:08:17.000000Z\"\n        },\n        {\n            \"id\": 4,\n            \"name\": \"Book\",\n            \"team_id\": 1,\n            \"quantity\": null,\n            \"created_at\": \"2020-08-14T13:08:20.000000Z\",\n            \"updated_at\": \"2020-08-14T13:08:20.000000Z\"\n        }\n    ],\n    \"meta\": {\n        \"pagination\": {\n            \"total\": 4,\n            \"count\": 4,\n            \"per_page\": 30,\n            \"current_page\": 1,\n            \"total_pages\": 1,\n            \"links\": {}\n        }\n    }\n}"}]}, {"name": "promotion.export", "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/promotion-export/{teamId}", "host": ["{{base_url}}"], "path": ["promotion-export", "{teamId}"]}}, "response": [{"name": "promotion.list.filtered", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/promotion?team_id=1", "host": ["{{base_url}}"], "path": ["promotion"], "query": [{"key": "team_id", "value": "1"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "localhost:8000"}, {"key": "Date", "value": "Fri, 14 Aug 2020 13:08:07 GMT"}, {"key": "Date", "value": "Fri, 14 Aug 2020 13:08:07 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/7.4.8"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "ETag", "value": "\"63de83e8ba9b11232c9e3a1bb543cf94c7470fba\""}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"data\": [\n        {\n            \"id\": 1,\n            \"name\": \"Book\",\n            \"team_id\": 1,\n            \"quantity\": null,\n            \"created_at\": \"2020-08-14T13:05:35.000000Z\",\n            \"updated_at\": \"2020-08-14T13:05:35.000000Z\"\n        },\n        {\n            \"id\": 2,\n            \"name\": \"Book\",\n            \"team_id\": 1,\n            \"quantity\": null,\n            \"created_at\": \"2020-08-14T13:05:44.000000Z\",\n            \"updated_at\": \"2020-08-14T13:05:44.000000Z\"\n        }\n    ],\n    \"meta\": {\n        \"pagination\": {\n            \"total\": 2,\n            \"count\": 2,\n            \"per_page\": 30,\n            \"current_page\": 1,\n            \"total_pages\": 1,\n            \"links\": {}\n        }\n    }\n}"}, {"name": "promotion.list", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/promotion", "host": ["{{base_url}}"], "path": ["promotion"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "localhost:8000"}, {"key": "Date", "value": "Fri, 14 Aug 2020 13:08:26 GMT"}, {"key": "Date", "value": "Fri, 14 Aug 2020 13:08:26 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/7.4.8"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "ETag", "value": "\"264563af7a4293685981f535715e259b3e01b55a\""}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"data\": [\n        {\n            \"id\": 1,\n            \"name\": \"Book\",\n            \"team_id\": 1,\n            \"quantity\": null,\n            \"created_at\": \"2020-08-14T13:05:35.000000Z\",\n            \"updated_at\": \"2020-08-14T13:05:35.000000Z\"\n        },\n        {\n            \"id\": 2,\n            \"name\": \"Book\",\n            \"team_id\": 1,\n            \"quantity\": null,\n            \"created_at\": \"2020-08-14T13:05:44.000000Z\",\n            \"updated_at\": \"2020-08-14T13:05:44.000000Z\"\n        },\n        {\n            \"id\": 3,\n            \"name\": \"Book\",\n            \"team_id\": 1,\n            \"quantity\": null,\n            \"created_at\": \"2020-08-14T13:08:17.000000Z\",\n            \"updated_at\": \"2020-08-14T13:08:17.000000Z\"\n        },\n        {\n            \"id\": 4,\n            \"name\": \"Book\",\n            \"team_id\": 1,\n            \"quantity\": null,\n            \"created_at\": \"2020-08-14T13:08:20.000000Z\",\n            \"updated_at\": \"2020-08-14T13:08:20.000000Z\"\n        }\n    ],\n    \"meta\": {\n        \"pagination\": {\n            \"total\": 4,\n            \"count\": 4,\n            \"per_page\": 30,\n            \"current_page\": 1,\n            \"total_pages\": 1,\n            \"links\": {}\n        }\n    }\n}"}]}, {"name": "promotion.export.all", "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/promotion-export-all", "host": ["{{base_url}}"], "path": ["promotion-export-all"]}}, "response": [{"name": "promotion.list.filtered", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/promotion?team_id=1", "host": ["{{base_url}}"], "path": ["promotion"], "query": [{"key": "team_id", "value": "1"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "localhost:8000"}, {"key": "Date", "value": "Fri, 14 Aug 2020 13:08:07 GMT"}, {"key": "Date", "value": "Fri, 14 Aug 2020 13:08:07 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/7.4.8"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "ETag", "value": "\"63de83e8ba9b11232c9e3a1bb543cf94c7470fba\""}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"data\": [\n        {\n            \"id\": 1,\n            \"name\": \"Book\",\n            \"team_id\": 1,\n            \"quantity\": null,\n            \"created_at\": \"2020-08-14T13:05:35.000000Z\",\n            \"updated_at\": \"2020-08-14T13:05:35.000000Z\"\n        },\n        {\n            \"id\": 2,\n            \"name\": \"Book\",\n            \"team_id\": 1,\n            \"quantity\": null,\n            \"created_at\": \"2020-08-14T13:05:44.000000Z\",\n            \"updated_at\": \"2020-08-14T13:05:44.000000Z\"\n        }\n    ],\n    \"meta\": {\n        \"pagination\": {\n            \"total\": 2,\n            \"count\": 2,\n            \"per_page\": 30,\n            \"current_page\": 1,\n            \"total_pages\": 1,\n            \"links\": {}\n        }\n    }\n}"}, {"name": "promotion.list", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/promotion", "host": ["{{base_url}}"], "path": ["promotion"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "localhost:8000"}, {"key": "Date", "value": "Fri, 14 Aug 2020 13:08:26 GMT"}, {"key": "Date", "value": "Fri, 14 Aug 2020 13:08:26 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/7.4.8"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "ETag", "value": "\"264563af7a4293685981f535715e259b3e01b55a\""}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"data\": [\n        {\n            \"id\": 1,\n            \"name\": \"Book\",\n            \"team_id\": 1,\n            \"quantity\": null,\n            \"created_at\": \"2020-08-14T13:05:35.000000Z\",\n            \"updated_at\": \"2020-08-14T13:05:35.000000Z\"\n        },\n        {\n            \"id\": 2,\n            \"name\": \"Book\",\n            \"team_id\": 1,\n            \"quantity\": null,\n            \"created_at\": \"2020-08-14T13:05:44.000000Z\",\n            \"updated_at\": \"2020-08-14T13:05:44.000000Z\"\n        },\n        {\n            \"id\": 3,\n            \"name\": \"Book\",\n            \"team_id\": 1,\n            \"quantity\": null,\n            \"created_at\": \"2020-08-14T13:08:17.000000Z\",\n            \"updated_at\": \"2020-08-14T13:08:17.000000Z\"\n        },\n        {\n            \"id\": 4,\n            \"name\": \"Book\",\n            \"team_id\": 1,\n            \"quantity\": null,\n            \"created_at\": \"2020-08-14T13:08:20.000000Z\",\n            \"updated_at\": \"2020-08-14T13:08:20.000000Z\"\n        }\n    ],\n    \"meta\": {\n        \"pagination\": {\n            \"total\": 4,\n            \"count\": 4,\n            \"per_page\": 30,\n            \"current_page\": 1,\n            \"total_pages\": 1,\n            \"links\": {}\n        }\n    }\n}"}]}, {"name": "promotion.create", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Book\",\n  \"team_id\" :1,\n  \"quantity\" : 2\n}"}, "url": {"raw": "{{base_url}}/promotion", "host": ["{{base_url}}"], "path": ["promotion"]}}, "response": []}, {"name": "promotion.create.multi", "request": {"method": "PUT", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\":[\"asd\",\"sd\"]\n} "}, "url": {"raw": "{{base_url}}/promotion", "host": ["{{base_url}}"], "path": ["promotion"]}}, "response": []}, {"name": "promotion.update", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Book\",\n  \"team_id\" :1\n}"}, "url": {"raw": "{{base_url}}/promotion/1", "host": ["{{base_url}}"], "path": ["promotion", "1"]}}, "response": []}, {"name": "promotion.delete", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/promotion/1", "host": ["{{base_url}}"], "path": ["promotion", "1"]}}, "response": []}]}, {"name": "pean<PERSON>y", "item": [{"name": "penalty.get", "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/penalty/1", "host": ["{{base_url}}"], "path": ["penalty", "1"]}}, "response": []}, {"name": "penalty.list", "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/penalty", "host": ["{{base_url}}"], "path": ["penalty"]}}, "response": []}, {"name": "penalty.create", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"team_id\": 1,\n    \"session_id\": 1,\n    \"subject\": \"Race Time Penalty\",\n    \"violation\": \"seat belt violation\",\n    \"penalty\": 1,\n    \"type\": \"after_race\",\n    \"conclusion\": \"test\"\n}"}, "url": {"raw": "{{base_url}}/penalty", "host": ["{{base_url}}"], "path": ["penalty"]}}, "response": []}, {"name": "penalty.update", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n\n\"subject\":\"test\",\n\"violation\":\"test\",\n\"penalty\":1000,\n\"type\":\"before_race\",\n\"conclusion\":\"test\"\n}"}, "url": {"raw": "{{base_url}}/penalty/1", "host": ["{{base_url}}"], "path": ["penalty", "1"]}}, "response": []}, {"name": "penalty.delete", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/penalty/1", "host": ["{{base_url}}"], "path": ["penalty", "1"]}}, "response": []}, {"name": "penalty.export", "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/penalty-team-export/1", "host": ["{{base_url}}"], "path": ["penalty-team-export", "1"]}}, "response": []}, {"name": "penalty.export.all", "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/penalty-export", "host": ["{{base_url}}"], "path": ["penalty-export"]}}, "response": []}]}, {"name": "Member", "item": [{"name": "member.get", "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/member/1", "host": ["{{base_url}}"], "path": ["member", "1"]}}, "response": []}, {"name": "member.list", "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/member", "host": ["{{base_url}}"], "path": ["member"]}}, "response": []}, {"name": "member.create", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"team_id\": 1,\n  \"role_in_team\": \"driver\",\n  \"identity_number\": \"151784579236\",\n  \"phone_number\": \"5455554466\",\n  \"birthday\": \"1990-01-01\",\n  \"gender\": \"male\",\n  \"in_area\": true,\n  \"parent_name\": \"<PERSON>\",\n  \"parent_phone\": \"39845983475\",\n  \"uniform_size\": \"M\",\n  \"hes_code\": \"L2G4-1123-4\"\n}"}, "url": {"raw": "{{base_url}}/member", "host": ["{{base_url}}"], "path": ["member"]}}, "response": []}, {"name": "member.update", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n\n\"subject\":\"test\",\n\"violation\":\"test\",\n\"penalty\":1000,\n\"type\":\"before_race\",\n\"conclusion\":\"test\"\n}"}, "url": {"raw": "{{base_url}}/member/1", "host": ["{{base_url}}"], "path": ["member", "1"]}}, "response": []}, {"name": "member.delete", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/member/1", "host": ["{{base_url}}"], "path": ["member", "1"]}}, "response": []}, {"name": "member.export", "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/member-team-export/1", "host": ["{{base_url}}"], "path": ["member-team-export", "1"]}}, "response": []}]}, {"name": "technical-design", "item": [{"name": "technical-design.get", "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/technical-design/1", "host": ["{{base_url}}"], "path": ["technical-design", "1"]}}, "response": []}, {"name": "technical-design.list", "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/technical-design", "host": ["{{base_url}}"], "path": ["technical-design"]}}, "response": []}, {"name": "technical-design.import", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file_name", "type": "file", "src": "/Users/<USER>/Downloads/TTR Özet_son (1) (1).xlsm"}]}, "url": {"raw": "{{base_url}}/technical-design-import", "host": ["{{base_url}}"], "path": ["technical-design-import"]}}, "response": []}, {"name": "technical-design.export", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/technical-design-export", "host": ["{{base_url}}"], "path": ["technical-design-export"]}}, "response": []}, {"name": "technical-design.create", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"no\": 1,\n    \"arac_no\": 546611,\n    \"universite\": \"Düzce Üniversitesi\",\n    \"takim_adi\": \"LODOS ELECTROMOBİLE TEAM\",\n    \"toplam\": 570,\n    \"toplam_kontrol\": 0,\n    \"arac_ozellikleri_tablosu\": 100,\n    \"dinamik_surus_testi\": 100,\n    \"motor\": 30,\n    \"motor_surucusu\": 0,\n    \"batarya_yonetim_sistemi\": 110,\n    \"yerlesik_sarj_birimi\": 120,\n    \"enerji_yonetim_sistemi\": null,\n    \"batarya_paketlemesi\": 0,\n    \"arac_kontrol_sistemi\": 0,\n    \"elektronik_diferansiyel_uygulamasi\": 0,\n    \"izolasyon_izleme_cihazi\": null,\n    \"direksiyon_sistemi\": 0,\n    \"kapi_mekanizmasi\": 0,\n    \"yakit_pili\": null,\n    \"yakit_pili_kontrol_sistemi\": null,\n    \"mekanik_detaylar\": 85,\n    \"elektrik_semasi\": 25,\n    \"orijinal_tasarim\": 0,\n    \"kriter_aciklama\": \"User kısmı bir paragraf yazılmış ancak tasarım detayları yok\",\n    \"geri_bildirim\": null\n}"}, "url": {"raw": "{{base_url}}/technical-design", "host": ["{{base_url}}"], "path": ["technical-design"]}}, "response": []}, {"name": "technical-design.update", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"no\": 1,\n    \"arac_no\": 546611,\n    \"universite\": \"Düzce Üniversitesi\",\n    \"takim_adi\": \"LODOS ELECTROMOBİLE TEAM\",\n    \"toplam\": 570,\n    \"toplam_kontrol\": 0,\n    \"arac_ozellikleri_tablosu\": 100,\n    \"dinamik_surus_testi\": 100,\n    \"motor\": 30,\n    \"motor_surucusu\": 0,\n    \"batarya_yonetim_sistemi\": 110,\n    \"yerlesik_sarj_birimi\": 120,\n    \"enerji_yonetim_sistemi\": null,\n    \"batarya_paketlemesi\": 0,\n    \"arac_kontrol_sistemi\": 0,\n    \"elektronik_diferansiyel_uygulamasi\": 0,\n    \"izolasyon_izleme_cihazi\": null,\n    \"direksiyon_sistemi\": 0,\n    \"kapi_mekanizmasi\": 0,\n    \"yakit_pili\": null,\n    \"yakit_pili_kontrol_sistemi\": null,\n    \"mekanik_detaylar\": 85,\n    \"elektrik_semasi\": 25,\n    \"orijinal_tasarim\": 0,\n    \"kriter_aciklama\": \"User kısmı bir paragraf yazılmış ancak tasarım detayları yok\",\n    \"geri_bildirim\": null\n}"}, "url": {"raw": "{{base_url}}/technical-design/1", "host": ["{{base_url}}"], "path": ["technical-design", "1"]}}, "response": []}, {"name": "technical-design.delete", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/technical-design/1", "host": ["{{base_url}}"], "path": ["technical-design", "1"]}}, "response": []}]}, {"name": "team Copy Copy", "item": [{"name": "team.get", "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/team/1", "host": ["{{base_url}}"], "path": ["team", "1"]}}, "response": []}, {"name": "team.list", "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/team?limit=20&page=1&sort[]=city_name:asc", "host": ["{{base_url}}"], "path": ["team"], "query": [{"key": "limit", "value": "20"}, {"key": "page", "value": "1"}, {"key": "sort[]", "value": "city_name:asc"}]}}, "response": []}, {"name": "team.import", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file_name", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/team/import", "host": ["{{base_url}}"], "path": ["team", "import"]}}, "response": []}, {"name": "team.create", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"team_name\": \"Cukurova Electromobile Team\",\n  \"university_name\": \"Çukurova Üniversitesi\",\n  \"vehicle_name\": \"1,5 Adana\",\n  \"city_name\": \"Adana\",\n  \"team_leader\": \"<PERSON><PERSON> ÇIR<PERSON>K\",\n  \"team_leader_phone\": \"5078467097\",\n  \"team_leader_email\": \"<EMAIL>\",\n  \"consultant_name\": \"Assoc. Prof. Mu<PERSON>\",\n  \"consultant_phone\": \"5356517110\",\n  \"consultant_email\": \"<EMAIL>\",\n  \"curator_name\": \"Halit ERİŞ\",\n  \"curator_phone\": \"5056283232\",\n  \"curator_email\": \"<EMAIL>\",\n  \"driver_name\": \"Umutcan OLMUŞ\",\n  \"driver_phone\": \"\",\n  \"driver_email\": \"<EMAIL>\",\n  \"second_driver_name\": \"<PERSON><PERSON>\",\n  \"second_driver_phone\": \"5050173735\",\n  \"second_driver_email\": \"<EMAIL>\",\n  \"vehicle_category\": null,\n  \"vehicle_number\": 0,\n  \"team_member_count\": 0\n}"}, "url": {"raw": "{{base_url}}/team", "host": ["{{base_url}}"], "path": ["team"]}}, "response": []}, {"name": "team.update", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"team_name\": \"Cukurova Electromobile Team\",\n  \"university_name\": \"Çukurova Üniversitesi\",\n  \"vehicle_name\": \"1,5 Adana\",\n  \"city_name\": \"Adana\",\n  \"team_leader\": \"<PERSON><PERSON> ÇIR<PERSON>K\",\n  \"team_leader_phone\": \"5078467097\",\n  \"team_leader_email\": \"<EMAIL>\",\n  \"consultant_name\": \"Assoc. Prof. Mu<PERSON>\",\n  \"consultant_phone\": \"5356517110\",\n  \"consultant_email\": \"<EMAIL>\",\n  \"curator_name\": \"Halit ERİŞ\",\n  \"curator_phone\": \"5056283232\",\n  \"curator_email\": \"<EMAIL>\",\n  \"driver_name\": \"Umutcan OLMUŞ\",\n  \"driver_phone\": \"50550050550\",\n  \"driver_email\": \"<EMAIL>\",\n  \"second_driver_name\": \"<PERSON><PERSON> T<PERSON>\",\n  \"second_driver_phone\": \"5050173735\",\n  \"second_driver_email\": \"<EMAIL>\",\n  \"vehicle_category\": \"electric\",\n  \"vehicle_number\": 0,\n  \"team_member_count\": 0\n}"}, "url": {"raw": "{{base_url}}/team/12", "host": ["{{base_url}}"], "path": ["team", "12"]}}, "response": []}, {"name": "team.delete", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/team/1", "host": ["{{base_url}}"], "path": ["team", "1"]}}, "response": []}]}, {"name": "system-files", "item": [{"name": "system-files.list", "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/system-files", "host": ["{{base_url}}"], "path": ["system-files"]}}, "response": []}, {"name": "system-files.create", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"asset_id\" : 1,\n  \"type\" : \"test file\"\n}"}, "url": {"raw": "{{base_url}}/system-files", "host": ["{{base_url}}"], "path": ["system-files"]}}, "response": []}, {"name": "system-files.delete", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "url": {"raw": "{{base_url}}/team/1", "host": ["{{base_url}}"], "path": ["team", "1"]}}, "response": []}]}, {"name": "asset.upload", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "/Users/<USER>/Downloads/screencapture-localhost-2678-campaign-mhex-2020-08-12-15_30_51.png"}]}, "url": {"raw": "{{base_url}}/asset", "host": ["{{base_url}}"], "path": ["asset"]}}, "response": []}, {"name": "import", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file_name", "type": "file", "src": "/Users/<USER>/Downloads/TTR Özet_son (1) (2).xlsm"}]}, "url": {"raw": "{{base_url}}/import", "host": ["{{base_url}}"], "path": ["import"]}}, "response": []}, {"name": "stats", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/stats", "host": ["{{base_url}}"], "path": ["stats"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}